<?php
// 认证目录保护脚本
// 禁止任何访问

http_response_code(404);
header('Content-Type: text/html; charset=UTF-8');

// 记录访问尝试
$log_entry = date('Y-m-d H:i:s') . " - 非法访问尝试: " . $_SERVER['REQUEST_URI'] . " - IP: " . $_SERVER['REMOTE_ADDR'] . "\n";
file_put_contents(__DIR__ . '/../../runtime/logs/security.log', $log_entry, FILE_APPEND | LOCK_EX);

// 返回404页面
?>
<!DOCTYPE html>
<html>
<head>
    <title>404 Not Found</title>
</head>
<body>
    <h1>404 Not Found</h1>
    <p>The requested resource was not found on this server.</p>
</body>
</html>
<?php
exit;
?>
