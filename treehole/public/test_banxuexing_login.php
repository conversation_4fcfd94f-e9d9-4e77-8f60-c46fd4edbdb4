<?php
// 测试伴学星登录功能
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, token');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入ThinkPHP
require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;

try {
    // 测试数据库连接
    $tables = Db::query("SHOW TABLES LIKE 'banxuexing_%'");
    
    echo json_encode([
        'code' => 200,
        'msg' => '数据库连接成功',
        'data' => [
            'tables' => $tables,
            'table_count' => count($tables)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'msg' => '数据库连接失败：' . $e->getMessage()
    ]);
}
?>
