<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号素材管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .tab-container {
            margin-bottom: 30px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 30px;
        }
        
        .tab {
            padding: 15px 30px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            font-weight: 600;
        }
        
        .tab:hover {
            color: #667eea;
            background: #f8f9ff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-label {
            display: block;
            padding: 20px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            text-align: center;
            color: #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload-label:hover {
            background: #f8f9ff;
            border-color: #5a67d8;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(244, 67, 54, 0.3);
        }
        
        .result-container {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                text-align: center;
            }
        }
        
        .material-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
        }
        
        .material-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .material-item:last-child {
            border-bottom: none;
        }
        
        .material-info {
            flex: 1;
        }
        
        .material-id {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 微信公众号素材管理工具</h1>
            <p>上传图片素材 | 生成草稿 | 管理永久素材</p>
        </div>
        
        <div class="content">
            <div class="tab-container">
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('upload')">📤 上传图片</button>
                    <button class="tab" onclick="switchTab('draft')">📝 生成草稿</button>
                    <button class="tab" onclick="switchTab('manage')">📋 素材管理</button>
                    <button class="tab" onclick="switchTab('batch')">📦 批量上传</button>
                </div>
                
                <!-- 上传图片标签页 -->
                <div id="upload" class="tab-content active">
                    <h3>上传图片为永久素材</h3>
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label>选择图片文件</label>
                                <div class="file-upload">
                                    <input type="file" id="imageFile" accept="image/*">
                                    <label for="imageFile" class="file-upload-label">
                                        📁 点击选择图片文件<br>
                                        <small>支持 JPG、PNG、GIF 格式，最大 2MB</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label>或输入图片URL</label>
                                <input type="url" id="imageUrl" class="form-control" placeholder="https://example.com/image.jpg">
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="uploadImage()">🚀 上传图片</button>
                </div>
                
                <!-- 生成草稿标签页 -->
                <div id="draft" class="tab-content">
                    <h3>生成微信公众号草稿</h3>
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label>消息数量限制</label>
                                <input type="number" id="messageLimit" class="form-control" value="10" min="1" max="50">
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label>自定义封面图片URL（可选）</label>
                                <input type="url" id="coverImage" class="form-control" placeholder="留空使用默认封面">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoPublish"> 自动发布（不勾选则只生成草稿）
                        </label>
                    </div>
                    <button class="btn btn-success" onclick="generateDraft()">📝 生成草稿</button>
                </div>
                
                <!-- 素材管理标签页 -->
                <div id="manage" class="tab-content">
                    <h3>永久素材管理</h3>
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label>素材类型</label>
                                <select id="materialType" class="form-control">
                                    <option value="image">图片</option>
                                    <option value="video">视频</option>
                                    <option value="voice">语音</option>
                                    <option value="news">图文</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label>获取数量</label>
                                <input type="number" id="materialCount" class="form-control" value="20" min="1" max="20">
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="getMaterialList()">📋 获取素材列表</button>
                    <div id="materialList" class="material-list" style="display: none;">
                        <!-- 素材列表将在这里显示 -->
                    </div>
                </div>
                
                <!-- 批量上传标签页 -->
                <div id="batch" class="tab-content">
                    <h3>批量上传图片</h3>
                    <div class="form-group">
                        <label>选择多个图片文件</label>
                        <div class="file-upload">
                            <input type="file" id="batchFiles" accept="image/*" multiple>
                            <label for="batchFiles" class="file-upload-label">
                                📁 点击选择多个图片文件<br>
                                <small>支持 JPG、PNG、GIF 格式，每个文件最大 2MB</small>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>或输入多个图片URL（每行一个）</label>
                        <textarea id="batchUrls" class="form-control" rows="5" placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="batchUpload()">📦 批量上传</button>
                </div>
            </div>
            
            <!-- 加载动画 -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>处理中，请稍候...</p>
            </div>
            
            <!-- 结果显示区域 -->
            <div id="result" class="result-container">
                <div id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/treehole/public/index.php';

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 给选中的标签添加active类
            event.target.classList.add('active');

            // 隐藏结果区域
            hideResult();
        }

        // 显示加载动画
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            hideResult();
        }

        // 隐藏加载动画
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 显示结果
        function showResult(content, isSuccess = true) {
            hideLoading();
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');

            resultDiv.className = `result-container ${isSuccess ? 'result-success' : 'result-error'}`;
            resultContent.innerHTML = content;
            resultDiv.style.display = 'block';
        }

        // 隐藏结果
        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }

        // 上传单个图片
        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const urlInput = document.getElementById('imageUrl');

            if (!fileInput.files[0] && !urlInput.value.trim()) {
                showResult('❌ 请选择图片文件或输入图片URL', false);
                return;
            }

            showLoading();

            try {
                const formData = new FormData();

                if (fileInput.files[0]) {
                    formData.append('image', fileInput.files[0]);
                } else {
                    formData.append('image_url', urlInput.value.trim());
                }

                const response = await fetch(`${API_BASE}/WechatMediaController/uploadImage`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    showResult(`
                        ✅ <strong>图片上传成功！</strong><br>
                        📋 Media ID: <code>${result.data.media_id}</code><br>
                        🕒 上传时间: ${result.data.upload_time}
                    `);

                    // 清空表单
                    fileInput.value = '';
                    urlInput.value = '';
                } else {
                    showResult(`❌ 上传失败: ${result.msg}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 生成草稿
        async function generateDraft() {
            const messageLimit = document.getElementById('messageLimit').value;
            const coverImage = document.getElementById('coverImage').value.trim();
            const autoPublish = document.getElementById('autoPublish').checked ? 1 : 0;

            showLoading();

            try {
                const response = await fetch(`${API_BASE}/SimpleDraftGenerator/generateDraft`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        limit: parseInt(messageLimit),
                        cover_image: coverImage,
                        auto_publish: autoPublish
                    })
                });

                const result = await response.json();

                if (result.code === 200) {
                    const action = autoPublish ? '生成并发布' : '生成';
                    showResult(`
                        ✅ <strong>草稿${action}成功！</strong><br>
                        📋 Media ID: <code>${result.data.media_id || '已发布'}</code><br>
                        📝 消息数量: ${messageLimit}条<br>
                        ${autoPublish ? '🚀 已自动发布到公众号' : '📄 草稿已保存，可在公众号后台查看'}
                    `);
                } else {
                    showResult(`❌ ${action}失败: ${result.msg}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 获取素材列表
        async function getMaterialList() {
            const materialType = document.getElementById('materialType').value;
            const materialCount = document.getElementById('materialCount').value;

            showLoading();

            try {
                const response = await fetch(`${API_BASE}/WechatMediaController/getMaterialList`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: materialType,
                        count: parseInt(materialCount),
                        offset: 0
                    })
                });

                const result = await response.json();

                if (result.code === 200) {
                    displayMaterialList(result.data);
                    showResult(`✅ 成功获取${materialType}素材列表`);
                } else {
                    showResult(`❌ 获取素材列表失败: ${result.msg}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 显示素材列表
        function displayMaterialList(data) {
            const listDiv = document.getElementById('materialList');

            if (!data.item || data.item.length === 0) {
                listDiv.innerHTML = '<p>📭 暂无素材</p>';
                listDiv.style.display = 'block';
                return;
            }

            let html = `<h4>📋 素材列表 (总计: ${data.total_count})</h4>`;

            data.item.forEach((item, index) => {
                html += `
                    <div class="material-item">
                        <div class="material-info">
                            <div><strong>素材 #${index + 1}</strong></div>
                            <div class="material-id">${item.media_id}</div>
                            <div><small>更新时间: ${new Date(item.update_time * 1000).toLocaleString()}</small></div>
                        </div>
                        <button class="btn btn-danger" onclick="deleteMaterial('${item.media_id}')">🗑️ 删除</button>
                    </div>
                `;
            });

            listDiv.innerHTML = html;
            listDiv.style.display = 'block';
        }

        // 删除素材
        async function deleteMaterial(mediaId) {
            if (!confirm('确定要删除这个素材吗？删除后无法恢复！')) {
                return;
            }

            showLoading();

            try {
                const response = await fetch(`${API_BASE}/WechatMediaController/deleteMaterial`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        media_id: mediaId
                    })
                });

                const result = await response.json();

                if (result.code === 200) {
                    showResult(`✅ 素材删除成功！`);
                    // 重新获取素材列表
                    setTimeout(() => {
                        getMaterialList();
                    }, 1000);
                } else {
                    showResult(`❌ 删除失败: ${result.msg}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 批量上传
        async function batchUpload() {
            const filesInput = document.getElementById('batchFiles');
            const urlsInput = document.getElementById('batchUrls').value.trim();

            if (!filesInput.files.length && !urlsInput) {
                showResult('❌ 请选择图片文件或输入图片URL列表', false);
                return;
            }

            showLoading();

            try {
                const formData = new FormData();

                // 添加文件
                if (filesInput.files.length > 0) {
                    for (let i = 0; i < filesInput.files.length; i++) {
                        formData.append('images[]', filesInput.files[i]);
                    }
                }

                // 添加URL列表
                if (urlsInput) {
                    const urls = urlsInput.split('\n').filter(url => url.trim());
                    formData.append('image_urls', JSON.stringify(urls));
                }

                const response = await fetch(`${API_BASE}/WechatMediaController/batchUploadImages`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    displayBatchResults(result.data);
                    showResult(`✅ 批量上传完成！成功: ${result.data.success_count}个，失败: ${result.data.fail_count}个`);

                    // 清空表单
                    filesInput.value = '';
                    document.getElementById('batchUrls').value = '';
                } else {
                    showResult(`❌ 批量上传失败: ${result.msg}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 显示批量上传结果
        function displayBatchResults(data) {
            let html = `
                <h4>📊 批量上传结果</h4>
                <p><strong>成功:</strong> ${data.success_count}个 | <strong>失败:</strong> ${data.fail_count}个</p>
                <div style="max-height: 300px; overflow-y: auto; margin-top: 15px;">
            `;

            data.results.forEach((item, index) => {
                const status = item.success ? '✅' : '❌';
                const statusClass = item.success ? 'color: green' : 'color: red';

                html += `
                    <div style="padding: 8px; border-bottom: 1px solid #eee;">
                        <span style="${statusClass}">${status}</span>
                        <strong>#${index + 1}</strong>
                        ${item.filename || item.url || ''}
                        ${item.success ? `<br><code style="font-size: 12px;">${item.media_id}</code>` : `<br><small style="color: red;">${item.error}</small>`}
                    </div>
                `;
            });

            html += '</div>';

            // 将结果添加到现有结果中
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML += html;
        }

        // 文件选择提示
        document.getElementById('imageFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const label = document.querySelector('label[for="imageFile"]');
                const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);
                label.innerHTML = `📁 已选择: ${file.name}<br><small>大小: ${fileSizeMB}MB</small>`;
            }
        });

        document.getElementById('batchFiles').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                const label = document.querySelector('label[for="batchFiles"]');
                const totalSize = Array.from(files).reduce((total, file) => total + file.size, 0);
                const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
                label.innerHTML = `📁 已选择 ${files.length} 个文件<br><small>总大小: ${totalSizeMB}MB</small>`;
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 微信公众号素材管理工具已加载');
        });
    </script>
</body>
</html>
