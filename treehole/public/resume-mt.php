<?php
// 简历信息配置
$resumeData = [
    'basic' => [
        'name' => '王铭浩',
        'age' => '25岁',
	'title' => '26届硕士',
        'photo' => 'images/avatar.jpg',
        'contact' => [
            ['icon' => 'images/phone_icon.png', 'value' => '15275535695'],
            ['icon' => 'images/email_icon.png', 'value' => '<EMAIL>'],
            ['icon' => 'images/school_icon.png', 'value' => 'Wenroujun1']
        ]
    ],
    
    'summary' => '北航电子信息硕士在读，本科毕业于哈工大自动化专业。具备校园数字化系统开发经验，掌握全栈开发技术与数据分析能力。对利用AI技术改进产品、提升用户体验充满热情，具备快速学习和应用AI相关知识的能力，致力于构建高效、便捷、安全的数字化应用与服务。',
    'self_evaluation' => [
        '乐于了解、学习新技术，善于用新技术解决需求',
        '善于发现用户需求痛点，将其转化为可行产品方案',
        '具备较强自驱力，主动推动项目从0到1实现落地',

        '积极探索AI技术在产品中的应用可能性，乐于动手实践并构建原型',
        '坚持不懈，长期专注于产品价值创造和用户满意度提升'
    ],
    'skills' => [
        ['数字化应用', '需求分析、系统设计、用户体验优化、数据分析、数字化转型'],
        ['项目能力', 'PRD文档编写、原型与交互设计、需求收集调研、数据分析和可视化'],
        ['AI技术', '各种AI工具应用、机器学习训练与应用'],
        ['技术开发', 'JavaScript、微信小程序、ThinkPHP、MySQL、Python'],
    ],
    'experiences' => [
        [
            'position' => '校级校园二手书回收项目',
            'title' => '技术开发 | 主导项目：二手书回收小程序',
            'period' => '2024.03 - 2024.06',
            'background' => [
                '校园二手书交易需求旺盛，但传统交易方式效率低下',
                '社团管理人员需要高效处理<b>上万本图书</b>的入库和分发工作'
            ],
            'content' => [
                '设计并开发二手书小程序，支持<b>连续扫描</b>，实现<b>单日处理5000+本图书</b>的高通量入库',
                '实现用户<b>提前下单功能</b>，开学前集中领取，减少现场排队时间，<b>单日处理3000+订单</b>',
                '设计<b>取件码系统</b>，实现图书预打包和快速领取，<b>分发效率提升200%</b>',
                '优化用户界面，支持<b>图书分类、搜索和价格筛选</b>，提升用户体验',
                '设计<b>数据统计功能</b>，帮助社团管理人员分析图书流通情况，<b>累计处理图书2万+本</b>，为未来引入智能化推荐或库存预测功能奠定基础'
            ],
            'features' => [
                ['高效入库', '连续扫描、批量处理、自动分类'],
                ['智能分发', '取件码系统、预打包、快速领取'],
                ['数据分析', '流通统计、库存管理、需求预测']
            ]
        ]
    ],
    'internship' => [
        [
            'position' => '灵行网络科技公司',
            'title' => '产品经理&AI技术开发 | 参与项目：校园生活服务小程序',
            'period' => '2024.07 - 至今',
            'background' => [
                '校园内缺少社交和本地生活的平台，有着广阔的需求和市场',
                'AIcoding背景下，低成本快速开发和校园内共同创建生态成为可能'
            ],
            'content' => [
                '负责小程序的产品设计与使用AIcoding完成大部分开发，集成<b>论坛、社团、生活服务、选课社区</b>等功能',
                '设计并实现社团、学生自建QQ群联动机制，完成用户裂变，<b>用户量增长超3000人</b>',
                '优化社团活动推送系统，解决信息分散问题，<b>提升活动参与率30%</b>',
                '设计生活服务板块，建立校内互助平台，实现<b>服务变现与资源共享</b>',
                '主导选课评价系统开发，实现<b>课程评分、评价、老师推荐</b>等功能',
                '负责<b>小程序、公众号、QQ三端互联</b>的产品设计'
            ],
            'features' => [
                ['论坛社区', '信息分类、投票、点赞、评论、提醒交互'],
                ['社团QQ群联动', '社团信息整合、学生自建QQ群联系方式、社团公众号'],
                ['社团活动推送', '活动发布、发布管理、信息集成、统一推送'],
                ['校园生活服务', '校内互助、价格优惠、服务变现、资源共享'],
                ['选课评价系统', '课程评分、课程评价、老师推荐、经验分享'],
                ['三端联动', '小程序、公众号、QQ群']
            ]
        ]
    ],
    'education' => [
        [
            'school' => '北京航空航天大学',
            'degree' => '电子信息 · 硕士',
            'period' => '2023.09-2026.01',
            'icon' => 'images/buaa_icon.png'  // 北航图标
        ],
        [
            'school' => '哈尔滨工业大学',
            'degree' => '自动化 · 学士',
            'period' => '2018.09-2022.06',
            'icon' => 'images/hit_icon.png'   // 哈工大图标
        ]
    ]
];

// 可以在这里添加从数据库获取简历数据的代码
// 例如：从MySQL数据库获取数据替换上面的$resumeData

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $resumeData['basic']['name']; ?> - 简历</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* PDF导出优化 */
        @page {
            size: A4;
            margin: 0;
        }
        
        html {
            zoom: 1;
        }
        
        body {
            font-family: 'SF Pro Text', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            font-size: 13px;
            width: 100%;
        }
        
        /* 打印模式专用样式 */
        .print-mode {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
            background-color: white;
        }
        
        .container {
            width: 210mm;
            height: 297mm;
            margin: 20px auto;
            background-color: #fff;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
            padding-bottom: 0;
        }
        
        /* 打印样式 */
        @media print {
            @page {
                size: A4;
                margin: 0;
            }
            html, body {
                width: 210mm;
                height: 297mm;
                background-color: #fff;
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            .container {
                box-shadow: none;
                margin: 0 auto !important; /* 确保居中 */
                padding: 0;
                width: 210mm;
                height: 297mm;
                page-break-after: avoid;
                overflow: hidden;
                position: relative;
                left: 50%;
                transform: translateX(-50%); /* 确保居中 */
            }
            .print-buttons {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                position: absolute !important;
                z-index: -1 !important;
            }
            button, .btn {
                display: none !important;
            }
            /* 确保页面内容适合打印 */
            .resume-grid {
                page-break-inside: avoid;
            }
            /* 保留颜色样式 */
            .feature-card, .feature-title, .feature-title::before,
            .highlight-item::before, .section-title::after,
            .main-title::after, .evaluation-item::before {
                color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
            }
            /* 移除所有交互效果 */
            .feature-card:hover {
                box-shadow: none !important;
                border-color: #eaeaea !important;
            }
            
            /* 修复头像和校徽图像的方形阴影问题 */
            .avatar {
                box-shadow: none !important;
                border: none !important;
            }
            .avatar img {
                border-radius: 50%;
            }
            .education-icon img, .contact-icon img {
                box-shadow: none !important;
                border: none !important;
            }
        }
        
        /* 辅助按钮 */
        .print-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        .btn {
            background-color: #306EEA;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }
        .btn:hover {
            background-color: #2756bf;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        /* 简历布局 */
        .resume-grid {
            display: grid;
            grid-template-columns: 220px 1fr;
            height: 100%;
            max-height: 297mm;
        }
        
        /* 侧边栏 */
        .sidebar {
            background-color: #f8f9fa;
            padding: 30px 20px;
            border-right: 1px solid #eaeaea;
            height: 100%;
            overflow: hidden;
        }
        .avatar {
            width: 110px;
            height: 110px;
            border-radius: 50%;
            margin: 0 auto 20px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            border: 3px solid white;
            background-color: white;
        }
        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .name {
            font-size: 20px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 5px;
            color: #222;
        }
        .job-title {
            font-size: 13px;
            color: #666;
            text-align: center;
            margin-bottom: 22px;
            font-weight: 500;
        }
        .contact-list {
            margin-bottom: 25px;
        }
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #444;
        }
        .contact-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .contact-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.2s ease;
        }
        .contact-icon:hover img {
            transform: scale(1.1);
        }
        
        /* 教育部分 */
        .education-section {
            margin-bottom: 25px;
        }
        .education-item {
            margin-bottom: 10px;
        }
        .education-icon {
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-right: 6px;
            vertical-align: middle;
        }
        .education-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.2s ease;
        }
        .education-icon:hover img {
            transform: scale(1.1);
        }
        .education-school {
            font-weight: 600;
            color: #444;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
        }
        .education-details {
            display: flex;
            justify-content: space-between;
            color: #666;
            font-size: 11.5px;
        }
        
        /* 技能部分 */
        .section-title {
            font-size: 15px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #222;
            position: relative;
            padding-bottom: 7px;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 35px;
            height: 3px;
            background-color: #306EEA;
        }
        .skill-group {
            margin-bottom: 12px;
        }
        .skill-title {
            font-weight: 600;
            margin-bottom: 3px;
            color: #444;
            font-size: 12.5px;
        }
        .skill-list {
            color: #666;
            font-size: 11.5px;
            line-height: 1.4;
            text-align: justify;
            text-justify: distribute;
            word-break: break-all;
            word-spacing: normal;
            white-space: normal;
        }
        
        /* 主内容区 */
        .main-content {
            padding: 30px 30px 40px 35px;
            height: 100%;
            overflow: hidden;
        }
        .section {
            margin-bottom: 22px;
        }
        .main-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 14px;
            color: #222;
            position: relative;
            padding-bottom: 7px;
        }
        .main-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: #306EEA;
        }
        
        /* 个人简介 */
        .summary {
            font-size: 13px;
            color: #444;
            margin-bottom: 5px;
            line-height: 1.6;
        }
        
        /* 经验部分 */
        .experience {
            margin-bottom: 22px;
            padding-bottom: 20px;
            border-bottom: 1px dashed #eaeaea;
        }
        .experience:last-child {
            border-bottom: none;
            padding-bottom: 20px;
            margin-bottom: 0;
        }
        .experience-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            align-items: center;
        }
        .experience-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-top: 3px;
            margin-bottom: 12px;
        }
        .experience-company {
            font-size: 15px;
            font-weight: 600;
            color: #306EEA;
        }
        .experience-period {
            color: #666;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* 亮点列表 */
        .highlight-list {
            list-style-type: none;
            margin-bottom: 15px;
            margin-top: 0;
        }
        .highlight-item {
            position: relative;
            padding-left: 22px;
            margin-bottom: 8px;
            line-height: 1.5;
            font-size: 12.5px;
        }
        .highlight-item::before {
            content: '•';
            position: absolute;
            left: 8px;
            color: #306EEA;
            font-weight: bold;
            font-size: 15px;
        }
        
        /* 项目背景和内容部分 */
        .project-section {
            margin-bottom: 15px;
        }
        .project-section-title {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            padding-left: 8px;
            border-left: 3px solid #306EEA;
        }
        
        /* 功能特性 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
        .feature-card {
            border: 1px solid #eaeaea;
            border-radius: 6px;
            padding: 10px;
            background-color: #f8f9fa;
            transition: all 0.2s ease;
            height: 100%;
        }
        .feature-card:hover {
            box-shadow: 0 3px 8px rgba(0,0,0,0.05);
            border-color: #ddd;
        }
        .feature-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
            display: flex;
            align-items: center;
            font-size: 12.5px;
        }
        .feature-title::before {
            content: '•';
            color: #306EEA;
            margin-right: 5px;
            font-size: 16px;
            line-height: 1;
        }
        .feature-desc {
            color: #666;
            font-size: 11px;
            line-height: 1.4;
        }
        
        /* 打印时的功能特性样式 */
        @media print {
            .feature-card {
                background-color: #f8f9fa !important;
                border: 1px solid #eaeaea !important;
                color: #333 !important;
            }
            .feature-title {
                color: #333 !important;
            }
            .feature-title::before {
                color: #306EEA !important;
            }
            .feature-desc {
                color: #666 !important;
            }
        }
        
        /* 添加重点样式 */
        .highlight-text {
            color: #306EEA;
            font-weight: 700;
            border-bottom: 2px solid #306EEA;
            padding-bottom: 1px;
            display: inline;
        }
        
        /* 年龄样式 */
        .age {
            font-size: 14px;
            font-weight: normal;
            color: #666;
            margin-left: 5px;
        }
        
        /* 自我评价样式 */
        .evaluation-list {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        
        .evaluation-item {
            position: relative;
            padding-left: 15px;
            margin-bottom: 8px;
            color: #666;
            font-size: 11.5px;
            line-height: 1.4;
            text-align: justify;
            text-justify: distribute;
            word-break: break-all;
            word-spacing: normal;
            white-space: normal;
        }
        
        .evaluation-item::before {
            content: '•';
            position: absolute;
            left: 2px;
            color: #306EEA;
            font-size: 14px;
        }
        
        @media print {
            /* 打印时的布局调整 */
            .resume-grid {
                height: 297mm;
                overflow: hidden;
            }
            
            .sidebar, .main-content {
                height: 297mm;
                overflow: hidden;
            }
        }
    </style>
</head>
<body>
    <div class="print-buttons">
        <button class="btn" onclick="window.print()">打印简历</button>
        <button class="btn" onclick="savePDF()">保存PDF</button>
    </div>
    
    <div class="container">
        <div class="resume-grid">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 头像 -->
                <div class="avatar">
                    <?php
                    // 照片处理：如果有照片则显示，否则显示默认头像
                    $photoPath = $resumeData['basic']['photo'];
                    // 检查是否为相对路径，如果是则转为绝对URL
                    if (!empty($photoPath) && strpos($photoPath, 'http') !== 0 && strpos($photoPath, 'data:') !== 0) {
                        // 获取当前URL基础路径
                        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/';
                        $photoUrl = $baseUrl . $photoPath;
                    } else if (empty($photoPath)) {
                        // 默认头像（使用Base64编码的数据URL）
                        $photoUrl = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0iI2U2ZTZlNiIvPjxjaXJjbGUgY3g9IjEyOCIgY3k9IjEwMCIgcj0iNTAiIGZpbGw9IiNiM2IzYjMiLz48cGF0aCBkPSJNMjE0IDE4NS41YzAgMTQtOTYgMTQtMTcyIDBDMjggODAuNSA4NCA1MiAxMjggNTJzMTAwIDI4LjUgODYgMTMzLjV6IiBmaWxsPSIjYjNiM2IzIi8+PC9zdmc+';
                    } else {
                        $photoUrl = $photoPath;
                    }
                    ?>
                    <img src="<?php echo $photoUrl; ?>" alt="<?php echo $resumeData['basic']['name']; ?>">
                </div>
                
                <!-- 姓名和职位 -->
                <h1 class="name"><?php echo $resumeData['basic']['name']; ?> <span class="age"><?php echo $resumeData['basic']['age']; ?></span></h1>
                <div class="job-title"><?php echo $resumeData['basic']['title']; ?></div>
                
                <!-- 联系方式 -->
                <div class="contact-list">
                    <?php foreach ($resumeData['basic']['contact'] as $contact): ?>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <?php
                            // 检查是否为图片路径
                            if (strpos($contact['icon'], '.') !== false) {
                                // 获取当前URL基础路径
                                $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/';
                                $iconUrl = $baseUrl . $contact['icon'];
                                echo '<img src="' . $iconUrl . '" alt="联系方式图标">';
                            } else {
                                // 如果不是图片路径，就显示emoji
                                echo $contact['icon'];
                            }
                            ?>
                        </div>
                        <div class="contact-value"><?php echo $contact['value']; ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 教育背景 -->
                <div class="education-section">
                    <h2 class="section-title">教育背景</h2>
                    <?php foreach ($resumeData['education'] as $edu): ?>
                    <div class="education-item">
                        <div class="education-school">
                            <div class="education-icon">
                                <?php
                                // 检查是否有图标路径
                                if (!empty($edu['icon'])) {
                                    // 获取当前URL基础路径
                                    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/';
                                    $iconUrl = $baseUrl . $edu['icon'];
                                    echo '<img src="' . $iconUrl . '" alt="学校图标">';
                                } else {
                                    // 如果没有图标则显示默认图标
                                    echo '🏫';
                                }
                                ?>
                            </div>
                            <?php echo $edu['school']; ?>
                        </div>
                        <div class="education-details">
                            <div><?php echo $edu['degree']; ?></div>
                            <div><?php echo $edu['period']; ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 技能 -->
                <div class="section">
                    <h2 class="section-title">专业技能</h2>
                    <?php foreach ($resumeData['skills'] as $skill): ?>
                    <div class="skill-group">
                        <div class="skill-title"><?php echo $skill[0]; ?></div>
                        <div class="skill-list"><?php echo $skill[1]; ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 自我评价 -->
                <div class="section">
                    <h2 class="section-title">自我评价</h2>
                    <ul class="evaluation-list">
                        <?php foreach ($resumeData['self_evaluation'] as $evaluation): ?>
                        <li class="evaluation-item"><?php echo $evaluation; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 个人简介 -->
                <!-- <div class="section">
                    <h2 class="main-title">个人简介</h2>
                    <p class="summary"><?php echo $resumeData['summary']; ?></p>
                </div> -->

                <!-- 实习经历 -->
                <div class="section">
                    <h2 class="main-title">实习经历</h2>
                    
                    <?php foreach ($resumeData['internship'] as $intern): ?>
                    <div class="experience">
                        <div class="experience-header">
                            <div class="experience-company"><?php echo $intern['position']; ?></div>
                            <div class="experience-period"><?php echo $intern['period']; ?></div>
                        </div>
                        <div class="experience-title"><?php echo $intern['title']; ?></div>
                        
                        <!-- 项目背景 -->
                        <div class="project-section">
                            <div class="project-section-title">项目背景</div>
                            <ul class="highlight-list">
                                <?php foreach ($intern['background'] as $item): ?>
                                <li class="highlight-item"><?php echo $item; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <!-- 项目内容 -->
                        <div class="project-section">
                            <div class="project-section-title">项目内容</div>
                            <ul class="highlight-list">
                                <?php foreach ($intern['content'] as $item): ?>
                                <li class="highlight-item"><?php echo $item; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <!-- 功能特性 -->
                        <div class="features-grid">
                            <?php foreach ($intern['features'] as $feature): ?>
                            <div class="feature-card">
                                <div class="feature-title"><?php echo $feature[0]; ?></div>
                                <div class="feature-desc"><?php echo $feature[1]; ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 项目经验 -->
                <div class="section">
                    <h2 class="main-title">项目经验</h2>
                    
                    <?php foreach ($resumeData['experiences'] as $exp): ?>
                    <div class="experience">
                        <div class="experience-header">
                            <div class="experience-company"><?php echo $exp['position']; ?></div>
                            <div class="experience-period"><?php echo $exp['period']; ?></div>
                        </div>
                        <div class="experience-title"><?php echo $exp['title']; ?></div>
                        
                        <!-- 项目背景 -->
                        <div class="project-section">
                            <div class="project-section-title">项目背景</div>
                            <ul class="highlight-list">
                                <?php foreach ($exp['background'] as $item): ?>
                                <li class="highlight-item"><?php echo $item; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <!-- 项目内容 -->
                        <div class="project-section">
                            <div class="project-section-title">项目内容</div>
                            <ul class="highlight-list">
                                <?php foreach ($exp['content'] as $item): ?>
                                <li class="highlight-item"><?php echo $item; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <!-- 功能特性 -->
                        <?php if (!empty($exp['features'])): ?>
                        <div class="features-grid">
                            <?php foreach ($exp['features'] as $feature): ?>
                            <div class="feature-card">
                                <div class="feature-title"><?php echo $feature[0]; ?></div>
                                <div class="feature-desc"><?php echo $feature[1]; ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
    function savePDF() {
        // 应用打印媒体样式
        document.documentElement.classList.add('print-mode');
        
        // 隐藏滚动条
        document.body.style.overflow = 'hidden';
        
        // 设置全屏
        const container = document.querySelector('.container');
        const originalStyle = {
            margin: container.style.margin,
            boxShadow: container.style.boxShadow
        };
        
        // 移除外边距和阴影
        container.style.margin = '0 auto'; // 修改为水平居中
        container.style.boxShadow = 'none';
        
        // 修改打印设置
        const style = document.createElement('style');
        style.textContent = `
            @page {
                size: 210mm 297mm;
                margin: 0;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .container {
                position: relative;
                left: 50%;
                transform: translateX(-50%);
            }
        `;
        document.head.appendChild(style);
        
        // 打印
        window.print();
        
        // 还原设置
        setTimeout(() => {
            document.head.removeChild(style);
            container.style.margin = originalStyle.margin;
            container.style.boxShadow = originalStyle.boxShadow;
            document.body.style.overflow = '';
            document.documentElement.classList.remove('print-mode');
            
            alert("请在打印对话框中选择'另存为PDF'选项，并确保：\n1. 页面大小为A4\n2. 边距设为'无'\n3. 启用'背景图形'选项\n4. 缩放比例为100%");
        }, 100);
    }

    // 照片上传功能
    function setupPhotoUpload() {
        // 创建隐藏的文件上传输入框
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // 为头像区域添加点击事件
        const avatar = document.querySelector('.avatar');
        if (avatar) {
            avatar.style.cursor = 'pointer';
            avatar.title = '点击上传照片';
            avatar.addEventListener('click', () => {
                fileInput.click();
            });
        }

        // 处理文件选择
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    // 将图片更新为base64编码的数据URL
                    const imageElement = document.querySelector('.avatar img');
                    if (imageElement) {
                        imageElement.src = event.target.result;
                        // 图片数据保存在本地存储中，用于下次加载
                        localStorage.setItem('resumePhoto', event.target.result);
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        // 从本地存储中恢复照片
        const savedPhoto = localStorage.getItem('resumePhoto');
        if (savedPhoto) {
            const imageElement = document.querySelector('.avatar img');
            if (imageElement) {
                imageElement.src = savedPhoto;
            }
        }
        
        // 设置联系方式图标上传
        setupIconUpload();
    }
    
    // 联系方式图标上传功能
    function setupIconUpload() {
        const contactIcons = document.querySelectorAll('.contact-icon');
        
        contactIcons.forEach((iconElement, index) => {
            // 设置鼠标悬停提示
            iconElement.title = '点击上传图标';
            
            // 添加点击事件
            iconElement.addEventListener('click', () => {
                // 创建文件输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                // 触发点击事件
                fileInput.click();
                
                // 文件选择后的处理
                fileInput.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            // 找到图标的图片元素
                            const imgElement = iconElement.querySelector('img');
                            if (imgElement) {
                                // 更新图片源
                                imgElement.src = event.target.result;
                                // 保存到本地存储
                                localStorage.setItem('contactIcon_' + index, event.target.result);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
            
            // 从本地存储中恢复图标
            const savedIcon = localStorage.getItem('contactIcon_' + index);
            if (savedIcon) {
                const imgElement = iconElement.querySelector('img');
                if (imgElement) {
                    imgElement.src = savedIcon;
                }
            }
        });
    }

    // 设置教育图标上传功能
    function setupEducationIconUpload() {
        const educationIcons = document.querySelectorAll('.education-icon');
        
        educationIcons.forEach((iconElement, index) => {
            // 设置鼠标悬停提示
            iconElement.title = '点击上传学校图标';
            
            // 添加点击事件
            iconElement.addEventListener('click', () => {
                // 创建文件输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                // 触发点击事件
                fileInput.click();
                
                // 文件选择后的处理
                fileInput.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            // 找到图标的图片元素
                            const imgElement = iconElement.querySelector('img');
                            if (imgElement) {
                                // 更新图片源
                                imgElement.src = event.target.result;
                                // 保存到本地存储
                                localStorage.setItem('educationIcon_' + index, event.target.result);
                            } else {
                                // 如果之前没有图片元素，则创建一个
                                const img = document.createElement('img');
                                img.src = event.target.result;
                                img.alt = '学校图标';
                                iconElement.innerHTML = '';
                                iconElement.appendChild(img);
                                // 保存到本地存储
                                localStorage.setItem('educationIcon_' + index, event.target.result);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
            
            // 从本地存储中恢复图标
            const savedIcon = localStorage.getItem('educationIcon_' + index);
            if (savedIcon) {
                const imgElement = iconElement.querySelector('img');
                if (imgElement) {
                    imgElement.src = savedIcon;
                } else {
                    // 如果之前没有图片元素，则创建一个
                    const img = document.createElement('img');
                    img.src = savedIcon;
                    img.alt = '学校图标';
                    iconElement.innerHTML = '';
                    iconElement.appendChild(img);
                }
            }
        });
    }

    // 页面加载完成后初始化所有上传功能
    document.addEventListener('DOMContentLoaded', function() {
        setupPhotoUpload();
        setupIconUpload();
        setupEducationIconUpload();
    });
    </script>
</body>
</html> 