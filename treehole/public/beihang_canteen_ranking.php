<?php
// 获取数据库中的真实数据
try {
    // 直接使用PDO连接数据库
    $host = 'localhost';
    $dbname = 'treehole';
    $username = 'root';
    $password = 'root';
    $port = '8889';

    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取食堂数据，按评分排序
    $stmt = $pdo->prepare("SELECT * FROM canteens WHERE status = 1 ORDER BY avg_rating DESC, total_ratings DESC, id ASC");
    $stmt->execute();
    $canteens = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取食堂数据并为每个食堂获取前5个窗口
    $canteenData = [];
    foreach ($canteens as $canteen) {
        // 获取该食堂下评分最高的5个窗口
        $stmt = $pdo->prepare("SELECT * FROM windows WHERE canteen_id = ? AND status = 1 ORDER BY avg_rating DESC, total_ratings DESC LIMIT 5");
        $stmt->execute([$canteen['id']]);
        $windows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 为每个窗口获取热门评论和统计数据
        foreach ($windows as &$window) {
            // 获取窗口的热门评论
            $stmt = $pdo->prepare("SELECT content FROM window_comments WHERE window_id = ? AND status = 1 AND parent_id IS NULL ORDER BY like_count DESC, created_at DESC LIMIT 1");
            $stmt->execute([$window['id']]);
            $topComment = $stmt->fetch(PDO::FETCH_ASSOC);

            $window['hot_comment'] = $topComment ? $topComment['content'] : '暂无评论';

            // 获取评分和评论总数
            $stmt = $pdo->prepare("SELECT COUNT(*) as rating_count FROM window_ratings WHERE window_id = ? AND status = 1");
            $stmt->execute([$window['id']]);
            $ratingCount = $stmt->fetch(PDO::FETCH_ASSOC)['rating_count'];

            $stmt = $pdo->prepare("SELECT COUNT(*) as comment_count FROM window_comments WHERE window_id = ? AND status = 1");
            $stmt->execute([$window['id']]);
            $commentCount = $stmt->fetch(PDO::FETCH_ASSOC)['comment_count'];

            $window['total_people'] = $ratingCount + $commentCount;
        }

        // 计算食堂的总评价人数
        $stmt = $pdo->prepare("
            SELECT
                (SELECT COUNT(*) FROM window_ratings wr JOIN windows w ON wr.window_id = w.id WHERE w.canteen_id = ? AND wr.status = 1) +
                (SELECT COUNT(*) FROM window_comments wc JOIN windows w ON wc.window_id = w.id WHERE w.canteen_id = ? AND wc.status = 1) as total_people
        ");
        $stmt->execute([$canteen['id'], $canteen['id']]);
        $totalPeople = $stmt->fetch(PDO::FETCH_ASSOC)['total_people'];

        $canteenData[] = [
            'id' => $canteen['id'],
            'name' => $canteen['name'],
            'description' => $canteen['description'] ?: '暂无描述',
            'location' => $canteen['location'],
            'image_url' => $canteen['image_url'],
            'avg_rating' => $canteen['avg_rating'],
            'total_people' => $totalPeople,
            'windows' => $windows
        ];
    }

    // 按评分重新排序
    usort($canteenData, function($a, $b) {
        return $b['avg_rating'] <=> $a['avg_rating'];
    });

} catch (Exception $e) {
    // 如果数据库连接失败，使用默认数据
    $canteenData = [
        [
            'id' => 1,
            'name' => '学生第一食堂',
            'description' => '学校主要食堂，提供各种美食',
            'location' => '校园中心区域',
            'image_url' => '/images/shitang.png',
            'avg_rating' => 4.8,
            'total_people' => 1256,
            'windows' => [
                [
                    'id' => 1,
                    'name' => '川菜窗口',
                    'floor' => '1F',
                    'avg_rating' => 8.5,
                    'image_url' => '/images/shitang.png',
                    'hot_comment' => '麻婆豆腐做得特别正宗，很下饭！',
                    'total_people' => 156
                ],
                [
                    'id' => 2,
                    'name' => '粤菜窗口',
                    'floor' => '1F',
                    'avg_rating' => 7.8,
                    'image_url' => '/images/shitang.png',
                    'hot_comment' => '白切鸡很嫩，汤也很鲜美',
                    'total_people' => 98
                ]
            ]
        ],
        [
            'id' => 2,
            'name' => '学生第二食堂',
            'description' => '新装修的食堂，环境优雅',
            'location' => '校园东区',
            'image_url' => '/images/shitang.png',
            'avg_rating' => 4.6,
            'total_people' => 892,
            'windows' => [
                [
                    'id' => 3,
                    'name' => '西式快餐',
                    'floor' => '1F',
                    'avg_rating' => 7.2,
                    'image_url' => '/images/shitang.png',
                    'hot_comment' => '汉堡做得不错，薯条也很香脆',
                    'total_people' => 89
                ]
            ]
        ]
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北航食堂美食排行榜 | 你的味蕾指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            text-align: center;
            padding: 40px 20px 30px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .intro {
            padding: 25px 20px;
            background: #f8f9fa;
            text-align: center;
        }
        
        .intro p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        .intro-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .intro-note {
            font-size: 14px;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 0;
        }
        
        .section {
            margin: 20px 0;
            padding: 0 20px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #ffa726);
            border-radius: 2px;
        }

        .section-desc {
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        .window-card {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .window-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .window-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .window-rank {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .window-avatar {
            width: 50px;
            height: 50px;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .avatar-img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: cover;
        }

        .window-info {
            flex: 1;
        }

        .window-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .window-location {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .canteen-name {
            font-size: 14px;
            color: #7f8c8d;
        }

        .floor-info {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .window-rating {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .window-comment {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .comment-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .comment-content {
            font-size: 14px;
            color: #2c3e50;
            line-height: 1.5;
            font-style: italic;
        }
        
        .rating-label {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
        }

        .rating-label.excellent {
            background: #e8f5e8;
            color: #27ae60;
        }

        .rating-label.good {
            background: #e3f2fd;
            color: #2196f3;
        }

        .rating-label.average {
            background: #fff3e0;
            color: #ff9800;
        }

        .rating-label.poor {
            background: #fce4ec;
            color: #e91e63;
        }

        .rating-label.bad {
            background: #ffebee;
            color: #f44336;
        }
        
        .rating-text {
            font-size: 16px;
            font-weight: bold;
            color: #ff6b6b;
        }
        
        .rating-count {
            font-size: 14px;
            color: #999;
        }
        
        .canteen-desc {
            margin-top: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .window-section {
            margin-top: 20px;
        }
        
        .window-title {
            font-size: 16px;
            font-weight: bold;
            color: #34495e;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .window-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .window-list {
            display: grid;
            gap: 10px;
        }
        
        .window-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .window-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .window-specialty {
            font-size: 13px;
            color: #7f8c8d;
        }

        .tips {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 20px;
            margin: 30px 20px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .tips h3 {
            color: #2c3e50;
            margin-bottom: 12px;
            font-size: 18px;
        }

        .tips ul {
            list-style: none;
            padding: 0;
        }

        .tips li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }

        .guide-content {
            text-align: left;
        }

        .guide-intro {
            margin-bottom: 20px;
            color: #555;
        }

        .guide-section {
            margin-bottom: 20px;
        }

        .guide-section h4 {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .guide-section p {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 40px;
        }

        .footer h3 {
            margin-bottom: 15px;
            font-size: 20px;
        }

        .footer p {
            opacity: 0.8;
            line-height: 1.8;
        }

        .qr-placeholder {
            width: 120px;
            height: 120px;
            background: #34495e;
            margin: 20px auto;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bdc3c7;
            font-size: 14px;
        }

        .footer-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 25px 0;
        }

        .feature-item {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .feature-item h4 {
            color: white;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .feature-item p {
            color: #bdc3c7;
            font-size: 12px;
            margin: 0;
            line-height: 1.4;
        }

        .footer-note {
            font-size: 12px;
            color: #95a5a6;
            line-height: 1.5;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>北航食堂美食排行榜</h1>
            <p class="subtitle">基于真实用户评价的权威榜单</p>
        </div>

        <!-- 介绍 -->
        <div class="intro">
            <p class="intro-title">🍽️ 又到了一年一度的美食盘点时间！</p>
            <p>亲爱的北航同学们，你们期待已久的食堂排行榜新鲜出炉啦！📊</p>
            <p>经过对全校食堂数据的深度分析，我们为大家带来了最具权威性的北航食堂排行榜。本次排名基于<strong>数千名同学的真实评价</strong>和热度数据，涵盖了各个食堂的人气窗口和招牌美食。</p>
            <p>无论你是想要寻找新的美食体验，还是想避开"踩雷"，这份榜单都能为你的校园用餐提供最靠谱的参考！</p>
            <p class="intro-note">💡 评分采用-10到10分制，分数越高代表同学们越喜爱</p>
        </div>

        <!-- 食堂排行榜 -->
        <div class="section">
            <h2 class="section-title">食堂人气排行榜</h2>
            <p class="section-desc">以下排名根据用户评分、评价数量等多维度数据综合计算得出</p>

            <?php
            $maxDisplay = min(5, count($canteenData)); // 最多显示5个食堂
            for ($i = 0; $i < $maxDisplay; $i++):
                $canteen = $canteenData[$i];
                $rank = $i + 1;

                // 直接使用原始评分(-10到10)
                $rating = $canteen['avg_rating'];

                // 根据评分显示不同的标签
                if ($rating >= 8) {
                    $ratingLabel = '超赞';
                    $ratingClass = 'excellent';
                } elseif ($rating >= 5) {
                    $ratingLabel = '推荐';
                    $ratingClass = 'good';
                } elseif ($rating >= 0) {
                    $ratingLabel = '一般';
                    $ratingClass = 'average';
                } elseif ($rating >= -5) {
                    $ratingLabel = '待改进';
                    $ratingClass = 'poor';
                } else {
                    $ratingLabel = '需提升';
                    $ratingClass = 'bad';
                }
            ?>

            <!-- 第<?php echo $rank; ?>名 -->
            <div class="canteen-card">
                <div class="canteen-header">
                    <div class="canteen-rank"><?php echo $rank; ?></div>
                    <div class="canteen-avatar">
                        <img src="<?php echo htmlspecialchars($canteen['image_url'] ?: '/images/shitang.png'); ?>" alt="<?php echo htmlspecialchars($canteen['name']); ?>" class="avatar-img">
                    </div>
                    <div class="canteen-info">
                        <div class="canteen-name"><?php echo htmlspecialchars($canteen['name']); ?></div>
                        <div class="canteen-location"><?php echo htmlspecialchars($canteen['location']); ?></div>
                        <div class="canteen-rating">
                            <span class="rating-label <?php echo $ratingClass; ?>"><?php echo $ratingLabel; ?></span>
                            <span class="rating-text"><?php echo number_format($rating, 1); ?>分</span>
                            <span class="rating-count"><?php echo number_format($canteen['total_people']); ?>人参与评价</span>
                        </div>
                    </div>
                </div>

                <?php if (!empty($canteen['windows'])): ?>
                <div class="windows-section">
                    <div class="windows-title">热门窗口（前5名）</div>
                    <div class="windows-list">
                        <?php foreach ($canteen['windows'] as $window): ?>
                        <div class="window-item">
                            <div class="window-item-header">
                                <div class="window-item-avatar">
                                    <img src="<?php echo htmlspecialchars($window['image_url'] ?: '/images/shitang.png'); ?>" alt="<?php echo htmlspecialchars($window['name']); ?>" class="window-item-img">
                                </div>
                                <div class="window-item-info">
                                    <div class="window-item-name"><?php echo htmlspecialchars($window['name']); ?></div>
                                    <div class="window-item-details">
                                        <span class="window-floor"><?php echo htmlspecialchars($window['floor']); ?></span>
                                        <span class="window-rating"><?php echo number_format($window['avg_rating'], 1); ?>分</span>
                                        <span class="window-people"><?php echo $window['total_people']; ?>人评价</span>
                                    </div>
                                </div>
                            </div>
                            <div class="window-comment">
                                <span class="comment-label">热评：</span>
                                <span class="comment-text">"<?php echo htmlspecialchars(mb_substr($window['hot_comment'], 0, 40) . (mb_strlen($window['hot_comment']) > 40 ? '...' : '')); ?>"</span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <?php endfor; ?>
        </div>

        <!-- 用餐指南 -->
        <div class="tips">
            <h3>用餐指南</h3>
            <div class="guide-content">
                <p class="guide-intro">为了让大家有更好的用餐体验，我们整理了以下实用建议：</p>

                <div class="guide-section">
                    <h4>最佳用餐时间</h4>
                    <p>建议错峰就餐，避开12:00-12:30和18:00-18:30的用餐高峰期，既能避免排队，也能享受到更好的服务。</p>
                </div>

                <?php if (!empty($canteenData)): ?>
                <div class="guide-section">
                    <h4>本期推荐</h4>
                    <?php
                    $topCanteen = $canteenData[0];
                    if (!empty($topCanteen['windows'])):
                        $topWindow = $topCanteen['windows'][0];
                    ?>
                    <p>本期人气王是<strong><?php echo htmlspecialchars($topCanteen['name']); ?></strong>的<strong><?php echo htmlspecialchars($topWindow['name']); ?></strong>（<?php echo htmlspecialchars($topWindow['floor']); ?>），评分高达<?php echo number_format($topWindow['avg_rating'], 1); ?>分，获得了同学们的一致好评，值得一试。</p>
                    <?php endif; ?>

                    <?php if (count($canteenData) > 1): ?>
                    <p><?php echo htmlspecialchars($canteenData[1]['name']); ?>环境舒适，是想要换换口味的同学的不错选择。</p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <div class="guide-section">
                    <h4>温馨提示</h4>
                    <p>各食堂营业时间一般为07:00-21:00，具体时间可能因季节调整，请同学们合理安排用餐时间。如果发现好吃的窗口，记得在小程序中给个好评，帮助更多同学发现美食。</p>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <h3>体验小程序，发现更多美食</h3>
            <div class="qr-placeholder">
                小程序二维码
            </div>
            <div class="footer-features">
                <div class="feature-item">
                    <h4>实时评分</h4>
                    <p>查看最新的食堂和窗口评分</p>
                </div>
                <div class="feature-item">
                    <h4>发布评价</h4>
                    <p>分享你的用餐体验和推荐</p>
                </div>
                <div class="feature-item">
                    <h4>美食发现</h4>
                    <p>探索校园里的隐藏美食</p>
                </div>
                <div class="feature-item">
                    <h4>同学交流</h4>
                    <p>与其他同学交流美食心得</p>
                </div>
            </div>
            <p class="footer-note">数据每日更新，排名仅供参考。欢迎同学们积极参与评价，共同打造更好的校园用餐环境。</p>
        </div>
    </div>
</body>
</html>
