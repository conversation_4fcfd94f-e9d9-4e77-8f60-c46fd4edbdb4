<!DOCTYPE html>
<html>
<head>
    <title>伴学星API测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>伴学星API测试页面</h1>
    
    <div class="test-item">
        <h3>测试接口</h3>
        <button onclick="testAPI()">测试基础API</button>
        <div id="test-result"></div>
    </div>
    
    <div class="test-item">
        <h3>获取老师列表</h3>
        <button onclick="getTeacherList()">获取老师列表</button>
        <div id="teacher-result"></div>
    </div>

    <script>
        function testAPI() {
            fetch('/index.php/banxuexingtest/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: ''
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('test-result').innerHTML = 
                    '<div class="success">✓ API测试成功: ' + JSON.stringify(data) + '</div>';
            })
            .catch(error => {
                document.getElementById('test-result').innerHTML = 
                    '<div class="error">✗ API测试失败: ' + error + '</div>';
            });
        }

        function getTeacherList() {
            fetch('/index.php/banxuexingteacher/getTeacherList', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'filter=recommend&page=1&limit=5'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('teacher-result').innerHTML = 
                    '<div class="success">✓ 获取老师列表成功: ' + JSON.stringify(data) + '</div>';
            })
            .catch(error => {
                document.getElementById('teacher-result').innerHTML = 
                    '<div class="error">✗ 获取老师列表失败: ' + error + '</div>';
            });
        }
    </script>
</body>
</html>
