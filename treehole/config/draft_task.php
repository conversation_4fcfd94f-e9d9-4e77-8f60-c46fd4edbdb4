<?php
/**
 * 草稿生成定时任务配置文件
 * 简化版本 - 只包含必要的配置项
 */

return [
    // API接口地址
    'api_url' => 'https://www.bjgaoxiaoshequ.store/SimpleDraftGenerator/generateDraft',

    // 执行时间（每天17:00）
    'schedule_time' => '17:00',

    // 请求参数配置（可根据需要修改）
    'params' => [
        'limit' => 20,              // 获取消息数量，建议10-30条
        'auto_publish' => 0,        // 是否自动发布，0=仅生成草稿，1=生成并自动发布
        'cover_image' => '',        // 封面图片URL，留空使用默认封面
    ],

    // 请求超时时间（秒）
    'timeout' => 120,
];
