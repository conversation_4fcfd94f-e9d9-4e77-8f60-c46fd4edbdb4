<?php
/**
 * 统一上传接口测试脚本
 * 用于验证头像上传和其他图片上传的一致性
 */

require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;
use app\util\CosUtil;

// 加载配置
Config::load('config/database.php', 'database');
Config::load('config/cos.php', 'cos');

echo "=== 统一上传接口测试 ===\n\n";

try {
    // 1. 测试COS工具类
    echo "1. 测试COS工具类\n";
    echo "-------------------\n";
    
    // 测试环境检测
    $isLocal = CosUtil::isLocal();
    echo "当前环境: " . ($isLocal ? "本地开发环境" : "生产环境") . "\n";
    
    // 测试相对路径生成
    $testKey = 'dev/avatar/2025/08/02/test_avatar.jpg';
    $avatarUrl = CosUtil::generateUrl($testKey, 'public');
    echo "头像URL生成测试: {$testKey} -> {$avatarUrl}\n";
    
    $commentKey = 'dev/comment/2025/08/02/test_comment.jpg';
    $commentUrl = CosUtil::generateUrl($commentKey, 'public');
    echo "评论图片URL生成测试: {$commentKey} -> {$commentUrl}\n\n";
    
    // 2. 测试数据库连接
    echo "2. 测试数据库连接\n";
    echo "-------------------\n";
    
    $result = Db::query('SELECT 1 as test');
    if ($result) {
        echo "数据库连接成功\n\n";
    } else {
        throw new Exception("数据库连接失败");
    }
    
    // 3. 检查用户表中的头像URL格式
    echo "3. 检查用户表头像URL格式\n";
    echo "-------------------------\n";
    
    $users = Db::name('user')
        ->where('face_url', '<>', '')
        ->whereNotNull('face_url')
        ->limit(5)
        ->field('id, phone, face_url')
        ->select();
    
    foreach ($users as $user) {
        $isFullUrl = strpos($user['face_url'], 'http') === 0;
        $urlType = $isFullUrl ? "完整URL" : "相对路径";
        echo "用户ID: {$user['id']}, 头像: {$user['face_url']} ({$urlType})\n";
    }
    echo "\n";
    
    // 4. 检查消息表中的图片URL格式
    echo "4. 检查消息表图片URL格式\n";
    echo "-------------------------\n";
    
    $messages = Db::name('message')
        ->where('images', '<>', '')
        ->where('images', '<>', '[]')
        ->whereNotNull('images')
        ->limit(3)
        ->field('id, images')
        ->select();
    
    foreach ($messages as $message) {
        $hasFullUrl = strpos($message['images'], 'http') !== false;
        $urlType = $hasFullUrl ? "包含完整URL" : "相对路径";
        echo "消息ID: {$message['id']}, 图片: {$message['images']} ({$urlType})\n";
    }
    echo "\n";
    
    // 5. 统计URL格式分布
    echo "5. URL格式统计\n";
    echo "---------------\n";
    
    // 用户头像统计
    $userStats = Db::query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN face_url LIKE 'http%' THEN 1 END) as full_urls,
            COUNT(CASE WHEN face_url NOT LIKE 'http%' AND face_url IS NOT NULL AND face_url != '' THEN 1 END) as relative_paths
        FROM user 
        WHERE face_url IS NOT NULL AND face_url != ''
    ");
    
    if ($userStats) {
        $stats = $userStats[0];
        echo "用户头像: 总计 {$stats['total']}, 完整URL {$stats['full_urls']}, 相对路径 {$stats['relative_paths']}\n";
    }
    
    // 消息图片统计
    $messageStats = Db::query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN images LIKE '%http%' THEN 1 END) as full_urls,
            COUNT(CASE WHEN images NOT LIKE '%http%' AND images IS NOT NULL AND images != '' AND images != '[]' THEN 1 END) as relative_paths
        FROM message 
        WHERE images IS NOT NULL AND images != '' AND images != '[]'
    ");
    
    if ($messageStats) {
        $stats = $messageStats[0];
        echo "消息图片: 总计 {$stats['total']}, 包含完整URL {$stats['full_urls']}, 相对路径 {$stats['relative_paths']}\n";
    }
    echo "\n";
    
    // 6. 测试图片URL处理
    echo "6. 测试图片URL处理\n";
    echo "-------------------\n";
    
    $testUrls = [
        'dev/avatar/2025/08/02/test.jpg',
        'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/dev/avatar/2025/08/02/test.jpg',
        '/uploads/avatar/test.jpg',
        'prod/comment/2025/08/02/test.jpg'
    ];
    
    foreach ($testUrls as $testUrl) {
        $processedUrl = CosUtil::generateUrl($testUrl, 'public');
        echo "输入: {$testUrl}\n";
        echo "输出: {$processedUrl}\n";
        echo "---\n";
    }
    
    // 7. 验证一致性
    echo "7. 一致性验证\n";
    echo "-------------\n";
    
    $inconsistentUsers = Db::query("
        SELECT COUNT(*) as count 
        FROM user 
        WHERE face_url LIKE 'http%' 
        AND face_url IS NOT NULL 
        AND face_url != ''
    ");
    
    if ($inconsistentUsers && $inconsistentUsers[0]['count'] > 0) {
        echo "⚠️  发现 {$inconsistentUsers[0]['count']} 个用户的头像仍使用完整URL\n";
        echo "建议执行数据库迁移脚本: mysql/migrate_image_urls_to_relative_paths.sql\n";
    } else {
        echo "✅ 所有用户头像都使用相对路径，格式一致\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "请检查配置和数据库连接\n";
}

// 8. 输出建议
echo "\n=== 建议 ===\n";
echo "1. 如果发现完整URL，请执行数据库迁移脚本\n";
echo "2. 测试前端图片显示是否正常\n";
echo "3. 验证新上传的图片是否使用相对路径\n";
echo "4. 检查开发和生产环境的域名配置\n";
?>
