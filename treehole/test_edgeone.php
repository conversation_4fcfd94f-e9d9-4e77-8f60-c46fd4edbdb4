<?php
// EdgeOne CDN加速效果测试脚本
require_once 'vendor/autoload.php';

use Qcloud\Cos\Client;

echo "=== EdgeOne CDN加速测试 ===\n";

// 直接读取配置文件
$config = include 'config/cos.php';

// 创建测试图片
$testImageContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
$testImagePath = sys_get_temp_dir() . '/test_image.png';
file_put_contents($testImagePath, $testImageContent);

echo "1. 上传测试图片到COS...\n";

// 创建COS客户端
$client = new Client([
    'region' => $config['region'],
    'credentials' => $config['credentials']
]);

// 构建对象键
$fileName = 'test_' . time() . '.png';
$key = 'common/' . date('Y/m/d') . '/' . $fileName;

try {
    $result = $client->putObject([
        'Bucket' => $config['buckets']['public']['bucket'],
        'Key' => $key,
        'Body' => fopen($testImagePath, 'rb'),
        'ACL' => 'public-read'
    ]);

    echo "✓ 上传成功，文件路径: " . $key . "\n";
} catch (Exception $e) {
    echo "✗ 上传失败: " . $e->getMessage() . "\n";
    exit;
}

// 强制使用原始COS域名
$cosOriginalUrl = $config['buckets']['public']['domains']['default'] . '/' . $key;

// 强制使用EdgeOne CDN域名
$edgeoneUrl = $config['buckets']['public']['domains']['production'] . '/' . $key;

// 同时测试HTTP版本（绕过SSL问题）
$edgeoneHttpUrl = str_replace('https://', 'http://', $edgeoneUrl);

echo "\n2. 测试访问速度对比:\n";
echo "原始COS域名: " . $cosOriginalUrl . "\n";
echo "EdgeOne CDN (HTTPS): " . $edgeoneUrl . "\n";
echo "EdgeOne CDN (HTTP): " . $edgeoneHttpUrl . "\n\n";

// 测试访问速度
function testUrlSpeed($url, $name) {
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $error = curl_error($ch);
    curl_close($ch);
    
    $endTime = microtime(true);
    $realTime = ($endTime - $startTime) * 1000; // 转换为毫秒
    
    echo "{$name}:\n";
    if ($error) {
        echo "  ✗ 访问失败: {$error}\n";
        return false;
    } elseif ($httpCode !== 200) {
        echo "  ✗ HTTP状态码: {$httpCode}\n";
        return false;
    } else {
        echo "  ✓ 访问成功\n";
        echo "  ⏱️  响应时间: " . round($realTime, 2) . "ms\n";
        echo "  📊 CURL时间: " . round($totalTime * 1000, 2) . "ms\n";
        echo "  📦 响应大小: " . strlen($response) . " bytes\n";
        return $realTime;
    }
    echo "\n";
}

// 测试原始COS域名
$cosTime = testUrlSpeed($cosOriginalUrl, "原始COS域名");
echo "\n";

// 测试EdgeOne CDN (HTTPS)
$edgeoneTime = testUrlSpeed($edgeoneUrl, "EdgeOne CDN (HTTPS)");
echo "\n";

// 测试EdgeOne CDN (HTTP)
$edgeoneHttpTime = testUrlSpeed($edgeoneHttpUrl, "EdgeOne CDN (HTTP)");
echo "\n";

// 比较结果
echo "3. 性能对比结果:\n";
if ($cosTime && $edgeoneTime) {
    $improvement = (($cosTime - $edgeoneTime) / $cosTime) * 100;
    if ($improvement > 0) {
        echo "✓ EdgeOne CDN (HTTPS) 比原始COS快 " . round($improvement, 1) . "%\n";
        echo "✓ 加速效果: " . round($cosTime - $edgeoneTime, 2) . "ms\n";
    } elseif ($improvement < -10) {
        echo "⚠️  EdgeOne CDN (HTTPS) 比原始COS慢 " . round(abs($improvement), 1) . "%\n";
        echo "⚠️  可能原因: CDN缓存未命中或网络波动\n";
    } else {
        echo "➡️  HTTPS版本性能相近，差异在误差范围内\n";
    }
} elseif ($cosTime && $edgeoneHttpTime) {
    $improvement = (($cosTime - $edgeoneHttpTime) / $cosTime) * 100;
    if ($improvement > 0) {
        echo "✓ EdgeOne CDN (HTTP) 比原始COS快 " . round($improvement, 1) . "%\n";
        echo "✓ 加速效果: " . round($cosTime - $edgeoneHttpTime, 2) . "ms\n";
    } elseif ($improvement < -10) {
        echo "⚠️  EdgeOne CDN (HTTP) 比原始COS慢 " . round(abs($improvement), 1) . "%\n";
        echo "⚠️  可能原因: CDN缓存未命中或网络波动\n";
    } else {
        echo "➡️  HTTP版本性能相近，差异在误差范围内\n";
    }
} else {
    echo "⚠️  无法完成性能对比，请检查域名配置\n";
}

echo "\n4. 手动测试建议:\n";
echo "请在浏览器中访问以下URL进行手动测试:\n";
echo "- 原始COS: {$cosOriginalUrl}\n";
echo "- EdgeOne: {$edgeoneUrl}\n";
echo "\n注意观察:\n";
echo "- 首次访问速度（冷缓存）\n";
echo "- 二次访问速度（热缓存）\n";
echo "- 不同地区的访问速度\n";

// 清理测试文件
unlink($testImagePath);

echo "\n=== 测试完成 ===\n";
?>
