<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\model\BuaaMajors;
use app\util\JwtUtil;
use think\facade\Db;
use think\facade\Request;

class Major extends BaseController
{
    /**
     * 获取系别列表
     */
    public function departments()
    {
        try {
            // 验证token
            $token = Request::header('token');
            if (!$token) {
                return json(['error_code' => 401, 'msg' => '缺少token']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 获取所有系别，按系号分组
            $departments = BuaaMajors::field('department_id')
                ->where('university_id', 1)
                ->group('department_id')
                ->order('department_id', 'asc')
                ->select()
                ->toArray();

            // 格式化系别数据
            $result = [];
            foreach ($departments as $dept) {
                $result[] = [
                    'id' => $dept['department_id'],
                    'name' => $dept['department_id'] . '系'
                ];
            }

            return json(['error_code' => 0, 'msg' => '获取成功', 'data' => $result]);

        } catch (\Exception $e) {
            return json(['error_code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取专业列表
     */
    public function list()
    {
        try {
            // 验证token
            $token = Request::header('token');
            if (!$token) {
                return json(['error_code' => 401, 'msg' => '缺少token']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
            }

            $departmentId = Request::post('department_id', '');
            $keyword = Request::post('keyword', '');
            $page = intval(Request::post('page', 1));
            $pageSize = intval(Request::post('page_size', 20));

            // 构建查询条件
            $where = [
                ['university_id', '=', 1]
            ];

            if (!empty($departmentId)) {
                $where[] = ['department_id', '=', $departmentId];
            }

            if (!empty($keyword)) {
                $where[] = ['major_name|college_name', 'like', '%' . $keyword . '%'];
            }

            // 查询专业列表
            $majors = BuaaMajors::where($where)
                ->order('department_id', 'asc')
                ->order('id', 'asc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            // 为每个专业添加热评和评价人数
            foreach ($majors as &$major) {
                $majorId = $major['id'];

                // 获取评价人数（评论总数，不包括回复）
                $commentCount = Db::name('major_comments')
                    ->where('major_id', $majorId)
                    ->where('status', 1)
                    ->where('parent_id', 0) // 只统计主评论
                    ->count();

                // 获取热评（点赞数最多的评论，如果都为0则获取最新评论）
                // 先尝试从数据库获取，如果like_count不准确，则重新计算
                $hotComment = Db::name('major_comments')
                    ->alias('mc')
                    ->where('mc.major_id', $majorId)
                    ->where('mc.status', 1)
                    ->where('mc.parent_id', 0)
                    ->field('mc.id, mc.content, mc.like_count, mc.created_at')
                    ->order('mc.like_count desc, mc.created_at desc')
                    ->find();

                // 如果有评论，重新计算实际点赞数
                if ($hotComment) {
                    $actualLikeCount = Db::name('major_comment_likes')
                        ->where('comment_id', $hotComment['id'])
                        ->count();

                    // 如果数据库中的like_count与实际不符，更新它
                    if ($actualLikeCount != $hotComment['like_count']) {
                        Db::name('major_comments')
                            ->where('id', $hotComment['id'])
                            ->update(['like_count' => $actualLikeCount]);
                        $hotComment['like_count'] = $actualLikeCount;
                    }
                }

                $major['comment_count'] = $commentCount;
                $major['hot_comment'] = $hotComment ? $hotComment['content'] : '';
                $major['has_likes'] = $hotComment && $hotComment['like_count'] > 0;
            }

            return json(['error_code' => 0, 'msg' => '获取成功', 'data' => $majors]);

        } catch (\Exception $e) {
            return json(['error_code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取专业详情
     */
    public function detail()
    {
        try {
            // 验证token
            $token = Request::header('token');
            if (!$token) {
                return json(['error_code' => 401, 'msg' => '缺少token']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
            }

            $id = Request::post('id', 0);
            $currentUserId = $payload['user_id'];

            if (empty($id)) {
                return json(['error_code' => 400, 'msg' => '缺少专业ID']);
            }

            // 查询专业详情
            $major = BuaaMajors::find($id);
            if (!$major) {
                return json(['error_code' => 404, 'msg' => '专业不存在']);
            }

            // 补充专业详细信息
            $majorInfo = $major->toArray();

            // 添加默认的专业介绍信息
            $majorInfo['description'] = $this->getMajorDescription($majorInfo['major_name']);
            $majorInfo['training_goal'] = $this->getTrainingGoal($majorInfo['major_name']);
            $majorInfo['main_courses'] = $this->getMainCourses($majorInfo['major_name']);
            $majorInfo['career_direction'] = $this->getCareerDirection($majorInfo['major_name']);
            $majorInfo['duration'] = '四年';
            $majorInfo['degree'] = $this->getDegreeType($majorInfo['major_name']);

            // 获取专业评论列表
            $comments = Db::name('major_comments')
                ->alias('mc')
                ->leftJoin('user u', 'mc.user_id = u.id')
                ->where('mc.major_id', $id)
                ->where('mc.status', 1)
                ->where('mc.parent_id', 0) // 只获取顶级评论，parent_id为0
                ->field('mc.id, mc.major_id, mc.user_id, mc.content, mc.images, mc.like_count, mc.created_at, u.username, u.face_url')
                ->order('mc.like_count desc, mc.created_at desc') // 按点赞数和时间排序
                ->select()
                ->toArray();

            $commentList = [];
            foreach ($comments as $comment) {
                // 检查当前用户是否点赞了这条评论
                $isLiked = false;
                if ($currentUserId) {
                    $likeRecord = Db::name('major_comment_likes')
                        ->where('comment_id', $comment['id'])
                        ->where('user_id', $currentUserId)
                        ->find();
                    $isLiked = !empty($likeRecord);
                }

                // 获取回复
                $replies = Db::name('major_comments')
                    ->alias('mc')
                    ->leftJoin('user u', 'mc.user_id = u.id')
                    ->leftJoin('user ru', 'mc.reply_to_user_id = ru.id')
                    ->where('mc.parent_id', $comment['id'])
                    ->where('mc.status', 1)
                    ->field('mc.id, mc.user_id, mc.content, mc.images, mc.like_count, mc.parent_id, mc.reply_to_user_id, mc.created_at, u.username, u.face_url, ru.username as reply_to_username')
                    ->order('mc.created_at asc')
                    ->select()
                    ->toArray();

                $replyList = [];
                foreach ($replies as $reply) {
                    // 检查当前用户是否点赞了这条回复
                    $replyIsLiked = false;
                    if ($currentUserId) {
                        $replyLikeRecord = Db::name('major_comment_likes')
                            ->where('comment_id', $reply['id'])
                            ->where('user_id', $currentUserId)
                            ->find();
                        $replyIsLiked = !empty($replyLikeRecord);
                    }

                    // 处理回复图片
                    $replyImages = [];
                    if (!empty($reply['images'])) {
                        $replyImages = json_decode($reply['images'], true) ?: [];
                    }

                    $replyList[] = [
                        'id' => $reply['id'],
                        'user' => [
                            'id' => $reply['user_id'],
                            'name' => $reply['username'] ?: '匿名用户',
                            'avatar' => $reply['face_url'] ?: '/images/default_avatar.png'
                        ],
                        'content' => $reply['content'],
                        'images' => $replyImages,
                        'time' => $reply['created_at'],
                        'likes' => intval($reply['like_count'] ?: 0),
                        'is_liked' => $replyIsLiked,
                        'parent_id' => $reply['parent_id'],
                        'reply_to_user_id' => $reply['reply_to_user_id'],
                        'reply_to_username' => $reply['reply_to_username']
                    ];
                }

                // 处理评论图片
                $commentImages = [];
                if (!empty($comment['images'])) {
                    $commentImages = json_decode($comment['images'], true) ?: [];
                }

                $commentList[] = [
                    'id' => $comment['id'],
                    'user' => [
                        'id' => $comment['user_id'],
                        'name' => $comment['username'] ?: '匿名用户',
                        'avatar' => $comment['face_url'] ?: '/images/default_avatar.png'
                    ],
                    'content' => $comment['content'],
                    'images' => $commentImages,
                    'time' => $comment['created_at'],
                    'likes' => intval($comment['like_count'] ?: 0),
                    'is_liked' => $isLiked,
                    'replies' => $replyList
                ];
            }

            // 将评论数据添加到专业信息中
            $majorInfo['comments'] = $commentList;

            return json(['error_code' => 0, 'msg' => '获取成功', 'data' => $majorInfo]);

        } catch (\Exception $e) {
            return json(['error_code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取相关专业推荐
     */
    public function related()
    {
        try {
            // 验证token
            $token = Request::header('token');
            if (!$token) {
                return json(['error_code' => 401, 'msg' => '缺少token']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
            }

            $id = Request::post('id', 0);
            $limit = intval(Request::post('limit', 5));

            if (empty($id)) {
                return json(['error_code' => 400, 'msg' => '缺少专业ID']);
            }

            // 获取当前专业信息
            $currentMajor = BuaaMajors::find($id);
            if (!$currentMajor) {
                return json(['error_code' => 404, 'msg' => '专业不存在']);
            }

            // 推荐同系的其他专业
            $relatedMajors = BuaaMajors::where([
                ['university_id', '=', 1],
                ['department_id', '=', $currentMajor->department_id],
                ['id', '<>', $id]
            ])
            ->limit($limit)
            ->select()
            ->toArray();

            // 如果同系专业不够，补充其他相关专业
            if (count($relatedMajors) < $limit) {
                $remaining = $limit - count($relatedMajors);
                $additionalMajors = BuaaMajors::where([
                    ['university_id', '=', 1],
                    ['department_id', '<>', $currentMajor->department_id],
                    ['id', '<>', $id]
                ])
                ->limit($remaining)
                ->select()
                ->toArray();

                $relatedMajors = array_merge($relatedMajors, $additionalMajors);
            }

            return json(['error_code' => 0, 'msg' => '获取成功', 'data' => $relatedMajors]);

        } catch (\Exception $e) {
            return json(['error_code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取专业介绍
     */
    private function getMajorDescription($majorName)
    {
        // 这里可以根据专业名称返回相应的介绍
        // 实际项目中可以从数据库或配置文件中获取
        return "本专业是北京航空航天大学的重点专业之一，致力于培养具有扎实理论基础和实践能力的高素质人才。";
    }

    /**
     * 获取培养目标
     */
    private function getTrainingGoal($majorName)
    {
        return "培养德智体美劳全面发展，具有扎实的专业基础知识、较强的实践能力和创新精神，能够在相关领域从事研究、设计、开发、管理等工作的高素质专门人才。";
    }

    /**
     * 获取主要课程
     */
    private function getMainCourses($majorName)
    {
        // 根据专业返回主要课程列表
        $courses = [
            "高等数学", "线性代数", "概率论与数理统计", "大学物理", 
            "专业基础课程", "专业核心课程", "实践教学环节"
        ];
        return $courses;
    }

    /**
     * 获取就业方向
     */
    private function getCareerDirection($majorName)
    {
        return "毕业生可在科研院所、高等院校、企事业单位等从事相关领域的研究、设计、开发、教学、管理等工作，也可继续攻读硕士、博士学位。";
    }

    /**
     * 获取学位类型
     */
    private function getDegreeType($majorName)
    {
        // 根据专业名称判断学位类型
        if (strpos($majorName, '工程') !== false || strpos($majorName, '技术') !== false) {
            return '工学学士';
        } elseif (strpos($majorName, '管理') !== false) {
            return '管理学学士';
        } elseif (strpos($majorName, '经济') !== false || strpos($majorName, '金融') !== false) {
            return '经济学学士';
        } elseif (strpos($majorName, '文学') !== false || strpos($majorName, '语') !== false) {
            return '文学学士';
        } elseif (strpos($majorName, '法学') !== false) {
            return '法学学士';
        } elseif (strpos($majorName, '艺术') !== false || strpos($majorName, '设计') !== false) {
            return '艺术学学士';
        } else {
            return '理学学士';
        }
    }

    /**
     * 修复专业评论点赞数
     */
    public function fixLikeCount()
    {
        try {
            // 验证token
            $token = Request::header('token');
            if (!$token) {
                return json(['error_code' => 401, 'msg' => '缺少token']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 修复NULL值
            Db::execute("UPDATE major_comments SET like_count = 0 WHERE like_count IS NULL");
            Db::execute("UPDATE major_comments SET reply_count = 0 WHERE reply_count IS NULL");

            // 重新计算所有评论的点赞数
            $comments = Db::name('major_comments')->select();
            $fixedCount = 0;

            foreach ($comments as $comment) {
                $actualLikeCount = Db::name('major_comment_likes')
                    ->where('comment_id', $comment['id'])
                    ->count();

                if ($actualLikeCount != $comment['like_count']) {
                    Db::name('major_comments')
                        ->where('id', $comment['id'])
                        ->update(['like_count' => $actualLikeCount]);
                    $fixedCount++;
                }
            }

            return json([
                'error_code' => 0,
                'msg' => '修复完成',
                'data' => [
                    'total_comments' => count($comments),
                    'fixed_count' => $fixedCount
                ]
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 500, 'msg' => '修复失败: ' . $e->getMessage()]);
        }
    }
}
