<?php
namespace app\controller;

use app\BaseController;
use think\Request;
use think\response\Json;

class BanxuexingTest extends BaseController
{
    /**
     * 测试接口
     */
    public function test(Request $request): <PERSON>son
    {
        return json([
            'error_code' => 0,
            'msg' => '伴学星API测试成功',
            'data' => [
                'time' => date('Y-m-d H:i:s'),
                'method' => $request->method(),
                'url' => $request->url()
            ]
        ]);
    }
}
