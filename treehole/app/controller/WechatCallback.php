<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;

class WechatCallback extends BaseController
{
    // 微信公众号配置
    private $token = 'bjgxwechattoken2024';
    private $appid = 'wxc768bd42eb3be286';  // 你的公众号appid
    private $appsecret = '2ce4c6d0394f8ea77f98edcfb2222760';  // 你的公众号appsecret
    private $encodingAESKey = 'LltfNjDgbVrmz9tmDPKRrOA8376I5E44HSozsedgCQP';  // 你的EncodingAESKey


    /**
     * 微信公众号消息处理入口
     * GET: 验证服务器
     * POST: 处理消息和事件
     */
    public function index(Request $request)
    {
        if ($request->isGet()) {
            // 验证服务器
            return $this->verifyServer($request);
        } else {
            // 处理消息和事件
            return $this->handleMessage($request);
        }
    }

    /**
     * 验证微信服务器
     */
    private function verifyServer(Request $request)
    {
        $signature = $request->get('signature');
        $timestamp = $request->get('timestamp');
        $nonce = $request->get('nonce');
        $echostr = $request->get('echostr');

        // 验证签名
        if ($this->checkSignature($signature, $timestamp, $nonce)) {
            return response($echostr);
        } else {
            return response('验证失败');
        }
    }

    /**
     * 检查签名
     */
    private function checkSignature($signature, $timestamp, $nonce)
    {
        $tmpArr = array($this->token, $timestamp, $nonce);
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        return $tmpStr === $signature;
    }

    /**
     * 处理微信消息和事件
     */
    private function handleMessage(Request $request)
    {
        // 获取微信推送的XML数据
        $postStr = file_get_contents("php://input");

        // 记录接收到的数据用于调试
        file_put_contents(root_path() . 'runtime/wechat_debug.log',
            date('Y-m-d H:i:s') . " 接收数据: " . $postStr . "\n", FILE_APPEND);

        if (empty($postStr)) {
            return response('');
        }

        // 解析XML
        $postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);

        if (!$postObj) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " XML解析失败\n", FILE_APPEND);
            return response('');
        }

        // 检查是否是加密消息
        if (isset($postObj->Encrypt)) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 检测到加密消息，开始解密\n", FILE_APPEND);

            // 验证消息签名
            $signature = $request->get('msg_signature');
            $timestamp = $request->get('timestamp');
            $nonce = $request->get('nonce');
            $encrypt = trim($postObj->Encrypt);

            if (!$this->checkMsgSignature($signature, $timestamp, $nonce, $encrypt)) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 消息签名验证失败\n", FILE_APPEND);
                return response('');
            }

            // 解密消息
            $decryptedXml = $this->decryptMessage($encrypt);
            if (!$decryptedXml) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 消息解密失败\n", FILE_APPEND);
                return response('');
            }

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 解密后的消息: " . $decryptedXml . "\n", FILE_APPEND);

            // 重新解析解密后的XML
            $postObj = simplexml_load_string($decryptedXml, 'SimpleXMLElement', LIBXML_NOCDATA);
            if (!$postObj) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 解密后XML解析失败\n", FILE_APPEND);
                return response('');
            }
        }

        $msgType = trim($postObj->MsgType);
        $fromUser = trim($postObj->FromUserName); // 用户的openid
        $toUser = trim($postObj->ToUserName);     // 公众号的原始ID

        file_put_contents(root_path() . 'runtime/wechat_debug.log',
            date('Y-m-d H:i:s') . " 消息类型: {$msgType}, 来自: {$fromUser}\n", FILE_APPEND);

        // 处理不同类型的消息
        switch ($msgType) {
            case 'event':
                return $this->handleEvent($postObj, $fromUser, $toUser);
            case 'text':
                return $this->handleTextMessage($postObj, $fromUser, $toUser);
            default:
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 未处理的消息类型: {$msgType}\n", FILE_APPEND);
                return response('');
        }
    }

    /**
     * 处理事件消息
     */
    private function handleEvent($postObj, $fromUser, $toUser)
    {
        $event = trim($postObj->Event);

        file_put_contents(root_path() . 'runtime/wechat_debug.log',
            date('Y-m-d H:i:s') . " 事件类型: {$event}, 来自: {$fromUser}\n", FILE_APPEND);

        switch ($event) {
            case 'subscribe':
                // 用户关注事件
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 处理关注事件开始\n", FILE_APPEND);
                $this->handleSubscribe($fromUser);
                return $this->replyTextMessage($fromUser, $toUser, "欢迎关注北航高校社区！\n\n您的账号已与小程序关联，可以接收相关通知消息。");

            case 'unsubscribe':
                // 用户取消关注事件
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 处理取消关注事件\n", FILE_APPEND);
                $this->handleUnsubscribe($fromUser);
                return response('');

            default:
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 未处理的事件类型: {$event}\n", FILE_APPEND);
                return response('');
        }
    }

    /**
     * 处理文本消息
     */
    private function handleTextMessage($postObj, $fromUser, $toUser)
    {
        $content = trim($postObj->Content);
        
        // 简单的自动回复
        $replyText = "感谢您的消息！\n\n如需帮助，请在小程序中联系客服。";
        
        return $this->replyTextMessage($fromUser, $toUser, $replyText);
    }

    /**
     * 处理用户关注事件
     */
    private function handleSubscribe($officialOpenid)
    {
        try {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 开始处理关注事件，公众号openid: {$officialOpenid}\n", FILE_APPEND);

            // 获取用户信息，尝试获取unionid
            $userInfo = $this->getUserInfo($officialOpenid);

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 获取用户信息: " . json_encode($userInfo) . "\n", FILE_APPEND);

            if ($userInfo && isset($userInfo['unionid']) && !empty($userInfo['unionid'])) {
                $unionid = $userInfo['unionid'];

                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 获取到unionid: {$unionid}\n", FILE_APPEND);

                // 查找是否已有小程序的映射关系
                $existingMapping = Db::name('user_mapping')
                    ->where('unionid', $unionid)
                    ->find();

                if ($existingMapping) {
                    // 更新映射关系，添加公众号openid
                    $result = Db::name('user_mapping')
                        ->where('unionid', $unionid)
                        ->update([
                            'official_openid' => $officialOpenid,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    file_put_contents(root_path() . 'runtime/wechat_debug.log',
                        date('Y-m-d H:i:s') . " 更新现有映射关系，结果: {$result}\n", FILE_APPEND);
                } else {
                    // 创建新的映射关系（只有公众号信息，等待小程序添加）
                    $result = Db::name('user_mapping')->insert([
                        'official_openid' => $officialOpenid,
                        'unionid' => $unionid,
                        'miniprogram_openid' => '', // 暂时为空，等待小程序登录时填充
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    file_put_contents(root_path() . 'runtime/wechat_debug.log',
                        date('Y-m-d H:i:s') . " 创建新映射关系，结果: {$result}\n", FILE_APPEND);
                }
            } else {
                // 没有获取到unionid，可能是小程序和公众号没有绑定到同一个开放平台
                // 先创建一个临时记录，只保存公众号openid
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 未获取到unionid，创建临时记录\n", FILE_APPEND);

                // 检查是否已存在该公众号openid的记录
                $existingRecord = Db::name('user_mapping')
                    ->where('official_openid', $officialOpenid)
                    ->find();

                if (!$existingRecord) {
                    // 创建临时记录，等待后续通过其他方式关联
                    $result = Db::name('user_mapping')->insert([
                        'official_openid' => $officialOpenid,
                        'unionid' => '', // 暂时为空
                        'miniprogram_openid' => '', // 暂时为空
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    file_put_contents(root_path() . 'runtime/wechat_debug.log',
                        date('Y-m-d H:i:s') . " 创建临时记录，结果: {$result}\n", FILE_APPEND);
                }
            }
        } catch (\Exception $e) {
            // 记录错误但不影响用户体验
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 处理关注事件异常: " . $e->getMessage() . "\n", FILE_APPEND);
        }
    }

    /**
     * 处理用户取消关注事件
     */
    private function handleUnsubscribe($officialOpenid)
    {
        try {
            // 清除公众号openid，但保留映射关系
            Db::name('user_mapping')
                ->where('official_openid', $officialOpenid)
                ->update([
                    'official_openid' => null,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } catch (\Exception $e) {
            trace('处理取消关注事件失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取用户信息
     */
    private function getUserInfo($openid)
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 获取access_token失败\n", FILE_APPEND);
                return null;
            }

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 获取到access_token: {$accessToken}\n", FILE_APPEND);

            $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$accessToken}&openid={$openid}&lang=zh_CN";
            $response = file_get_contents($url);

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 用户信息API响应: {$response}\n", FILE_APPEND);

            $result = json_decode($response, true);

            if (isset($result['errcode'])) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 获取用户信息失败: {$result['errmsg']}\n", FILE_APPEND);
                return null;
            }

            return $result;
        } catch (\Exception $e) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 获取用户信息异常: " . $e->getMessage() . "\n", FILE_APPEND);
            return null;
        }
    }

    /**
     * 获取access_token
     */
    private function getAccessToken()
    {
        try {
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appsecret}";
            $response = file_get_contents($url);

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " access_token API响应: {$response}\n", FILE_APPEND);

            $result = json_decode($response, true);

            if (isset($result['access_token'])) {
                return $result['access_token'];
            }

            if (isset($result['errcode'])) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 获取access_token失败: {$result['errmsg']}\n", FILE_APPEND);
            }

            return null;
        } catch (\Exception $e) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 获取access_token异常: " . $e->getMessage() . "\n", FILE_APPEND);
            return null;
        }
    }

    /**
     * 回复文本消息
     */
    private function replyTextMessage($fromUser, $toUser, $content)
    {
        $time = time();
        $textTpl = "<xml>
            <ToUserName><![CDATA[%s]]></ToUserName>
            <FromUserName><![CDATA[%s]]></FromUserName>
            <CreateTime>%s</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[%s]]></Content>
        </xml>";

        $resultStr = sprintf($textTpl, $fromUser, $toUser, $time, $content);
        return response($resultStr)->contentType('application/xml');
    }

    /**
     * 检查消息签名（安全模式）
     */
    private function checkMsgSignature($signature, $timestamp, $nonce, $encrypt)
    {
        $tmpArr = array($this->token, $timestamp, $nonce, $encrypt);
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        return $tmpStr === $signature;
    }

    /**
     * 解密消息
     */
    private function decryptMessage($encrypt)
    {
        try {
            // 生成AESKey
            $aesKey = base64_decode($this->encodingAESKey . '=');

            // Base64解码加密数据
            $encryptData = base64_decode($encrypt);

            // AES解密
            $decrypted = openssl_decrypt($encryptData, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, substr($aesKey, 0, 16));

            if ($decrypted === false) {
                return false;
            }

            // 解析解密后的数据
            // 格式：random(16B) + msg_len(4B) + msg + appid
            $random = substr($decrypted, 0, 16);
            $msgLen = unpack('N', substr($decrypted, 16, 4))[1];
            $msg = substr($decrypted, 20, $msgLen);
            $appid = substr($decrypted, 20 + $msgLen);

            // 验证appid
            if ($appid !== $this->appid) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " AppID验证失败: 期望{$this->appid}, 实际{$appid}\n", FILE_APPEND);
                return false;
            }

            return $msg;

        } catch (\Exception $e) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 解密异常: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 同步所有已关注用户的映射关系
     * 访问: /wechatCallback/syncUsers
     */
    public function syncUsers()
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return json(['error_code' => 1, 'msg' => '获取access_token失败']);
            }

            // 获取关注用户列表
            $userList = $this->getFollowersList($accessToken);
            if (!$userList) {
                return json(['error_code' => 2, 'msg' => '获取用户列表失败']);
            }

            $successCount = 0;
            $errorCount = 0;
            $logs = [];

            foreach ($userList as $openid) {
                try {
                    // 获取用户详细信息
                    $userInfo = $this->getUserInfoByOpenid($accessToken, $openid);

                    if ($userInfo && isset($userInfo['unionid']) && !empty($userInfo['unionid'])) {
                        $unionid = $userInfo['unionid'];

                        // 查找是否已有映射关系
                        $existingMapping = Db::name('user_mapping')
                            ->where('unionid', $unionid)
                            ->find();

                        if ($existingMapping) {
                            // 更新现有映射关系
                            Db::name('user_mapping')
                                ->where('unionid', $unionid)
                                ->update([
                                    'official_openid' => $openid,
                                    'updated_at' => date('Y-m-d H:i:s')
                                ]);
                            $logs[] = "更新用户 {$openid} 的映射关系";
                        } else {
                            // 创建新的映射关系
                            Db::name('user_mapping')->insert([
                                'official_openid' => $openid,
                                'unionid' => $unionid,
                                'miniprogram_openid' => '',
                                'created_at' => date('Y-m-d H:i:s')
                            ]);
                            $logs[] = "创建用户 {$openid} 的映射关系";
                        }
                        $successCount++;
                    } else {
                        $logs[] = "用户 {$openid} 没有unionid";
                        $errorCount++;
                    }
                } catch (\Exception $e) {
                    $logs[] = "处理用户 {$openid} 失败: " . $e->getMessage();
                    $errorCount++;
                }
            }

            return json([
                'error_code' => 0,
                'msg' => '同步完成',
                'data' => [
                    'total' => count($userList),
                    'success' => $successCount,
                    'error' => $errorCount,
                    'logs' => $logs
                ]
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 3, 'msg' => '同步失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取关注用户列表
     */
    private function getFollowersList($accessToken)
    {
        try {
            $url = "https://api.weixin.qq.com/cgi-bin/user/get?access_token={$accessToken}";
            $response = file_get_contents($url);
            $result = json_decode($response, true);

            if (isset($result['errcode'])) {
                return null;
            }

            return $result['data']['openid'] ?? [];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 根据openid获取用户信息
     */
    private function getUserInfoByOpenid($accessToken, $openid)
    {
        try {
            $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$accessToken}&openid={$openid}&lang=zh_CN";
            $response = file_get_contents($url);
            $result = json_decode($response, true);

            if (isset($result['errcode'])) {
                return null;
            }

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }
}
