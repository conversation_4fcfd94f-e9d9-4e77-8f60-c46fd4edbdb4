<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;

class BuaaProxy extends BaseController
{

    /**
     * 获取北航系统Cookie
     * @param Request $request
     * @return Json
     */
    public function getBuaaCookies(Request $request): Json
    {
        // 验证token - 支持多种获取方式
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        try {
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData) {
                return json(['error_code' => 2, 'msg' => 'token验证失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => 'token验证失败: ' . $e->getMessage()]);
        }

        $username = $request->post('username');
        $password = $request->post('password');

        if (!$username || !$password) {
            return json(['error_code' => 3, 'msg' => '缺少用户名或密码']);
        }

        try {
            // 获取Cookie
            $cookies = $this->loginAndGetCookies($username, $password);
            
            if ($cookies) {
                return json([
                    'error_code' => 0, 
                    'msg' => '获取Cookie成功',
                    'cookies' => $cookies
                ]);
            } else {
                return json([
                    'error_code' => 4,
                    'msg' => '登录失败，请检查用户名密码'
                ]);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 5, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 使用获取到的Cookie查询成绩
     * @param Request $request
     * @return Json
     */
    public function getScoreData(Request $request): Json
    {
        // 验证token - 支持多种获取方式
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        try {
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData) {
                return json(['error_code' => 2, 'msg' => 'token验证失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => 'token验证失败: ' . $e->getMessage()]);
        }

        $cookies = $request->post('cookies');
        $xq = $request->post('xq', '1');
        $year = $request->post('year', '2024-2025');

        if (!$cookies) {
            return json(['error_code' => 3, 'msg' => '缺少Cookie信息']);
        }

        try {
            $scoreData = $this->requestScoreData($cookies, $xq, $year);
            
            if ($scoreData) {
                return json([
                    'error_code' => 0,
                    'msg' => '获取成绩成功',
                    'data' => $scoreData
                ]);
            } else {
                return json(['error_code' => 4, 'msg' => '获取成绩失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 5, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 仅获取GPA信息
     * @param Request $request
     * @return Json
     */
    public function getGpaOnly(Request $request): Json
    {
        // 验证token
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        try {
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData) {
                return json(['error_code' => 2, 'msg' => 'token验证失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => 'token验证失败: ' . $e->getMessage()]);
        }

        $username = $request->post('username');
        $password = $request->post('password');

        if (!$username || !$password) {
            return json(['error_code' => 3, 'msg' => '缺少用户名或密码']);
        }

        try {
            // 获取Cookie
            $cookies = $this->loginAndGetCookies($username, $password);

            if (!$cookies) {
                return json([
                    'error_code' => 4,
                    'msg' => '登录失败，请检查用户名密码'
                ]);
            }

            // 仅获取用户信息（包含GPA）
            $userInfo = $this->extractUserInfoFromMainPage($cookies);

            if (isset($userInfo['error'])) {
                return json(['error_code' => 5, 'msg' => '获取GPA失败: ' . $userInfo['error']]);
            }

            return json([
                'error_code' => 0,
                'msg' => '获取GPA成功',
                'data' => $userInfo,
                'cookies' => $cookies
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 6, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 快速获取成绩（仅查询成绩，不获取GPA）
     * @param Request $request
     * @return Json
     */
    public function getScoreQuick(Request $request): Json
    {
        // 验证token - 支持多种获取方式
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        try {
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData) {
                return json(['error_code' => 2, 'msg' => 'token验证失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => 'token验证失败: ' . $e->getMessage()]);
        }

        $username = $request->post('username');
        $password = $request->post('password');
        $xq = $request->post('xq', '1');
        $year = $request->post('year', '2024-2025');

        if (!$username || !$password) {
            return json(['error_code' => 3, 'msg' => '缺少用户名或密码']);
        }

        try {
            // 1. 获取Cookie
            $cookies = $this->loginAndGetCookies($username, $password);

            if (!$cookies) {
                return json([
                    'error_code' => 4,
                    'msg' => '登录失败，请检查用户名密码'
                ]);
            }

            // 2. 仅查询成绩数据，不获取GPA
            $scoreData = $this->requestScoreData($cookies, $xq, $year);

            if ($scoreData) {
                return json([
                    'error_code' => 0,
                    'msg' => '获取成绩成功',
                    'data' => $scoreData,
                    'cookies' => $cookies // 返回cookies供后续使用
                ]);
            } else {
                return json(['error_code' => 5, 'msg' => '获取成绩失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 6, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 一键获取成绩（登录+查询+GPA）
     * @param Request $request
     * @return Json
     */
    public function getScoreOneStep(Request $request): Json
    {
        // 验证token - 支持多种获取方式
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        try {
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData) {
                return json(['error_code' => 2, 'msg' => 'token验证失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => 'token验证失败: ' . $e->getMessage()]);
        }

        $username = $request->post('username');
        $password = $request->post('password');
        $xq = $request->post('xq', '1');
        $year = $request->post('year', '2024-2025');

        if (!$username || !$password) {
            return json(['error_code' => 3, 'msg' => '缺少用户名或密码']);
        }

        try {
            // 1. 获取Cookie
            $cookies = $this->loginAndGetCookies($username, $password);

            if (!$cookies) {
                return json([
                    'error_code' => 4,
                    'msg' => '登录失败，请检查用户名密码'
                ]);
            }

            // 2. 检查是否需要获取用户信息（GPA缓存优化）
            $userInfo = $this->getCachedOrFreshUserInfo($username, $cookies);

            // 3. 使用Cookie查询成绩
            $scoreData = $this->requestScoreData($cookies, $xq, $year);

            if ($scoreData) {
                return json([
                    'error_code' => 0,
                    'msg' => '获取成绩成功',
                    'data' => $scoreData,
                    'user_info' => $userInfo, // 添加用户信息
                    'cookies' => $cookies // 同时返回cookies供后续使用
                ]);
            } else {
                return json(['error_code' => 5, 'msg' => '获取成绩失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 6, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 模拟登录获取Cookie
     * @param string $username
     * @param string $password
     * @return array|false
     */
    private function loginAndGetCookies($username, $password)
    {
        try {
            // 第一步：获取登录页面
            $serviceUrl = 'https://app.buaa.edu.cn/a_buaa/api/cas/index?redirect=https%3A%2F%2Fapp.buaa.edu.cn%2Fbuaascore%2Fwap%2Fdefault%2Findex&from=wap&login_from=';
            $loginPageUrl = 'https://sso.buaa.edu.cn/login?service=' . urlencode($serviceUrl);

            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language: zh-CN,zh-Hans;q=0.9'
                    ],
                    'timeout' => 30
                ]
            ]);

            $loginPageContent = file_get_contents($loginPageUrl, false, $context);

            if (!$loginPageContent) {
                return false;
            }

            // 解析响应头获取初始Cookie
            $initialCookies = $this->parseCookiesFromHeaders($http_response_header ?? []);

            // 解析登录页面中的隐藏字段
            $hiddenFields = $this->parseHiddenFields($loginPageContent);

            // 第二步：提交登录表单
            $loginFormData = array_merge([
                'username' => $username,
                'password' => $password,
                'service' => $serviceUrl
            ], $hiddenFields);

            $loginData = http_build_query($loginFormData);

            $loginContext = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Origin: https://sso.buaa.edu.cn',
                        'Referer: ' . $loginPageUrl,
                        'Cookie: ' . $this->formatCookies($initialCookies)
                    ],
                    'content' => $loginData,
                    'timeout' => 30,
                    'follow_location' => 0 // 不自动跟随重定向
                ]
            ]);

            $loginResponse = file_get_contents('https://sso.buaa.edu.cn/login', false, $loginContext);

            // 检查是否登录成功（通过检查重定向）
            $responseHeaders = $http_response_header ?? [];
            $location = $this->getLocationFromHeaders($responseHeaders);

            if (!$location || strpos($location, 'ticket=') === false) {
                return false;
            }

            // 第三步：跟随重定向获取最终Cookie
            $finalCookies = $this->followRedirectsAndGetFinalCookies($location, $initialCookies);
            return $finalCookies;

        } catch (\Exception $e) {
            return false;
        }
    }



    /**
     * 解析登录页面中的隐藏字段
     */
    private function parseHiddenFields($html)
    {
        $hiddenFields = [];

        // 方法1: 匹配所有隐藏的input字段
        preg_match_all('/<input[^>]*type=["\']hidden["\'][^>]*>/i', $html, $matches);

        foreach ($matches[0] as $input) {
            // 提取name和value属性
            if (preg_match('/name=["\']([^"\']*)["\']/', $input, $nameMatch) &&
                preg_match('/value=["\']([^"\']*)["\']/', $input, $valueMatch)) {
                $hiddenFields[$nameMatch[1]] = $valueMatch[1];
            }
        }

        // 方法2: 专门匹配execution字段（支持多种格式）
        $executionPatterns = [
            '/name=["\']execution["\'][^>]*value=["\']([^"\']*)["\']/',
            '/value=["\']([^"\']*)["\'][^>]*name=["\']execution["\']/',
            '/<input[^>]*name=["\']execution["\'][^>]*value=["\']([^"\']*)["\'][^>]*>/',
            '/<input[^>]*value=["\']([^"\']*)["\'][^>]*name=["\']execution["\'][^>]*>/'
        ];

        foreach ($executionPatterns as $pattern) {
            if (preg_match($pattern, $html, $execMatch)) {
                $hiddenFields['execution'] = $execMatch[1];
                break;
            }
        }

        // 方法3: 匹配_eventId字段
        $eventIdPatterns = [
            '/name=["\']_eventId["\'][^>]*value=["\']([^"\']*)["\']/',
            '/value=["\']([^"\']*)["\'][^>]*name=["\']_eventId["\']/'
        ];

        foreach ($eventIdPatterns as $pattern) {
            if (preg_match($pattern, $html, $eventMatch)) {
                $hiddenFields['_eventId'] = $eventMatch[1];
                break;
            }
        }

        // 方法4: 匹配lt字段
        $ltPatterns = [
            '/name=["\']lt["\'][^>]*value=["\']([^"\']*)["\']/',
            '/value=["\']([^"\']*)["\'][^>]*name=["\']lt["\']/'
        ];

        foreach ($ltPatterns as $pattern) {
            if (preg_match($pattern, $html, $ltMatch)) {
                $hiddenFields['lt'] = $ltMatch[1];
                break;
            }
        }

        // 如果还是没有找到execution，尝试更宽泛的搜索
        if (!isset($hiddenFields['execution'])) {
            if (preg_match('/execution["\'][^>]*value=["\']([^"\']*)["\']/', $html, $execMatch)) {
                $hiddenFields['execution'] = $execMatch[1];
            }
        }

        return $hiddenFields;
    }



    /**
     * 跟随重定向获取最终Cookie
     */
    private function followRedirectsAndGetFinalCookies($location, $initialCookies)
    {
        $cookies = $initialCookies;
        $maxRedirects = 5;
        $redirectCount = 0;

        while ($location && $redirectCount < $maxRedirects) {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Cookie: ' . $this->formatCookies($cookies)
                    ],
                    'timeout' => 30,
                    'follow_location' => 0
                ]
            ]);

            $response = file_get_contents($location, false, $context);
            $responseHeaders = $http_response_header ?? [];
            
            // 更新Cookie
            $newCookies = $this->parseCookiesFromHeaders($responseHeaders);
            $cookies = array_merge($cookies, $newCookies);
            
            // 检查是否还有重定向
            $location = $this->getLocationFromHeaders($responseHeaders);
            $redirectCount++;
        }

        return $cookies;
    }

    /**
     * 使用Cookie请求成绩数据
     */
    private function requestScoreData($cookies, $xq, $year)
    {
        $scoreUrl = 'https://app.buaa.edu.cn/buaascore/wap/default/index';
        
        $postData = http_build_query([
            'xq' => $xq,
            'year' => $year
        ]);

        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
                    'Accept: application/json, text/javascript, */*; q=0.01',
                    'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
                    'X-Requested-With: XMLHttpRequest',
                    'Origin: https://app.buaa.edu.cn',
                    'Referer: https://app.buaa.edu.cn/buaascore/wap/default/index',
                    'Cookie: ' . $this->formatCookies($cookies)
                ],
                'content' => $postData,
                'timeout' => 30
            ]
        ]);

        $response = file_get_contents($scoreUrl, false, $context);
        
        if ($response) {
            return json_decode($response, true);
        }
        
        return false;
    }

    /**
     * 从响应头中解析Cookie
     */
    private function parseCookiesFromHeaders($headers)
    {
        $cookies = [];
        
        foreach ($headers as $header) {
            if (stripos($header, 'Set-Cookie:') === 0) {
                $cookieStr = substr($header, 11);
                $cookieParts = explode(';', $cookieStr);
                $cookieKeyValue = explode('=', trim($cookieParts[0]), 2);
                
                if (count($cookieKeyValue) === 2) {
                    $cookies[trim($cookieKeyValue[0])] = trim($cookieKeyValue[1]);
                }
            }
        }
        
        return $cookies;
    }

    /**
     * 从响应头中获取Location
     */
    private function getLocationFromHeaders($headers)
    {
        foreach ($headers as $header) {
            if (stripos($header, 'Location:') === 0) {
                return trim(substr($header, 9));
            }
        }
        return null;
    }

    /**
     * 格式化Cookie为字符串
     */
    private function formatCookies($cookies)
    {
        $cookieStrings = [];
        foreach ($cookies as $name => $value) {
            $cookieStrings[] = $name . '=' . $value;
        }
        return implode('; ', $cookieStrings);
    }





    /**
     * 获取缓存的或新鲜的用户信息（GPA缓存优化）
     */
    private function getCachedOrFreshUserInfo($username, $cookies)
    {
        // 生成缓存键
        $cacheKey = 'user_gpa_' . md5($username);

        // 尝试从缓存获取（这里使用简单的文件缓存，也可以用Redis等）
        $cacheFile = runtime_path() . 'cache/' . $cacheKey . '.json';
        $cacheExpiry = 24 * 60 * 60; // 24小时过期

        // 检查缓存是否存在且未过期
        if (file_exists($cacheFile)) {
            $cacheData = json_decode(file_get_contents($cacheFile), true);
            if ($cacheData && isset($cacheData['timestamp']) &&
                (time() - $cacheData['timestamp']) < $cacheExpiry) {
                // 缓存有效，直接返回
                return $cacheData['user_info'];
            }
        }

        // 缓存无效或不存在，重新获取
        $userInfo = $this->extractUserInfoFromMainPage($cookies);

        // 保存到缓存
        if (!isset($userInfo['error'])) {
            // 确保缓存目录存在
            $cacheDir = dirname($cacheFile);
            if (!is_dir($cacheDir)) {
                mkdir($cacheDir, 0755, true);
            }

            $cacheData = [
                'timestamp' => time(),
                'user_info' => $userInfo
            ];
            file_put_contents($cacheFile, json_encode($cacheData));
        }

        return $userInfo;
    }

    /**
     * 从主页面提取用户信息和GPA
     */
    private function extractUserInfoFromMainPage($cookies)
    {
        try {
            // 获取主页面内容
            $mainPageUrl = 'https://app.buaa.edu.cn/buaascore/wap/default/index';
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Cookie: ' . $this->formatCookies($cookies)
                    ],
                    'timeout' => 30
                ]
            ]);

            $content = file_get_contents($mainPageUrl, false, $context);
            if (!$content) {
                return ['error' => '无法获取页面内容'];
            }

            // 确保内容是UTF-8编码
            if (!mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', 'auto');
            }

            $userInfo = [];

            // 提取学工号
            if (preg_match('/学工号：([^<]+)/', $content, $matches)) {
                $userInfo['student_id'] = trim($matches[1]);
            }

            // 提取姓名
            if (preg_match('/<span class="name">([^<]+)<\/span>/', $content, $matches)) {
                $userInfo['name'] = trim($matches[1]);
            }

            // 提取学院和年级信息
            if (preg_match('/<span class="tit">([^<]+)<\/span>/', $content, $matches)) {
                $userInfo['college_grade'] = trim($matches[1]);
            }

            // 提取GPA值 - 从Vue.js的data中获取
            $userInfo['gpa'] = '0'; // 默认值
            if (preg_match('/gpa\s*:\s*["\']?([^"\'}\s,]+)["\']?/i', $content, $matches)) {
                $gpaValue = trim($matches[1]);
                if (is_numeric($gpaValue)) {
                    $userInfo['gpa'] = $gpaValue;
                }
            }

            // 如果没有找到GPA，尝试其他模式
            if ($userInfo['gpa'] === '0') {
                // 查找可能的GPA显示模式 - 更精确的匹配
                if (preg_match('/GPA成绩\s*([0-9]+(?:\.[0-9]+)?)/', $content, $matches)) {
                    $userInfo['gpa'] = $matches[1];
                } elseif (preg_match('/绩点\s*([0-9]+(?:\.[0-9]+)?)/', $content, $matches)) {
                    $userInfo['gpa'] = $matches[1];
                }
            }

            return $userInfo;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
