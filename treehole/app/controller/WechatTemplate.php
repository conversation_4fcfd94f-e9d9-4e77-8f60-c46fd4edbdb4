<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;

class WechatTemplate extends BaseController
{
    // 微信公众号配置
    private $appid = 'wxc768bd42eb3be286';
    private $appsecret = '2ce4c6d0394f8ea77f98edcfb2222760';
    private $miniprogramAppid = 'wx13d6e0dee303467f'; // 小程序appid

    /**
     * 发送模板消息
     * @param Request $request
     * @return Json
     */
    public function sendTemplate(Request $request): Json
    {
        $toUser = $request->post('to_user'); // 接收者openid或unionid
        $templateId = $request->post('template_id');
        $data = $request->post('data', []);
        $url = $request->post('url', '');
        $miniprogramPage = $request->post('miniprogram_page', '');
        $userType = $request->post('user_type', 'official'); // official: 公众号openid, unionid: 通过unionid查找

        if (!$toUser || !$templateId || empty($data)) {
            return json(['error_code' => 1, 'msg' => '参数不完整']);
        }

        try {
            // 根据用户类型获取公众号openid
            $officialOpenid = $this->getOfficialOpenid($toUser, $userType);
            if (!$officialOpenid) {
                return json(['error_code' => 2, 'msg' => '未找到用户的公众号openid']);
            }

            // 发送模板消息
            $result = $this->sendTemplateMessage($officialOpenid, $templateId, $data, $url, $miniprogramPage);
            
            if ($result['success']) {
                return json(['error_code' => 0, 'msg' => '发送成功', 'data' => $result['data']]);
            } else {
                return json(['error_code' => 3, 'msg' => '发送失败: ' . $result['error']]);
            }

        } catch (\Exception $e) {
            return json(['error_code' => 4, 'msg' => '发送异常: ' . $e->getMessage()]);
        }
    }

    /**
     * 发送意见反馈通知（专用接口）
     * 注意：只给管理员发送通知，与提交意见的用户是否关注公众号无关
     * @param Request $request
     * @return Json
     */
    public function sendFeedbackNotice(Request $request): Json
    {
        $userOpenid = $request->post('user_openid'); // 提交意见的用户openid（仅用于获取用户名）
        $content = $request->post('content', '');
        $category = $request->post('category', '意见反馈');

        if (!$userOpenid || !$content) {
            return json(['error_code' => 1, 'msg' => '参数不完整']);
        }

        try {
            // 获取用户信息（仅用于获取用户名，不需要用户关注公众号）
            $user = Db::name('user')->where('openid', $userOpenid)->find();
            $username = $user ? $user['username'] : '匿名用户';

            // 管理员openid（固定发送给管理员）
            $adminOpenid = 'oqqL27AacwJMWSfVpp5AAhs2XUyM';

            // 模板ID
            $templateId = 'NsNEKtbrL2NighG7hxolJPFu3vNWcpPbOZlCGD3Mmr0';

            // 模板数据
            $templateData = [
                'thing11' => ['value' => $category],
                'thing9' => ['value' => $username . '反馈了意见：' . mb_substr($content, 0, 15) . (mb_strlen($content) > 15 ? '...' : '')]
            ];

            // 跳转到意见页面
            $miniprogramPage = 'pages/foldshare/yijian/yijian';

            // 发送模板消息（直接发送给管理员，不需要检查用户是否关注）
            $result = $this->sendTemplateMessage($adminOpenid, $templateId, $templateData, '', $miniprogramPage);

            if ($result['success']) {
                return json(['error_code' => 0, 'msg' => '通知发送成功']);
            } else {
                return json(['error_code' => 2, 'msg' => '通知发送失败: ' . $result['error']]);
            }

        } catch (\Exception $e) {
            return json(['error_code' => 3, 'msg' => '发送异常: ' . $e->getMessage()]);
        }
    }

    /**
     * 根据用户类型获取公众号openid
     */
    private function getOfficialOpenid($identifier, $userType)
    {
        if ($userType === 'official') {
            // 直接是公众号openid
            return $identifier;
        } elseif ($userType === 'unionid') {
            // 通过unionid查找公众号openid
            $mapping = Db::name('user_mapping')
                ->where('unionid', $identifier)
                ->find();
            return $mapping ? $mapping['official_openid'] : null;
        } elseif ($userType === 'miniprogram') {
            // 通过小程序openid查找公众号openid
            $mapping = Db::name('user_mapping')
                ->where('miniprogram_openid', $identifier)
                ->find();
            return $mapping ? $mapping['official_openid'] : null;
        }

        return null;
    }

    /**
     * 发送模板消息核心方法
     */
    private function sendTemplateMessage($toUser, $templateId, $data, $url = '', $miniprogramPage = '')
    {
        try {
            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return ['success' => false, 'error' => '获取access_token失败'];
            }

            // 构建请求数据
            $postData = [
                'touser' => $toUser,
                'template_id' => $templateId,
                'data' => $data
            ];

            // 添加跳转链接
            if ($url) {
                $postData['url'] = $url;
            }

            // 添加小程序跳转
            if ($miniprogramPage) {
                $postData['miniprogram'] = [
                    'appid' => $this->miniprogramAppid,
                    'pagepath' => $miniprogramPage
                ];
            }

            // 发送请求
            $apiUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$accessToken}";
            $response = $this->httpPost($apiUrl, json_encode($postData, JSON_UNESCAPED_UNICODE));
            $result = json_decode($response, true);

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                return ['success' => true, 'data' => $result];
            } else {
                return ['success' => false, 'error' => $result['errmsg'] ?? '未知错误'];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 获取access_token
     */
    private function getAccessToken()
    {
        try {
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appsecret}";
            $response = file_get_contents($url);
            $result = json_decode($response, true);

            return $result['access_token'] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        curl_close($ch);

        return $response;
    }

    /**
     * 静态方法：发送意见反馈通知
     * 用于在其他控制器中调用，避免构造函数问题
     * 注意：这个方法只给管理员发送通知，与提交意见的用户是否关注公众号无关
     */
    public static function sendFeedbackNotification($userOpenid, $content, $username)
    {
        try {
            // 微信公众号配置
            $appid = 'wxc768bd42eb3be286';
            $appsecret = '2ce4c6d0394f8ea77f98edcfb2222760';
            $miniprogramAppid = 'wx13d6e0dee303467f';

            // 管理员openid（固定发送给管理员，与提交用户是否关注无关）
            $adminOpenid = 'oqqL27AacwJMWSfVpp5AAhs2XUyM';

            // 获取access_token
            $tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
            $tokenResponse = file_get_contents($tokenUrl);
            $tokenResult = json_decode($tokenResponse, true);

            if (!isset($tokenResult['access_token'])) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 意见反馈-获取access_token失败: " . json_encode($tokenResult) . "\n", FILE_APPEND);
                return false;
            }

            $accessToken = $tokenResult['access_token'];

            // 构建模板消息数据（只发送给管理员）
            $templateData = [
                'touser' => $adminOpenid,
                'template_id' => 'NsNEKtbrL2NighG7hxolJPFu3vNWcpPbOZlCGD3Mmr0',
                'miniprogram' => [
                    'appid' => $miniprogramAppid,
                    'pagepath' => 'pages/foldshare/yijian/yijian'
                ],
                'data' => [
                    'thing11' => ['value' => '意见反馈'],
                    'thing9' => ['value' => $username . '反馈了意见：' . mb_substr($content, 0, 15) . (mb_strlen($content) > 15 ? '...' : '')]
                ]
            ];

            // 发送模板消息
            $apiUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$accessToken}";
            $postData = json_encode($templateData, JSON_UNESCAPED_UNICODE);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData)
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $response = curl_exec($ch);
            curl_close($ch);

            $result = json_decode($response, true);

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 意见反馈模板消息发送成功: " . json_encode($result) . "\n", FILE_APPEND);
                return true;
            } else {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 意见反馈模板消息发送失败: " . json_encode($result) . "\n", FILE_APPEND);
                return false;
            }

        } catch (\Exception $e) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 发送意见反馈通知异常: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 静态方法：发送评论/回复通知
     * 用于通知被评论或被回复的用户
     */
    public static function sendCommentNotification($targetUserOpenid, $fromUsername, $actionText, $content, $messageId = null)
    {
        try {
            // 微信公众号配置
            $appid = 'wxc768bd42eb3be286';
            $appsecret = '2ce4c6d0394f8ea77f98edcfb2222760';
            $miniprogramAppid = 'wx13d6e0dee303467f';

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 查找用户映射关系 - 小程序openid: " . $targetUserOpenid . "\n", FILE_APPEND);

            // 通过小程序openid查找公众号openid
            $mapping = Db::name('user_mapping')
                ->where('miniprogram_openid', $targetUserOpenid)
                ->find();

            if (!$mapping) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 未找到用户映射关系: " . $targetUserOpenid . "\n", FILE_APPEND);
                return false;
            }

            if (!$mapping['official_openid']) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 用户未关注公众号，无法发送模板消息: " . $targetUserOpenid . " (映射存在但official_openid为空)\n", FILE_APPEND);
                return false; // 用户未关注公众号，静默失败
            }

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 找到公众号openid: " . $mapping['official_openid'] . "\n", FILE_APPEND);

            $officialOpenid = $mapping['official_openid'];

            // 获取access_token
            $tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
            $tokenResponse = file_get_contents($tokenUrl);
            $tokenResult = json_decode($tokenResponse, true);

            if (!isset($tokenResult['access_token'])) {
                error_log("获取access_token失败: " . json_encode($tokenResult));
                return false;
            }

            $accessToken = $tokenResult['access_token'];

            // 根据页面类型构建跳转页面路径
            $pagePath = '';
            if ($pageType === 'window') {
                // 食堂窗口页面
                $pagePath = 'pages/window/detail/index';
                if ($messageId) {
                    $pagePath .= '?id=' . $messageId;
                }
            } else {
                // 默认为了然几分消息页面
                $message = Db::name('message')->where('id', $messageId)->find();
                $authorUserId = $message ? $message['user_id'] : '';

                $pagePath = 'packageEmoji/pages/messageDetail/messageDetail';
                if ($messageId) {
                    $pagePath .= '?id=' . $messageId;
                }
            }

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 构建的页面路径: " . $pagePath . " (长度: " . strlen($pagePath) . ")\n", FILE_APPEND);

            // 构建模板消息数据
            // 第一行：用户名 + 动作文本（thing11字段）
            $thing11Value = $fromUsername . $actionText;
            // 确保thing11字段不超过20个字符且不包含特殊字符
            $thing11Value = mb_substr($thing11Value, 0, 20);
            $thing11Value = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9]/u', '', $thing11Value);

            // 第二行：评论/回复的具体内容（thing9字段）
            $thing9Value = mb_substr($content, 0, 20);
            $thing9Value = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9]/u', '', $thing9Value);

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 模板消息数据 - thing11: " . $thing11Value . ", thing9: " . $thing9Value . "\n", FILE_APPEND);

            $templateData = [
                'touser' => $officialOpenid,
                'template_id' => 'NsNEKtbrL2NighG7hxolJPFu3vNWcpPbOZlCGD3Mmr0',
                'miniprogram' => [
                    'appid' => $miniprogramAppid,
                    'pagepath' => $pagePath
                ],
                'data' => [
                    'thing11' => ['value' => $thing11Value],
                    'thing9' => ['value' => $thing9Value]
                ]
            ];

            // 发送模板消息
            $apiUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$accessToken}";
            $postData = json_encode($templateData, JSON_UNESCAPED_UNICODE);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData)
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $response = curl_exec($ch);
            curl_close($ch);

            $result = json_decode($response, true);

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 评论回复模板消息发送成功: " . json_encode($result) . "\n", FILE_APPEND);
                return true;
            } else {
                // 特殊处理用户未关注的情况
                if (isset($result['errcode']) && $result['errcode'] == 43004) {
                    file_put_contents(root_path() . 'runtime/wechat_debug.log',
                        date('Y-m-d H:i:s') . " 用户未关注公众号，无法发送模板消息: " . $officialOpenid . "\n", FILE_APPEND);
                } else {
                    file_put_contents(root_path() . 'runtime/wechat_debug.log',
                        date('Y-m-d H:i:s') . " 评论回复模板消息发送失败: " . json_encode($result) . "\n", FILE_APPEND);
                }
                return false;
            }

        } catch (\Exception $e) {
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 发送评论回复通知异常: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 静态方法：发送评论/回复通知给指定用户
     * 用于在其他控制器中调用
     */
    public static function sendCommentNotificationToUser($targetUserId, $fromUsername, $actionText, $content, $messageId = null, $pageType = 'message')
    {
        try {
            // 添加调试日志
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " === 开始发送评论回复模板消息 ===\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 目标用户ID: " . $targetUserId . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 发送者用户名: " . $fromUsername . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 动作文本: " . $actionText . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 内容: " . mb_substr($content, 0, 50) . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 消息ID: " . $messageId . "\n", FILE_APPEND);

            // 获取目标用户的openid
            $targetUser = Db::name('user')->where('id', $targetUserId)->find();
            if (!$targetUser || !$targetUser['openid']) {
                file_put_contents(root_path() . 'runtime/wechat_debug.log',
                    date('Y-m-d H:i:s') . " 目标用户openid不存在: " . $targetUserId . "\n", FILE_APPEND);
                return false;
            }

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 目标用户openid: " . $targetUser['openid'] . "\n", FILE_APPEND);

            // 调用发送模板消息方法
            $result = self::sendCommentNotification(
                $targetUser['openid'],
                $fromUsername,
                $actionText,
                $content,
                $messageId
            );

            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 模板消息发送结果: " . ($result ? '成功' : '失败') . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " === 评论回复模板消息处理完成 ===\n", FILE_APPEND);

            return $result;

        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 发送评论回复通知异常: " . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents(root_path() . 'runtime/wechat_debug.log',
                date('Y-m-d H:i:s') . " 异常堆栈: " . $e->getTraceAsString() . "\n", FILE_APPEND);
            return false;
        }
    }
}
