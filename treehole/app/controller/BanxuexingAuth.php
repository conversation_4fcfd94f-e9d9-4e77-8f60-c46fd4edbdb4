<?php
declare(strict_types=1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Db;
use app\utils\BanxuexingJwtUtil;

class BanxuexingAuth
{
    /**
     * 微信登录
     */
    public function wxLogin(Request $request): Response
    {
        try {
            $code = $request->post('code', '');
            
            if (empty($code)) {
                return json(['code' => 400, 'msg' => '缺少code参数']);
            }
            
            // 微信小程序配置
            $appId = 'wx643c1ff21fe15e4f'; // 请替换为您的小程序AppID
            $appSecret = '940fbe26b6b028165a6cd27d49cf9117'; // 请替换为您的小程序AppSecret
            
            // 调用微信接口获取session_key和openid
            $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appId}&secret={$appSecret}&js_code={$code}&grant_type=authorization_code";
            $response = file_get_contents($url);
            $data = json_decode($response, true);
            
            if (isset($data['errcode'])) {
                return json(['code' => 400, 'msg' => '微信登录失败：' . $data['errmsg']]);
            }
            
            $openid = $data['openid'];
            $sessionKey = $data['session_key'];
            
            // 查询用户是否存在
            $user = Db::name('banxuexing_users')->where('openid', $openid)->find();
            
            if ($user) {
                // 用户已存在，直接登录
                $token = BanxuexingJwtUtil::createToken(['user_id' => $user['id']]);

                // 格式化用户ID为6位数字
                $formattedId = str_pad((string)$user['id'], 6, '0', STR_PAD_LEFT);

                return json([
                    'code' => 200,
                    'msg' => '登录成功',
                    'data' => [
                        'token' => $token,
                        'user_info' => [
                            'id' => $formattedId,
                            'nickname' => $user['nickname'],
                            'avatar' => $user['avatar'],
                            'is_teacher' => $user['is_teacher'],
                            'phone' => $user['phone'],
                            'real_name' => $user['real_name']
                        ],
                        'is_new_user' => false
                    ]
                ]);
            } else {
                // 新用户，需要注册
                return json([
                    'code' => 200,
                    'msg' => '新用户需要注册',
                    'data' => [
                        'openid' => $openid,
                        'session_key' => $sessionKey,
                        'is_new_user' => true
                    ]
                ]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
    
    /**
     * 自动登录（自动注册新用户）
     */
    public function autoLogin(Request $request): Response
    {
        try {
            $code = $request->post('code', '');

            if (empty($code)) {
                return json(['code' => 400, 'msg' => '缺少code参数']);
            }

            // 微信小程序配置
            $appId = 'wx643c1ff21fe15e4f'; // 请替换为您的小程序AppID
            $appSecret = '940fbe26b6b028165a6cd27d49cf9117'; // 请替换为您的小程序AppSecret

            // 检查是否配置了真实的微信小程序信息
            if ($appId === 'your_miniprogram_appid' || $appSecret === 'your_miniprogram_appsecret') {
                // 开发测试模式：使用模拟的openid
                $openid = 'test_openid_' . time();
            } else {
                // 调用微信接口获取session_key和openid
                $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appId}&secret={$appSecret}&js_code={$code}&grant_type=authorization_code";
                $response = file_get_contents($url);
                $data = json_decode($response, true);

                if (isset($data['errcode'])) {
                    return json(['code' => 400, 'msg' => '微信登录失败：' . $data['errmsg']]);
                }

                $openid = $data['openid'];
            }

            // 查询用户是否存在
            $user = Db::name('banxuexing_users')->where('openid', $openid)->find();

            if ($user) {
                // 用户已存在，直接登录
                $token = BanxuexingJwtUtil::createToken(['user_id' => $user['id']]);

                // 格式化用户ID为6位数字
                $formattedId = str_pad((string)$user['id'], 6, '0', STR_PAD_LEFT);

                return json([
                    'code' => 200,
                    'msg' => '登录成功',
                    'data' => [
                        'token' => $token,
                        'user_info' => [
                            'id' => $formattedId,
                            'nickname' => $user['nickname'],
                            'avatar' => $user['avatar'],
                            'is_teacher' => $user['is_teacher'],
                            'phone' => $user['phone'],
                            'real_name' => $user['real_name']
                        ],
                        'is_new_user' => false
                    ]
                ]);
            } else {
                // 新用户，自动注册
                // 生成默认用户名：学霸 + 6位随机数字
                $randomCode = str_pad((string)rand(0, 999999), 6, '0', STR_PAD_LEFT);
                $defaultNickname = '学霸' . $randomCode;

                // 默认头像（只存储相对路径，前端拼接域名）
                $defaultAvatar = '/touxiang/1.png';

                $userData = [
                    'openid' => $openid,
                    'nickname' => $defaultNickname,
                    'avatar' => $defaultAvatar,
                    'phone' => '', // 手机号暂时为空，后续可以完善
                    'real_name' => '', // 真实姓名暂时为空，后续可以完善
                    'gender' => 0, // 默认未知
                    'birth_date' => null,
                    'id_card' => '',
                    'city' => '',
                    'is_teacher' => 0, // 默认为学生，后续可以修改
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ];

                $userId = Db::name('banxuexing_users')->insertGetId($userData);

                // 生成token
                $token = BanxuexingJwtUtil::createToken(['user_id' => $userId]);

                // 格式化用户ID为6位数字
                $formattedId = str_pad((string)$userId, 6, '0', STR_PAD_LEFT);

                return json([
                    'code' => 200,
                    'msg' => '注册成功',
                    'data' => [
                        'token' => $token,
                        'user_info' => [
                            'id' => $formattedId,
                            'nickname' => $defaultNickname,
                            'avatar' => $defaultAvatar,
                            'is_teacher' => 0,
                            'phone' => '',
                            'real_name' => ''
                        ],
                        'is_new_user' => true
                    ]
                ]);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    /**
     * 用户注册
     */
    public function register(Request $request): Response
    {
        try {
            $openid = $request->post('openid', '');
            $nickname = $request->post('nickname', '');
            $avatar = $request->post('avatar', '');
            $phone = $request->post('phone', '');
            $realName = $request->post('real_name', '');
            $gender = $request->post('gender', 0);
            $birthDate = $request->post('birth_date', '');
            $idCard = $request->post('id_card', '');
            $city = $request->post('city', '');
            $isTeacher = $request->post('is_teacher', 0);
            
            // 验证必填字段
            if (empty($openid) || empty($nickname) || empty($phone) || empty($realName)) {
                return json(['code' => 400, 'msg' => '请填写完整信息']);
            }
            
            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                return json(['code' => 400, 'msg' => '手机号格式不正确']);
            }
            
            // 检查手机号是否已被使用
            $existUser = Db::name('banxuexing_users')->where('phone', $phone)->find();
            if ($existUser) {
                return json(['code' => 400, 'msg' => '该手机号已被注册']);
            }
            
            // 开启事务
            Db::startTrans();
            
            try {
                // 创建用户
                $userData = [
                    'openid' => $openid,
                    'nickname' => $nickname,
                    'avatar' => $avatar,
                    'phone' => $phone,
                    'real_name' => $realName,
                    'gender' => intval($gender),
                    'birth_date' => $birthDate ?: null,
                    'id_card' => $idCard,
                    'city' => $city,
                    'is_teacher' => intval($isTeacher),
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                $userId = Db::name('banxuexing_users')->insertGetId($userData);
                
                // 如果是老师，创建老师详细信息记录
                if ($isTeacher) {
                    $teacherData = $request->post('teacher_info', []);
                    if (!empty($teacherData)) {
                        $teacherDetailData = [
                            'user_id' => $userId,
                            'school' => $teacherData['school'] ?? '',
                            'major' => $teacherData['major'] ?? '',
                            'grade_level' => $teacherData['grade_level'] ?? '',
                            'education' => $teacherData['education'] ?? '',
                            'school_time' => $teacherData['school_time'] ?? '',
                            'textbook_version' => $teacherData['textbook_version'] ?? '',
                            'expected_salary' => $teacherData['expected_salary'] ?? '',
                            'teachable_subjects' => $teacherData['teachable_subjects'] ?? '',
                            'introduction' => $teacherData['introduction'] ?? '',
                            'certificates' => $teacherData['certificates'] ?? '',
                            'teachable_grades' => $teacherData['teachable_grades'] ?? '',
                            'available_schedule' => $teacherData['available_schedule'] ?? '',
                            'student_id_card' => $teacherData['student_id_card'] ?? '',
                            'score_card' => $teacherData['score_card'] ?? '',
                            'create_time' => time(),
                            'update_time' => time()
                        ];
                        
                        Db::name('banxuexing_teacher_details')->insert($teacherDetailData);
                    }
                }
                
                // 提交事务
                Db::commit();
                
                // 生成token
                $token = BanxuexingJwtUtil::createToken(['user_id' => $userId]);

                // 格式化用户ID为6位数字
                $formattedId = str_pad((string)$userId, 6, '0', STR_PAD_LEFT);

                return json([
                    'code' => 200,
                    'msg' => '注册成功',
                    'data' => [
                        'token' => $token,
                        'user_info' => [
                            'id' => $formattedId,
                            'nickname' => $nickname,
                            'avatar' => $avatar,
                            'is_teacher' => $isTeacher,
                            'phone' => $phone,
                            'real_name' => $realName
                        ]
                    ]
                ]);
                
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '注册失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request): Response
    {
        try {
            $token = $request->header('token', '');
            if (empty($token)) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }
            
            $payload = BanxuexingJwtUtil::verifyToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效']);
            }
            
            $userId = $payload['user_id'];
            $user = Db::name('banxuexing_users')->where('id', $userId)->find();
            
            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            
            // 格式化用户ID为6位数字
            $formattedId = str_pad((string)$user['id'], 6, '0', STR_PAD_LEFT);

            $userInfo = [
                'id' => $formattedId,
                'nickname' => $user['nickname'],
                'avatar' => $user['avatar'],
                'phone' => $user['phone'],
                'real_name' => $user['real_name'],
                'gender' => $user['gender'],
                'birth_date' => $user['birth_date'],
                'id_card' => $user['id_card'],
                'city' => $user['city'],
                'is_teacher' => $user['is_teacher'],
                'status' => $user['status']
            ];
            
            // 如果是老师，获取老师详细信息
            if ($user['is_teacher']) {
                $teacherDetail = Db::name('banxuexing_teacher_details')->where('user_id', $userId)->find();
                if ($teacherDetail) {
                    $userInfo['teacher_info'] = $teacherDetail;
                }
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $userInfo
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
}
