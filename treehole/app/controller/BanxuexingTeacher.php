<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;

class BanxuexingTeacher extends BaseController
{
    /**
     * 获取老师列表
     */
    public function getTeacherList(Request $request): Json
    {
        $filter = $request->post('filter', 'recommend'); // recommend, latest, nearby
        $page = $request->post('page', 1);
        $limit = $request->post('limit', 10);
        
        $query = Db::name('banxuexing_teachers')
            ->alias('t')
            ->leftJoin('banxuexing_users u', 't.user_id = u.id')
            ->field('t.*, u.nickname, u.avatar')
            ->where('t.status', 1); // 只显示审核通过的

        // 根据筛选条件排序
        switch ($filter) {
            case 'latest':
                $query->order('t.create_time', 'desc');
                break;
            case 'nearby':
                // TODO: 根据地理位置排序
                $query->order('t.create_time', 'desc');
                break;
            case 'recommend':
            default:
                $query->order('t.is_urgent', 'desc')
                      ->order('t.create_time', 'desc');
                break;
        }

        $total = $query->count();
        $list = $query->page($page, $limit)->select();

        // 处理数据格式
        $teacherList = [];
        foreach ($list as $item) {
            $teacherList[] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'description' => $item['description'],
                'username' => $item['nickname'] ?: '用户' . $item['user_id'],
                'avatar' => $item['avatar'] ?: '/images/default-avatar.png',
                'distance' => rand(1, 50), // TODO: 计算实际距离
                'area' => $item['area'],
                'subject' => $item['subject'],
                'grade' => $item['grade'],
                'gender' => $item['gender_requirement'],
                'time' => $item['available_time'],
                'minPrice' => $item['min_price'],
                'maxPrice' => $item['max_price'],
                'isUrgent' => $item['is_urgent'] == 1,
                'isRecruiting' => $item['status'] == 1
            ];
        }

        return json([
            'error_code' => 0,
            'msg' => '获取成功',
            'data' => [
                'list' => $teacherList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * 获取老师详情
     */
    public function getTeacherDetail(Request $request): Json
    {
        $teacherId = $request->post('teacher_id');
        if (!$teacherId) {
            return json(['error_code' => 1, 'msg' => '缺少老师ID']);
        }

        $teacher = Db::name('banxuexing_teachers')
            ->alias('t')
            ->leftJoin('banxuexing_users u', 't.user_id = u.id')
            ->field('t.*, u.nickname, u.avatar, u.phone')
            ->where('t.id', $teacherId)
            ->find();

        if (!$teacher) {
            return json(['error_code' => 2, 'msg' => '老师信息不存在']);
        }

        return json([
            'error_code' => 0,
            'msg' => '获取成功',
            'data' => $teacher
        ]);
    }

    /**
     * 发布老师信息
     */
    public function publishTeacher(Request $request): Json
    {
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        $payload = JwtUtil::verifyToken($token);
        if (!$payload) {
            return json(['error_code' => 2, 'msg' => 'token无效']);
        }

        $userId = $payload['user_id'];
        
        // 获取表单数据
        $data = [
            'user_id' => $userId,
            'title' => $request->post('title'),
            'description' => $request->post('description'),
            'subject' => $request->post('subject'),
            'grade' => $request->post('grade'),
            'gender_requirement' => $request->post('gender_requirement'),
            'area' => $request->post('area'),
            'min_price' => $request->post('min_price'),
            'max_price' => $request->post('max_price'),
            'available_time' => $request->post('available_time'),
            'is_urgent' => $request->post('is_urgent', 0),
            'status' => 0, // 待审核
            'create_time' => time(),
            'update_time' => time()
        ];

        // 验证必填字段
        $required = ['title', 'description', 'subject', 'grade', 'min_price', 'max_price'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                return json(['error_code' => 3, 'msg' => "缺少必填字段: {$field}"]);
            }
        }

        $result = Db::name('banxuexing_teachers')->insert($data);

        if ($result) {
            return json(['error_code' => 0, 'msg' => '发布成功，等待审核']);
        } else {
            return json(['error_code' => 4, 'msg' => '发布失败']);
        }
    }

    /**
     * 表示感兴趣
     */
    public function showInterest(Request $request): Json
    {
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        $payload = JwtUtil::verifyToken($token);
        if (!$payload) {
            return json(['error_code' => 2, 'msg' => 'token无效']);
        }

        $userId = $payload['user_id'];
        $teacherId = $request->post('teacher_id');

        if (!$teacherId) {
            return json(['error_code' => 3, 'msg' => '缺少老师ID']);
        }

        // 检查是否已经表示过兴趣
        $exists = Db::name('banxuexing_interests')
            ->where('user_id', $userId)
            ->where('teacher_id', $teacherId)
            ->find();

        if ($exists) {
            return json(['error_code' => 4, 'msg' => '已经表示过兴趣了']);
        }

        // 添加兴趣记录
        $result = Db::name('banxuexing_interests')->insert([
            'user_id' => $userId,
            'teacher_id' => $teacherId,
            'create_time' => time()
        ]);

        if ($result) {
            return json(['error_code' => 0, 'msg' => '表示兴趣成功']);
        } else {
            return json(['error_code' => 5, 'msg' => '操作失败']);
        }
    }
}
