<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;

class BanxuexingUser extends BaseController
{
    /**
     * 微信登录
     */
    public function wxLogin(Request $request): Json
    {
        $code = $request->post('code');
        if (!$code) {
            return json(['error_code' => 1, 'msg' => '缺少code']);
        }

        // 微信小程序配置（需要替换为实际的appid和secret）
        $appid = 'wx643c1ff21fe15e4f';
        $appsecret = '940fbe26b6b028165a6cd27d49cf9117';
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appid}&secret={$appsecret}&js_code={$code}&grant_type=authorization_code";
        
        $result = json_decode(file_get_contents($url), true);

        if (isset($result['errcode'])) {
            return json(['error_code' => 2, 'msg' => $result['errmsg']]);
        }

        $openid = $result['openid'];
        
        // 查找用户
        $user = Db::name('banxuexing_users')
            ->where('openid', $openid)
            ->find();

        if (!$user) {
            // 创建新用户
            $userId = Db::name('banxuexing_users')->insertGetId([
                'openid' => $openid,
                'create_time' => time(),
                'update_time' => time()
            ]);
            
            $user = [
                'id' => $userId,
                'openid' => $openid,
                'nickname' => '',
                'avatar' => '',
                'phone' => '',
                'is_teacher' => 0
            ];
        }

        // 生成token
        $token = JwtUtil::createToken($user['id']);

        return json([
            'error_code' => 0,
            'msg' => '登录成功',
            'data' => [
                'token' => $token,
                'user_info' => $user
            ]
        ]);
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request): Json
    {
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        $payload = JwtUtil::verifyToken($token);
        if (!$payload) {
            return json(['error_code' => 2, 'msg' => 'token无效']);
        }

        $userId = $payload['user_id'];
        $user = Db::name('banxuexing_users')
            ->where('id', $userId)
            ->find();

        if (!$user) {
            return json(['error_code' => 3, 'msg' => '用户不存在']);
        }

        return json([
            'error_code' => 0,
            'msg' => '获取成功',
            'data' => $user
        ]);
    }

    /**
     * 更新用户信息
     */
    public function updateUserInfo(Request $request): Json
    {
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        $payload = JwtUtil::verifyToken($token);
        if (!$payload) {
            return json(['error_code' => 2, 'msg' => 'token无效']);
        }

        $userId = $payload['user_id'];
        $nickname = $request->post('nickname');
        $avatar = $request->post('avatar');
        $phone = $request->post('phone');

        $updateData = [
            'update_time' => time()
        ];

        if ($nickname) $updateData['nickname'] = $nickname;
        if ($avatar) $updateData['avatar'] = $avatar;
        if ($phone) $updateData['phone'] = $phone;

        $result = Db::name('banxuexing_users')
            ->where('id', $userId)
            ->update($updateData);

        if ($result) {
            return json(['error_code' => 0, 'msg' => '更新成功']);
        } else {
            return json(['error_code' => 3, 'msg' => '更新失败']);
        }
    }
}
