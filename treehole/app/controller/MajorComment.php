<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\facade\Db;
use app\BaseController;
use app\util\JwtUtil;

class MajorComment extends BaseController
{
    /**
     * 获取专业评论列表
     */
    public function getComments(Request $request)
    {
        try {
            // 验证token
            $userData = $this->validateToken($request);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token验证失败']);
            }

            $majorId = $request->post('major_id', 0);
            $page = $request->post('page', 1);
            $pageSize = $request->post('page_size', 10);
            $sortType = $request->post('sort_type', 'latest'); // latest, hot
            $userId = $userData['user_id'];

            // 参数验证
            if (empty($majorId)) {
                return json(['code' => 400, 'msg' => '缺少专业ID参数']);
            }

            // 参数类型转换和验证
            $majorId = intval($majorId);
            $page = max(1, intval($page));
            $pageSize = max(1, min(100, intval($pageSize))); // 限制最大100条

            if ($majorId <= 0) {
                return json(['code' => 400, 'msg' => '专业ID无效']);
            }

            $offset = ($page - 1) * $pageSize;

            // 构建查询
            $query = Db::name('major_comments')
                ->alias('mc')
                ->leftJoin('user u', 'mc.user_id = u.id')
                ->where('mc.major_id', $majorId)
                ->where('mc.parent_id', 0) // 只获取主评论
                ->where('mc.status', 1);

            // 排序
            if ($sortType === 'hot') {
                $query->order('mc.like_count DESC, mc.created_at DESC');
            } else {
                $query->order('mc.created_at DESC');
            }

            $comments = $query->field([
                'mc.*',
                'u.username',
                'u.face_url as avatar'
            ])
            ->limit($offset, $pageSize)
            ->select()
            ->toArray();

            // 确保数值字段不为null
            foreach ($comments as &$comment) {
                $comment['like_count'] = intval($comment['like_count'] ?? 0);
                $comment['reply_count'] = intval($comment['reply_count'] ?? 0);
            }

            // 获取每个评论的回复和点赞状态
            foreach ($comments as &$comment) {
                // 获取回复列表
                $replies = Db::name('major_comments')
                    ->alias('mc')
                    ->leftJoin('user u', 'mc.user_id = u.id')
                    ->where('mc.parent_id', $comment['id'])
                    ->where('mc.status', 1)
                    ->field([
                        'mc.*',
                        'u.username',
                        'u.face_url as avatar'
                    ])
                    ->order('mc.created_at ASC')
                    ->select()
                    ->toArray();

                // 确保回复的数值字段不为null
                foreach ($replies as &$reply) {
                    $reply['like_count'] = intval($reply['like_count'] ?? 0);
                    $reply['reply_count'] = intval($reply['reply_count'] ?? 0);
                }

                // 处理回复的点赞状态
                foreach ($replies as &$reply) {
                    $reply['is_liked'] = Db::name('major_comment_likes')
                        ->where('comment_id', $reply['id'])
                        ->where('user_id', $userId)
                        ->count() > 0;
                    
                    // 处理图片
                    if ($reply['images']) {
                        $reply['images'] = json_decode($reply['images'], true) ?: [];
                    } else {
                        $reply['images'] = [];
                    }

                    // 格式化用户信息
                    $reply['user'] = [
                        'id' => $reply['user_id'],
                        'name' => $reply['username'],
                        'avatar' => $reply['avatar'] ?: '/images/touxiang.png'
                    ];

                    // 格式化时间
                    $reply['time'] = $this->formatTimeAgo($reply['created_at']);
                    $reply['likes'] = intval($reply['like_count'] ?? 0);
                }

                $comment['replies'] = $replies;

                // 检查当前用户是否点赞过该评论
                $comment['is_liked'] = Db::name('major_comment_likes')
                    ->where('comment_id', $comment['id'])
                    ->where('user_id', $userId)
                    ->count() > 0;

                // 处理图片
                if ($comment['images']) {
                    $comment['images'] = json_decode($comment['images'], true) ?: [];
                } else {
                    $comment['images'] = [];
                }

                // 格式化用户信息
                $comment['user'] = [
                    'id' => $comment['user_id'],
                    'name' => $comment['username'],
                    'avatar' => $comment['avatar'] ?: '/images/touxiang.png'
                ];

                // 格式化时间和点赞数
                $comment['time'] = $this->formatTimeAgo($comment['created_at']);
                $comment['likes'] = intval($comment['like_count'] ?? 0);
            }

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $comments
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取评论失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 添加评论
     */
    public function addComment(Request $request)
    {
        try {
            // 验证token
            $userData = $this->validateToken($request);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token验证失败']);
            }

            $majorId = $request->post('major_id', 0);
            $content = trim($request->post('content', ''));
            $images = $request->post('images', '[]');
            $parentId = $request->post('parent_id', 0);
            $replyToUserId = $request->post('reply_to_user_id', 0);
            $userId = $userData['user_id'];

            if (empty($majorId) || empty($content)) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }

            // 获取用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }

            $data = [
                'major_id' => $majorId,
                'user_id' => $userId,
                'content' => $content,
                'images' => $images,
                'parent_id' => $parentId
            ];

            // 如果是回复，获取回复目标用户信息
            if ($replyToUserId > 0) {
                $replyToUser = Db::name('user')->where('id', $replyToUserId)->find();
                if ($replyToUser) {
                    $data['reply_to_user_id'] = $replyToUserId;
                    $data['reply_to_username'] = $replyToUser['username'];
                }
            }

            Db::startTrans();
            try {
                // 插入评论
                $commentId = Db::name('major_comments')->insertGetId($data);

                // 如果是回复，更新父评论的回复数
                if ($parentId > 0) {
                    Db::name('major_comments')
                        ->where('id', $parentId)
                        ->inc('reply_count', 1);
                }

                // 处理回复通知
                $this->handleCommentNotification($userId, $majorId, $parentId, $replyToUserId, $content, $images, $commentId);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => '评论成功',
                    'data' => ['id' => $commentId]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '评论失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 点赞/取消点赞评论
     */
    public function likeComment(Request $request)
    {
        try {
            // 验证token
            $userData = $this->validateToken($request);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token验证失败']);
            }

            $commentId = $request->post('comment_id', 0);
            $majorId = $request->post('major_id', 0);
            $userId = $userData['user_id'];

            if (empty($commentId) || empty($majorId)) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }

            // 获取评论信息
            $comment = Db::name('major_comments')
                ->where('id', $commentId)
                ->where('status', 1)
                ->find();

            if (!$comment) {
                return json(['code' => 404, 'msg' => '评论不存在']);
            }

            // 检查是否已经点赞
            $existLike = Db::name('major_comment_likes')
                ->where('comment_id', $commentId)
                ->where('user_id', $userId)
                ->find();

            Db::startTrans();
            try {
                if ($existLike) {
                    // 取消点赞
                    $deleteResult = Db::name('major_comment_likes')
                        ->where('id', $existLike['id'])
                        ->delete();

                    // 重新计算实际点赞数并更新
                    $actualLikeCount = Db::name('major_comment_likes')
                        ->where('comment_id', $commentId)
                        ->count();

                    $updateResult = Db::name('major_comments')
                        ->where('id', $commentId)
                        ->update(['like_count' => $actualLikeCount]);

                    $isLiked = false;
                } else {
                    // 添加点赞
                    $insertResult = Db::name('major_comment_likes')->insert([
                        'comment_id' => $commentId,
                        'user_id' => $userId,
                        'major_id' => $majorId
                    ]);

                    // 重新计算实际点赞数并更新
                    $actualLikeCount = Db::name('major_comment_likes')
                        ->where('comment_id', $commentId)
                        ->count();

                    $updateResult = Db::name('major_comments')
                        ->where('id', $commentId)
                        ->update(['like_count' => $actualLikeCount]);

                    // 添加通知（只有当点赞者不是评论作者本人时才发送通知）
                    if ($comment['user_id'] != $userId) {
                        // 获取用户信息
                        $user = Db::name('user')->where('id', $userId)->find();
                        // 获取专业信息
                        $major = Db::name('buaa_majors')->where('id', $majorId)->find();

                        if ($user && $major) {
                            $targetType = $comment['parent_id'] > 0 ? 'major_reply' : 'major_comment';
                            $contentText = $comment['parent_id'] > 0 ? '点赞了你的专业回复' : '点赞了你的专业评论';

                            Db::name('notification')->insert([
                                'user_id' => $comment['user_id'],
                                'from_user_id' => $userId,
                                'type' => 'like',
                                'target_type' => $targetType,
                                'target_id' => $comment['id'],
                                'message_id' => $majorId,
                                'content' => $user['username'] . $contentText,
                                'target_content' => $comment['content'],
                                'content_image' => '',
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_read' => 0
                            ]);

                            // 更新用户未读消息数
                            Db::name('user')->where('id', $comment['user_id'])->inc('unread', 1)->update();
                        }
                    }

                    $isLiked = true;
                }

                // 获取最新点赞数（直接从点赞表计算，确保准确）
                $likeCount = Db::name('major_comment_likes')
                    ->where('comment_id', $commentId)
                    ->count();

                // 确保点赞数不为null
                $likeCount = intval($likeCount ?? 0);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => 'success',
                    'data' => [
                        'is_liked' => $isLiked,
                        'total_likes' => $likeCount
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '操作失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除评论
     */
    public function deleteComment(Request $request)
    {
        try {
            // 验证token
            $userData = $this->validateToken($request);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token验证失败']);
            }

            $commentId = $request->post('comment_id', 0);
            $userId = $userData['user_id'];

            if (empty($commentId)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查评论是否存在
            $comment = Db::name('major_comments')
                ->where('id', $commentId)
                ->where('status', 1)
                ->find();

            if (!$comment) {
                return json(['code' => 404, 'msg' => '评论不存在']);
            }

            // 验证是否是评论作者
            if ($comment['user_id'] != $userId) {
                return json(['code' => 403, 'msg' => '无权限删除此评论']);
            }

            Db::startTrans();
            try {
                // 软删除评论（将status设为0）
                $result = Db::name('major_comments')
                    ->where('id', $commentId)
                    ->update(['status' => 0]);

                if ($result) {
                    // 如果是回复，需要减少父评论的回复数
                    if ($comment['parent_id'] && $comment['parent_id'] > 0) {
                        // 重新计算父评论的回复数
                        $replyCount = Db::name('major_comments')
                            ->where('parent_id', $comment['parent_id'])
                            ->where('status', 1)
                            ->count();

                        Db::name('major_comments')
                            ->where('id', $comment['parent_id'])
                            ->update(['reply_count' => $replyCount]);
                    }

                    Db::commit();
                    return json(['code' => 200, 'msg' => '删除成功']);
                } else {
                    Db::rollback();
                    return json(['code' => 500, 'msg' => '删除失败']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 修复评论点赞数
     */
    public function fixLikeCount(Request $request)
    {
        try {
            // 验证token
            $userData = $this->validateToken($request);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token验证失败']);
            }

            // 修复NULL值
            Db::execute("UPDATE major_comments SET like_count = 0 WHERE like_count IS NULL");
            Db::execute("UPDATE major_comments SET reply_count = 0 WHERE reply_count IS NULL");

            // 重新计算点赞数
            $comments = Db::name('major_comments')->select();
            foreach ($comments as $comment) {
                $likeCount = Db::name('major_comment_likes')
                    ->where('comment_id', $comment['id'])
                    ->count();

                Db::name('major_comments')
                    ->where('id', $comment['id'])
                    ->update(['like_count' => $likeCount]);
            }

            return json([
                'code' => 200,
                'msg' => '修复完成',
                'data' => ['fixed_count' => count($comments)]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '修复失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化时间为相对时间
     */
    private function formatTimeAgo($datetime)
    {
        if (empty($datetime)) {
            return '刚刚';
        }

        $timestamp = strtotime($datetime);
        $now = time();
        $diff = $now - $timestamp;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 259200) { // 3天内
            return floor($diff / 86400) . '天前';
        } else {
            // 超过3天显示具体日期
            return date('y-m-d', $timestamp);
        }
    }

    /**
     * 处理评论通知
     */
    private function handleCommentNotification($userId, $majorId, $parentId, $replyToUserId, $content, $images, $commentId)
    {
        try {
            // 获取用户信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return;
            }

            // 处理图片，获取第一张图片作为通知图片
            $firstImage = '';
            if (!empty($images)) {
                $imageList = json_decode($images, true);
                if (is_array($imageList) && !empty($imageList)) {
                    $firstImage = $imageList[0];
                }
            }

            if ($parentId) {
                // 这是回复评论
                $parentComment = Db::name('major_comments')->where('id', $parentId)->find();
                if ($parentComment && $parentComment['user_id'] != $userId) {
                    // 通知原评论作者
                    Db::name('notification')->insert([
                        'user_id' => $parentComment['user_id'],
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'major_comment',
                        'target_id' => $parentId,
                        'message_id' => $majorId,
                        'content' => $user['username'] . '回复了你的专业评论',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $parentComment['user_id'])->inc('unread', 1)->update();
                }

                // 如果是回复特定用户，也要通知被回复的用户
                if ($replyToUserId && $replyToUserId != $userId && $replyToUserId != $parentComment['user_id']) {
                    Db::name('notification')->insert([
                        'user_id' => $replyToUserId,
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'major_reply',
                        'target_id' => $commentId, // 使用新回复的ID，而不是父评论ID
                        'message_id' => $majorId,
                        'content' => $user['username'] . '回复了你的专业回复',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $replyToUserId)->inc('unread', 1)->update();
                }
            }
            // 注意：专业没有"创建者"概念，所以不需要通知专业创建者
        } catch (\Exception $e) {
            // 通知发送失败不影响评论功能
            error_log("专业评论通知发送失败: " . $e->getMessage());
        }
    }

    /**
     * 验证token
     */
    private function validateToken(Request $request)
    {
        $token = $request->header('token');
        if (!$token) {
            return false;
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return false;
        }

        return $userData;
    }
}
