<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Log;

class School extends BaseController
{
    /**
     * 获取学校列表
     */
    public function getSchools()
    {
        try {
            // 获取所有学校，按启用状态和排序权重排序
            $schools = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, school_level, school_type, is_active, sort_order, logo_url')
                ->order('is_active DESC, sort_order DESC, id ASC')
                ->select()
                ->toArray();

            if (empty($schools)) {
                return json([
                    'code' => 200,
                    'message' => '暂无学校数据',
                    'data' => []
                ]);
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $schools
            ]);

        } catch (\Exception $e) {
            Log::error('获取学校列表失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => []
            ]);
        }
    }

    /**
     * 获取已启用的学校列表
     */
    public function getEnabledSchools()
    {
        try {
            $schools = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, school_level, school_type, logo_url')
                ->where('is_active', 1)
                ->order('sort_order DESC, id ASC')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $schools
            ]);

        } catch (\Exception $e) {
            Log::error('获取已启用学校列表失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => []
            ]);
        }
    }

    /**
     * 根据区域获取学校
     */
    public function getSchoolsByDistrict()
    {
        try {
            $district = $this->request->post('district', '');
            
            if (empty($district)) {
                return json([
                    'code' => 400,
                    'message' => '请提供区域参数',
                    'data' => []
                ]);
            }

            $schools = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, school_level, school_type, is_active, logo_url')
                ->where('district', $district)
                ->order('is_active DESC, sort_order DESC, id ASC')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $schools
            ]);

        } catch (\Exception $e) {
            Log::error('根据区域获取学校失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => []
            ]);
        }
    }

    /**
     * 搜索学校
     */
    public function searchSchools()
    {
        try {
            $keyword = $this->request->post('keyword', '');
            
            if (empty($keyword)) {
                return json([
                    'code' => 400,
                    'message' => '请提供搜索关键词',
                    'data' => []
                ]);
            }

            $schools = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, school_level, school_type, is_active, logo_url')
                ->where('short_name', 'like', '%' . $keyword . '%')
                ->whereOr('university_short_name', 'like', '%' . $keyword . '%')
                ->whereOr('full_name', 'like', '%' . $keyword . '%')
                ->whereOr('university_name', 'like', '%' . $keyword . '%')
                ->whereOr('district', 'like', '%' . $keyword . '%')
                ->order('is_active DESC, sort_order DESC, id ASC')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'message' => '搜索成功',
                'data' => $schools
            ]);

        } catch (\Exception $e) {
            Log::error('搜索学校失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => []
            ]);
        }
    }

    /**
     * 获取学校详情
     */
    public function getSchoolDetail()
    {
        try {
            $schoolId = $this->request->post('school_id', 0);
            
            if (empty($schoolId)) {
                return json([
                    'code' => 400,
                    'message' => '请提供学校ID',
                    'data' => null
                ]);
            }

            $school = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, school_level, school_type, is_active, sort_order, logo_url, campus_type, address, longitude, latitude, student_count, description, website, created_at, updated_at')
                ->where('id', $schoolId)
                ->find();

            if (!$school) {
                return json([
                    'code' => 404,
                    'message' => '学校不存在',
                    'data' => null
                ]);
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $school
            ]);

        } catch (\Exception $e) {
            Log::error('获取学校详情失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取所有区域列表
     */
    public function getDistricts()
    {
        try {
            $districts = Db::table('beijing_universities')
                ->field('district')
                ->distinct(true)
                ->order('district ASC')
                ->column('district');

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $districts
            ]);

        } catch (\Exception $e) {
            Log::error('获取区域列表失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => []
            ]);
        }
    }
}
