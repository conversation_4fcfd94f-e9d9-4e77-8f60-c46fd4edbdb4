<?php
namespace app\util;

/**
 * 图片安全验证工具类
 * 用于防止恶意文件上传
 */
class ImageSecurityUtil
{
    /**
     * 最大文件大小（5MB）
     */
    const MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 允许的MIME类型
     */
    const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'image/gif'
    ];

    /**
     * 允许的文件扩展名
     */
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif'];

    /**
     * 图片文件头部特征
     */
    const IMAGE_HEADERS = [
        'image/jpeg' => ['ffd8ff'],
        'image/png' => ['89504e47'],
        'image/gif' => ['474946383761', '474946383961'] // GIF87a, GIF89a
    ];

    /**
     * 危险字符列表
     */
    const DANGEROUS_CHARS = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*', "\0"];

    /**
     * 危险文件名列表
     */
    const DANGEROUS_NAMES = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'];

    /**
     * 验证原生$_FILES格式的图片文件
     * @param array $file $_FILES数组中的文件信息
     * @return array ['success' => bool, 'message' => string]
     */
    public static function validateNativeFile($file)
    {
        // 检查文件是否上传成功
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => '文件上传失败'];
        }

        // 检查文件大小
        if ($file['size'] > self::MAX_FILE_SIZE) {
            return ['success' => false, 'message' => '文件大小不能超过5MB'];
        }

        if ($file['size'] <= 0) {
            return ['success' => false, 'message' => '文件不能为空'];
        }

        // 检查临时文件是否存在
        if (!file_exists($file['tmp_name'])) {
            return ['success' => false, 'message' => '临时文件不存在'];
        }

        // 验证MIME类型
        $mimeCheck = self::validateMimeType($file['tmp_name']);
        if (!$mimeCheck['success']) {
            return $mimeCheck;
        }

        // 验证文件头部
        $headerCheck = self::validateFileHeader($file['tmp_name'], $mimeCheck['mime_type']);
        if (!$headerCheck['success']) {
            return $headerCheck;
        }

        // 验证文件扩展名
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            return ['success' => false, 'message' => '文件扩展名不被允许'];
        }

        // 验证文件名
        $nameCheck = self::validateFileName($file['name']);
        if (!$nameCheck['success']) {
            return $nameCheck;
        }

        return ['success' => true, 'message' => '验证通过', 'mime_type' => $mimeCheck['mime_type']];
    }

    /**
     * 验证ThinkPHP文件对象
     * @param \think\file\UploadedFile $file ThinkPHP文件对象
     * @return array ['success' => bool, 'message' => string]
     */
    public static function validateThinkPHPFile($file)
    {
        // 检查文件大小
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            return ['success' => false, 'message' => '文件大小不能超过5MB'];
        }

        if ($file->getSize() <= 0) {
            return ['success' => false, 'message' => '文件不能为空'];
        }

        // 验证MIME类型
        $mimeCheck = self::validateMimeType($file->getRealPath());
        if (!$mimeCheck['success']) {
            return $mimeCheck;
        }

        // 验证文件头部
        $headerCheck = self::validateFileHeader($file->getRealPath(), $mimeCheck['mime_type']);
        if (!$headerCheck['success']) {
            return $headerCheck;
        }

        // 验证文件扩展名
        $extension = strtolower($file->extension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            return ['success' => false, 'message' => '文件扩展名不被允许'];
        }

        // 验证文件名
        $nameCheck = self::validateFileName($file->getOriginalName());
        if (!$nameCheck['success']) {
            return $nameCheck;
        }

        return ['success' => true, 'message' => '验证通过', 'mime_type' => $mimeCheck['mime_type']];
    }

    /**
     * 验证文件MIME类型
     * @param string $filePath 文件路径
     * @return array ['success' => bool, 'message' => string, 'mime_type' => string]
     */
    private static function validateMimeType($filePath)
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        if (!in_array($mimeType, self::ALLOWED_MIME_TYPES)) {
            return ['success' => false, 'message' => '只允许上传JPG、PNG、GIF格式的图片'];
        }

        return ['success' => true, 'message' => 'MIME类型验证通过', 'mime_type' => $mimeType];
    }

    /**
     * 验证文件头部信息
     * @param string $filePath 文件路径
     * @param string $mimeType MIME类型
     * @return array ['success' => bool, 'message' => string]
     */
    private static function validateFileHeader($filePath, $mimeType)
    {
        $file = fopen($filePath, 'rb');
        $header = fread($file, 16); // 读取前16个字节
        fclose($file);
        $headerHex = bin2hex($header);

        if (!isset(self::IMAGE_HEADERS[$mimeType])) {
            return ['success' => false, 'message' => '不支持的图片格式'];
        }

        foreach (self::IMAGE_HEADERS[$mimeType] as $validHeader) {
            if (strpos(strtolower($headerHex), strtolower($validHeader)) === 0) {
                return ['success' => true, 'message' => '文件头部验证通过'];
            }
        }

        return ['success' => false, 'message' => '文件格式不正确或文件已损坏'];
    }

    /**
     * 验证文件名是否安全
     * @param string $fileName 文件名
     * @return array ['success' => bool, 'message' => string]
     */
    private static function validateFileName($fileName)
    {
        // 检查是否包含危险字符
        foreach (self::DANGEROUS_CHARS as $char) {
            if (strpos($fileName, $char) !== false) {
                return ['success' => false, 'message' => '文件名包含非法字符'];
            }
        }

        // 检查是否为危险的文件名
        $nameWithoutExt = strtolower(pathinfo($fileName, PATHINFO_FILENAME));
        if (in_array($nameWithoutExt, self::DANGEROUS_NAMES)) {
            return ['success' => false, 'message' => '文件名不被允许'];
        }

        return ['success' => true, 'message' => '文件名验证通过'];
    }

    /**
     * 根据MIME类型获取安全的文件扩展名
     * @param string $mimeType MIME类型
     * @return string 安全的文件扩展名
     */
    public static function getSafeExtension($mimeType)
    {
        switch ($mimeType) {
            case 'image/jpeg':
                return 'jpg';
            case 'image/png':
                return 'png';
            case 'image/gif':
                return 'gif';
            default:
                return 'jpg'; // 默认返回jpg
        }
    }

    /**
     * 根据文件路径获取安全的文件扩展名
     * @param string $filePath 文件路径
     * @return string 安全的文件扩展名
     */
    public static function getSafeExtensionByFile($filePath)
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        return self::getSafeExtension($mimeType);
    }

    /**
     * 生成安全的文件名
     * @param string $prefix 文件名前缀
     * @param string $extension 文件扩展名
     * @return string 安全的文件名
     */
    public static function generateSafeFileName($prefix = 'image', $extension = 'jpg')
    {
        return $prefix . '_' . time() . '_' . mt_rand(1000, 9999) . '.' . $extension;
    }
}
