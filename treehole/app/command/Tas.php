<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class Tas extends Command
{
    protected function configure()
    {
        // 设置名称为task2
        $this->setName('tas')
            // 增加一个命令参数
            ->addArgument('action', Argument::OPTIONAL, "action", '')
            ->addArgument('force', Argument::OPTIONAL, "force", '');
    }

    protected function execute(Input $input, Output $output)
    {
        // 获取输入参数
        $action = trim($input->getArgument('action'));
        $force = trim($input->getArgument('force'));

        // 配置任务
        $task = new \EasyTask\Task();
        $task->setRunTimePath('./runtime/');

        // 每 15 分钟触发一次 send:message2 任务
        $task->addFunc(function () {
            // 假设 send:message2 是一个 Artisan 或 ThinkPHP 命令
            exec('php think send');
        }, 'send_message', 15 * 60); // 时间间隔为 15 分钟（以秒为单位）

        // 根据命令执行
        if ($action == 'start') {
            $task->start();
        } elseif ($action == 'status') {
            $task->status();
        } elseif ($action == 'stop') {
            $force = ($force == 'force'); // 是否强制停止
            $task->stop($force);
        } else {
            exit('Command is not exist');
        }
    }
}