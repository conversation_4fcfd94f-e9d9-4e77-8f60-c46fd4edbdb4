<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db; // 引入数据库操作类
use think\facade\Log; // 引入日志类

class Send extends Command
{
    protected function configure()
    {
        // 设置命令名称和描述
        $this->setName('send')
            ->setDescription('每15分钟发送一次消息');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            // 从 message 表中获取按 id 倒序的前 45 条消息

            $messages = Db::name('message')
                        ->where('choose', '<', 100) // 筛选 choose 字段小于 100
                        ->order('id', 'desc')
                        ->limit(100)
                        ->select()
                        ->toArray();

            // 分别收集不同ispull值的消息
            $normalMessages = []; // ispull = 1 的消息
            $urgentMessages = []; // ispull = 2 的消息
            $normalIdsToUpdate = []; // ispull = 1 需要更新的消息 ID
            $urgentIdsToUpdate = []; // ispull = 2 需要更新的消息 ID

            if ($messages) {
                foreach ($messages as $message) {
                    if ($message['ispull'] == 0) {
                        // 如果 ispull 为 0，停止查找
                        break;
                    }

                    // 如果有换行，只取换行前的内容；否则限制前 28 个字符
                    $content = $message['content'];
                    $newlinePos = strpos($content, "\n");
                    if ($newlinePos !== false) {
                        // 如果找到换行符，只取换行前的内容
                        $limitedContent = substr($content, 0, $newlinePos);
                    } else {
                        // 如果没有换行符，限制前 28 个字符
                        $limitedContent = mb_substr($content, 0, 28, 'UTF-8');
                    }

                    // 根据ispull值分类收集消息
                    if ($message['ispull'] == 1) {
                        // 普通消息
                        $formattedMessage = $limitedContent . "\n" . $message['scheme'] . "\n";
                        $normalMessages[] = $formattedMessage;
                        $normalIdsToUpdate[] = $message['id'];
                    } elseif ($message['ispull'] == 2) {
                        // 低价急出消息，在内容前面加上【金额】
                        $pricePrefix = '';
                        if (!empty($message['jine'])) {
                            $pricePrefix = '【' . $message['jine'] . '】';
                        }
                        $formattedMessage = $pricePrefix . $limitedContent . "\n" . $message['scheme'] . "\n";
                        $urgentMessages[] = $formattedMessage;
                        $urgentIdsToUpdate[] = $message['id'];
                    }
                }
            }
            // 打印到日志文件


        } catch (\Exception $e) {
            $output->writeln('数据库查询出错：' . $e->getMessage());
            Log::error('数据库查询出错：' . $e->getMessage());
            return;
        }

        // 处理普通消息 (ispull = 1)
        if (!empty($normalMessages)) {
            $this->sendMessages($normalMessages, $normalIdsToUpdate, 'normal', $output);
        }

        // 处理低价急出消息 (ispull = 2)
        if (!empty($urgentMessages)) {
            $this->sendMessages($urgentMessages, $urgentIdsToUpdate, 'urgent', $output);
        }
    }

    /**
     * 发送消息的通用方法
     * @param array $messages 消息数组
     * @param array $idsToUpdate 需要更新的ID数组
     * @param string $type 消息类型 ('normal' 或 'urgent')
     * @param Output $output 输出对象
     */
    private function sendMessages($messages, $idsToUpdate, $type, $output)
    {
        // 将消息分成每组最多 10 条，并倒序发送
        $messageChunks = array_chunk($messages, 10);
        $messageChunks = array_reverse($messageChunks); // 将整个批次的顺序倒过来

        foreach ($messageChunks as $chunk) {
            // 将消息倒序排列
            $chunk = array_reverse($chunk);

            // 将消息合并成一个字符串，使用 \n 分隔
            $receivedContent = implode("\n", $chunk);

            // 根据消息类型构建不同的请求数据
            if ($type === 'urgent') {
                // 低价急出消息推送到灵行北航校内论坛6
                $postData = [
                    "socketType" => 2,
                    "list" => [
                        [
                            "type" => 203,
                            "titleList" => [
                                "二手物品快速交易群"
                            ],
                            "receivedContent" => $receivedContent
                        ]
                    ]
                ];
            } else {
                // 普通消息推送到原有的群组
                $postData = [
                    "socketType" => 2,
                    "list" => [
                        [
                            "type" => 203,
                            "titleList" => [
                                "灵行北航校内论坛3",
                                "灵行北航校内论坛2",
                                "灵行北航校内论坛1",
                                "灵行北航校内论坛5"
                            ],
                            "receivedContent" => $receivedContent
                        ]
                    ]
                ];
            }

            // 初始化 cURL
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.worktool.ymdyes.cn/wework/sendRawMessage?robotId=wt6f671p34nvtpr22xfq25xc085nfg4u',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($postData, JSON_UNESCAPED_UNICODE),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            $error = curl_error($curl);
            curl_close($curl);

            if ($response === false) {
                $output->writeln("[$type] 接口调用失败：" . $error);
                Log::error("[$type] 接口调用失败：" . $error);
            } else {
                $output->writeln("[$type] 接口调用成功：" . $response);
                Log::info("[$type] 接口调用成功：" . $response);

                // 接口调用成功后更新 ispull 字段为 0
                if (!empty($idsToUpdate)) {
                    foreach ($idsToUpdate as $id) {
                        try {
                            Db::name('message')
                                ->where('id', $id)
                                ->update(['ispull' => 0]);
                            Log::info("[$type] 成功将消息 ID {$id} 的 ispull 设置为 0。");
                        } catch (\Exception $e) {
                            $output->writeln("[$type] 更新消息 ID {$id} 的 ispull 字段失败：" . $e->getMessage());
                            Log::error("[$type] 更新消息 ID {$id} 的 ispull 字段失败：" . $e->getMessage());
                        }
                    }
                }
            }
            // 如果有多组消息，批次间隔 1 分钟
        }
    }
}