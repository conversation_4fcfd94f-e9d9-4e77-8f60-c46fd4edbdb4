<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

class DraftGenerate extends Command
{
    protected function configure()
    {
        // 设置名称为draft:generate
        $this->setName('draft:generate')
            ->setDescription('执行草稿生成任务');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            // 检查今天是否已经执行过
            $today = date('Y-m-d');
            $lockFile = './runtime/draft_task_' . $today . '.lock';

            if (file_exists($lockFile)) {
                $output->writeln("[" . date('Y-m-d H:i:s') . "] 今天已经执行过草稿生成任务，跳过执行");
                return;
            }

            // 记录任务开始时间
            $startTime = date('Y-m-d H:i:s');
            $output->writeln("[{$startTime}] 开始执行草稿生成任务");

            // 配置参数
            $config = $this->getDraftConfig();

            // 构建请求URL
            $url = $config['api_url'];
            $params = http_build_query($config['params']);
            if (!empty($params)) {
                $url .= '?' . $params;
            }

            // 发送HTTP请求
            $result = $this->sendHttpRequest($url, $config['timeout']);

            // 记录结果
            $endTime = date('Y-m-d H:i:s');
            if ($result['success']) {
                $output->writeln("[{$endTime}] 草稿生成任务执行成功");
                $output->writeln("响应内容: " . $result['response']);

                // 创建锁文件，标记今天已执行
                file_put_contents($lockFile, $endTime);
            } else {
                $output->writeln("[{$endTime}] 草稿生成任务执行失败: " . $result['error']);
            }

        } catch (\Exception $e) {
            $errorTime = date('Y-m-d H:i:s');
            $output->writeln("[{$errorTime}] 草稿生成任务异常: " . $e->getMessage());
        }
    }

    /**
     * 获取草稿生成配置
     */
    private function getDraftConfig(): array
    {
        return [
            // API接口地址
            'api_url' => 'https://www.bjgaoxiaoshequ.store/SimpleDraftGenerator/generateDraft',

            // 请求参数配置（可在此处修改参数）
            'params' => [
                'limit' => 10,              // 获取消息数量，建议10-30条
                'auto_publish' => 0,        // 是否自动发布，0=仅生成草稿，1=生成并自动发布
                'cover_image' => '',        // 封面图片URL，留空使用默认封面
            ],

            // 请求超时时间（秒）
            'timeout' => 120,
        ];
    }

    /**
     * 发送HTTP请求
     */
    private function sendHttpRequest(string $url, int $timeout = 60): array
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($ch, CURLOPT_USERAGENT, 'DraftTask/1.0');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return ['success' => false, 'error' => 'CURL错误: ' . $error];
            }

            if ($httpCode !== 200) {
                return ['success' => false, 'error' => 'HTTP错误: ' . $httpCode];
            }

            return ['success' => true, 'response' => $response];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => '请求异常: ' . $e->getMessage()];
        }
    }
}
