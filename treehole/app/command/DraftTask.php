<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class DraftTask extends Command
{
    protected function configure()
    {
        // 设置名称为draft-task
        $this->setName('draft-task')
            // 增加一个命令参数
            ->addArgument('action', Argument::OPTIONAL, "action", '')
            ->addArgument('force', Argument::OPTIONAL, "force", '');
    }

    protected function execute(Input $input, Output $output)
    {
        // 获取输入参数
        $action = trim($input->getArgument('action'));
        $force = trim($input->getArgument('force'));

        // 配置任务
        $task = new \EasyTask\Task();
        $task->setRunTimePath('./runtime/');

        // 每小时检查一次是否到了17:00执行时间
        $task->addFunc(function () {
            // 检查当前时间是否为17:00-17:59之间
            $currentHour = (int)date('H');
            if ($currentHour === 17) {
                // 使用独立进程调用，避免上下文问题
                exec('php think draft:generate');
            }
        }, 'generate_draft', 60 * 60); // 每小时检查一次

        // 根据命令执行
        if ($action == 'start') {
            $task->start();
            $output->writeln('草稿生成定时任务已启动，每天17:00执行');
        } elseif ($action == 'status') {
            $task->status();
        } elseif ($action == 'stop') {
            $force = ($force == 'force'); // 是否强制停止
            $task->stop($force);
            $output->writeln('草稿生成定时任务已停止');
        } else {
            $output->writeln('可用命令:');
            $output->writeln('  start  - 启动定时任务');
            $output->writeln('  status - 查看任务状态');
            $output->writeln('  stop   - 停止任务');
            $output->writeln('  stop force - 强制停止任务');
        }
    }






}
