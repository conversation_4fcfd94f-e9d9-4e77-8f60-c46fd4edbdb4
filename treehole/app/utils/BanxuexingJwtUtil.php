<?php
declare(strict_types=1);

namespace app\utils;

class BanxuexingJwtUtil
{
    // JWT密钥
    private static $key = 'banxuexing_jwt_secret_key_2024';
    
    // token有效期（2小时）
    private static $expire = 7200;
    
    /**
     * 生成JWT token
     */
    public static function createToken(array $payload): string
    {
        $header = [
            'typ' => 'JWT',
            'alg' => 'HS256'
        ];
        
        $payload['iat'] = time(); // 签发时间
        $payload['exp'] = time() + self::$expire; // 过期时间
        
        $headerEncoded = self::base64UrlEncode(json_encode($header));
        $payloadEncoded = self::base64UrlEncode(json_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::$key, true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }
    
    /**
     * 验证JWT token
     */
    public static function verifyToken(string $token): ?array
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }
        
        [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;
        
        // 验证签名
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::$key, true);
        $expectedSignature = self::base64UrlEncode($signature);
        
        if (!hash_equals($expectedSignature, $signatureEncoded)) {
            return null;
        }
        
        // 解析payload
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        if (!$payload) {
            return null;
        }
        
        // 检查是否过期
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return null;
        }
        
        // 检查必要字段
        if (!isset($payload['user_id'])) {
            return null;
        }
        
        return $payload;
    }
    
    /**
     * Base64 URL编码
     */
    private static function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode(string $data): string
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
}
