<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 北航专业信息模型
 */
class BuaaMajors extends Model
{
    // 设置表名
    protected $name = 'buaa_majors';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'department_id' => 'int',
        'college_name'  => 'string',
        'major_name'    => 'string',
        'university_id' => 'int',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /**
     * 获取系别列表
     */
    public static function getDepartments()
    {
        return self::field('department_id')
            ->where('university_id', 1)
            ->group('department_id')
            ->order('department_id', 'asc')
            ->column('department_id');
    }

    /**
     * 根据系别获取专业列表
     */
    public static function getMajorsByDepartment($departmentId = null, $keyword = '', $page = 1, $pageSize = 20)
    {
        $where = [
            ['university_id', '=', 1]
        ];

        if (!empty($departmentId)) {
            $where[] = ['department_id', '=', $departmentId];
        }

        if (!empty($keyword)) {
            $where[] = ['major_name|college_name', 'like', '%' . $keyword . '%'];
        }

        return self::where($where)
            ->order('department_id', 'asc')
            ->order('id', 'asc')
            ->page($page, $pageSize)
            ->select();
    }

    /**
     * 获取相关专业推荐
     */
    public static function getRelatedMajors($currentMajorId, $limit = 5)
    {
        // 获取当前专业信息
        $currentMajor = self::find($currentMajorId);
        if (!$currentMajor) {
            return [];
        }

        // 优先推荐同系的其他专业
        $relatedMajors = self::where([
            ['university_id', '=', 1],
            ['department_id', '=', $currentMajor->department_id],
            ['id', '<>', $currentMajorId]
        ])
        ->limit($limit)
        ->select()
        ->toArray();

        // 如果同系专业不够，补充其他专业
        if (count($relatedMajors) < $limit) {
            $remaining = $limit - count($relatedMajors);
            $additionalMajors = self::where([
                ['university_id', '=', 1],
                ['department_id', '<>', $currentMajor->department_id],
                ['id', '<>', $currentMajorId]
            ])
            ->limit($remaining)
            ->select()
            ->toArray();
            
            $relatedMajors = array_merge($relatedMajors, $additionalMajors);
        }

        return $relatedMajors;
    }

    /**
     * 搜索专业
     */
    public static function searchMajors($keyword, $page = 1, $pageSize = 20)
    {
        return self::where([
            ['university_id', '=', 1],
            ['major_name|college_name', 'like', '%' . $keyword . '%']
        ])
        ->order('department_id', 'asc')
        ->order('id', 'asc')
        ->page($page, $pageSize)
        ->select();
    }

    /**
     * 获取专业统计信息
     */
    public static function getMajorStats()
    {
        return [
            'total_majors' => self::where('university_id', 1)->count(),
            'total_departments' => self::where('university_id', 1)->group('department_id')->count(),
            'total_colleges' => self::where('university_id', 1)->group('college_name')->count(),
        ];
    }
}
