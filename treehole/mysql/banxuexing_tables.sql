-- 伴学星项目数据库表结构

-- 用户表
CREATE TABLE `banxuexing_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像URL',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `real_name` varchar(20) DEFAULT '' COMMENT '真实姓名',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `id_card` varchar(20) DEFAULT '' COMMENT '身份证号',
  `city` varchar(50) DEFAULT '' COMMENT '所在城市',
  `is_teacher` tinyint(1) DEFAULT 0 COMMENT '是否为老师：0否，1是',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1正常',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='伴学星用户表';

-- 老师信息表
CREATE TABLE `banxuexing_teachers` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '老师信息ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `subject` varchar(50) NOT NULL COMMENT '科目',
  `grade` varchar(50) NOT NULL COMMENT '年级',
  `gender_requirement` varchar(10) DEFAULT '' COMMENT '性别要求',
  `area` varchar(100) DEFAULT '' COMMENT '地区',
  `min_price` decimal(8,2) NOT NULL COMMENT '最低价格',
  `max_price` decimal(8,2) NOT NULL COMMENT '最高价格',
  `available_time` varchar(200) DEFAULT '' COMMENT '可用时间',
  `is_urgent` tinyint(1) DEFAULT 0 COMMENT '是否急聘：0否，1是',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0待审核，1已通过，2已拒绝',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `interest_count` int(11) DEFAULT 0 COMMENT '感兴趣次数',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `subject` (`subject`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='老师信息表';

-- 老师详细信息表
CREATE TABLE `banxuexing_teacher_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `school` varchar(100) DEFAULT '' COMMENT '学校',
  `major` varchar(100) DEFAULT '' COMMENT '专业',
  `grade_level` varchar(20) DEFAULT '' COMMENT '年级',
  `education` varchar(20) DEFAULT '' COMMENT '最高学历',
  `school_time` varchar(100) DEFAULT '' COMMENT '在校时间',
  `textbook_version` varchar(100) DEFAULT '' COMMENT '可教教材版本',
  `expected_salary` varchar(100) DEFAULT '' COMMENT '期望薪资',
  `teachable_subjects` varchar(200) DEFAULT '' COMMENT '可教学科',
  `introduction` text COMMENT '个人简介',
  `certificates` varchar(500) DEFAULT '' COMMENT '证书/荣誉',
  `teachable_grades` varchar(200) DEFAULT '' COMMENT '可教年级',
  `available_schedule` text COMMENT '可约时间（JSON格式）',
  `student_id_card` varchar(255) DEFAULT '' COMMENT '学生证/毕业证图片',
  `score_card` varchar(255) DEFAULT '' COMMENT '高考成绩单图片',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='老师详细信息表';

-- 感兴趣记录表
CREATE TABLE `banxuexing_interests` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `teacher_id` int(11) NOT NULL COMMENT '老师信息ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0取消，1感兴趣',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_teacher` (`user_id`, `teacher_id`),
  KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='感兴趣记录表';

-- 消息表
CREATE TABLE `banxuexing_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `from_user_id` int(11) NOT NULL COMMENT '发送者ID',
  `to_user_id` int(11) NOT NULL COMMENT '接收者ID',
  `teacher_id` int(11) DEFAULT NULL COMMENT '相关老师信息ID',
  `content` text NOT NULL COMMENT '消息内容',
  `type` tinyint(1) DEFAULT 1 COMMENT '消息类型：1文本，2图片，3系统消息',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：0未读，1已读',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `from_user_id` (`from_user_id`),
  KEY `to_user_id` (`to_user_id`),
  KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';
