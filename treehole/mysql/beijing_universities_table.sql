-- 创建北京高校信息表
DROP TABLE IF EXISTS `beijing_universities`;

CREATE TABLE `beijing_universities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '校区ID',
  `university_id` int(11) NOT NULL COMMENT '学校ID（同一学校不同校区使用相同ID）',
  `district` varchar(20) NOT NULL COMMENT '行政区',
  `full_name` varchar(100) NOT NULL COMMENT '学校全称',
  `short_name` varchar(50) NOT NULL COMMENT '常用简称（含校区）',
  `university_short_name` varchar(20) NOT NULL COMMENT '学校简称（不含校区）',
  `university_name` varchar(100) NOT NULL COMMENT '所属学校名称',
  `campus_type` varchar(50) DEFAULT NULL COMMENT '校区类型（主校区/分校区等）',
  `address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `school_level` varchar(20) DEFAULT NULL COMMENT '学校层次（985/211/双一流/普通本科等）',
  `school_type` varchar(20) DEFAULT NULL COMMENT '学校类型（综合/理工/师范/艺术等）',
  `student_count` int(11) DEFAULT NULL COMMENT '在校学生数',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（1启用0禁用）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `logo_url` varchar(200) DEFAULT NULL COMMENT '学校logo图片URL',
  `description` text DEFAULT NULL COMMENT '学校简介',
  `website` varchar(200) DEFAULT NULL COMMENT '官方网站',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_university_id` (`university_id`),
  KEY `idx_district` (`district`),
  KEY `idx_short_name` (`short_name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='北京高校信息表';
