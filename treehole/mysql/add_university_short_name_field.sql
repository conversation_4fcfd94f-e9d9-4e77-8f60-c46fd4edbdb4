-- 为beijing_universities表添加university_short_name字段
-- 用于存储不带校区的学校简称

ALTER TABLE `beijing_universities` 
ADD COLUMN `university_short_name` varchar(20) NOT NULL COMMENT '学校简称（不含校区）' AFTER `short_name`;

-- 更新所有记录的university_short_name字段
UPDATE `beijing_universities` SET `university_short_name` = '北大' WHERE `university_name` = '北京大学';
UPDATE `beijing_universities` SET `university_short_name` = '清华' WHERE `university_name` = '清华大学';
UPDATE `beijing_universities` SET `university_short_name` = '人大' WHERE `university_name` = '中国人民大学';
UPDATE `beijing_universities` SET `university_short_name` = '北师大' WHERE `university_name` = '北京师范大学';
UPDATE `beijing_universities` SET `university_short_name` = '北航' WHERE `university_name` = '北京航空航天大学';
UPDATE `beijing_universities` SET `university_short_name` = '北理工' WHERE `university_name` = '北京理工大学';
UPDATE `beijing_universities` SET `university_short_name` = '农大' WHERE `university_name` = '中国农业大学';
UPDATE `beijing_universities` SET `university_short_name` = '民大' WHERE `university_name` = '中央民族大学';
UPDATE `beijing_universities` SET `university_short_name` = '北交大' WHERE `university_name` = '北京交通大学';
UPDATE `beijing_universities` SET `university_short_name` = '北科' WHERE `university_name` = '北京科技大学';
UPDATE `beijing_universities` SET `university_short_name` = '北邮' WHERE `university_name` = '北京邮电大学';
UPDATE `beijing_universities` SET `university_short_name` = '北林' WHERE `university_name` = '北京林业大学';
UPDATE `beijing_universities` SET `university_short_name` = '北外' WHERE `university_name` = '北京外国语大学';
UPDATE `beijing_universities` SET `university_short_name` = '贸大' WHERE `university_name` = '对外经济贸易大学';
UPDATE `beijing_universities` SET `university_short_name` = '央财' WHERE `university_name` = '中央财经大学';
UPDATE `beijing_universities` SET `university_short_name` = '法大' WHERE `university_name` = '中国政法大学';
UPDATE `beijing_universities` SET `university_short_name` = '地大' WHERE `university_name` = '中国地质大学(北京)';
UPDATE `beijing_universities` SET `university_short_name` = '矿大' WHERE `university_name` = '中国矿业大学(北京)';
UPDATE `beijing_universities` SET `university_short_name` = '石大' WHERE `university_name` = '中国石油大学(北京)';
UPDATE `beijing_universities` SET `university_short_name` = '北化' WHERE `university_name` = '北京化工大学';
UPDATE `beijing_universities` SET `university_short_name` = '首师大' WHERE `university_name` = '首都师范大学';
UPDATE `beijing_universities` SET `university_short_name` = '北工商' WHERE `university_name` = '北京工商大学';
UPDATE `beijing_universities` SET `university_short_name` = '北语' WHERE `university_name` = '北京语言大学';
UPDATE `beijing_universities` SET `university_short_name` = '北体' WHERE `university_name` = '北京体育大学';
UPDATE `beijing_universities` SET `university_short_name` = '国关' WHERE `university_name` = '国际关系学院';
UPDATE `beijing_universities` SET `university_short_name` = '北影' WHERE `university_name` = '北京电影学院';
UPDATE `beijing_universities` SET `university_short_name` = '北舞' WHERE `university_name` = '北京舞蹈学院';
UPDATE `beijing_universities` SET `university_short_name` = '央音' WHERE `university_name` = '中央音乐学院';
UPDATE `beijing_universities` SET `university_short_name` = '中劳' WHERE `university_name` = '中国劳动关系学院';
UPDATE `beijing_universities` SET `university_short_name` = '公大' WHERE `university_name` = '中国人民公安大学';
UPDATE `beijing_universities` SET `university_short_name` = '首体' WHERE `university_name` = '首都体育学院';
UPDATE `beijing_universities` SET `university_short_name` = '信科大' WHERE `university_name` = '北京信息科技大学';
UPDATE `beijing_universities` SET `university_short_name` = '联大' WHERE `university_name` = '北京联合大学';
UPDATE `beijing_universities` SET `university_short_name` = '女子学院' WHERE `university_name` = '中华女子学院';
UPDATE `beijing_universities` SET `university_short_name` = '国科大' WHERE `university_name` = '中国科学院大学';
UPDATE `beijing_universities` SET `university_short_name` = '华电' WHERE `university_name` = '华北电力大学';
UPDATE `beijing_universities` SET `university_short_name` = '北农' WHERE `university_name` = '北京农学院';
UPDATE `beijing_universities` SET `university_short_name` = '消防学院' WHERE `university_name` = '中国消防救援学院';
UPDATE `beijing_universities` SET `university_short_name` = '北工大' WHERE `university_name` = '北京工业大学';
UPDATE `beijing_universities` SET `university_short_name` = '首经贸' WHERE `university_name` = '首都经济贸易大学';
UPDATE `beijing_universities` SET `university_short_name` = '北二外' WHERE `university_name` = '北京第二外国语学院';
UPDATE `beijing_universities` SET `university_short_name` = '中传' WHERE `university_name` = '中国传媒大学';
UPDATE `beijing_universities` SET `university_short_name` = '央美' WHERE `university_name` = '中央美术学院';
UPDATE `beijing_universities` SET `university_short_name` = '国音' WHERE `university_name` = '中国音乐学院';
UPDATE `beijing_universities` SET `university_short_name` = '北服' WHERE `university_name` = '北京服装学院';
UPDATE `beijing_universities` SET `university_short_name` = '社科大' WHERE `university_name` = '中国社会科学院大学';
UPDATE `beijing_universities` SET `university_short_name` = '北中医' WHERE `university_name` = '北京中医药大学';
UPDATE `beijing_universities` SET `university_short_name` = '北建大' WHERE `university_name` = '北京建筑大学';
UPDATE `beijing_universities` SET `university_short_name` = '北印' WHERE `university_name` = '北京印刷学院';
UPDATE `beijing_universities` SET `university_short_name` = '石化学院' WHERE `university_name` = '北京石油化工学院';
UPDATE `beijing_universities` SET `university_short_name` = '首医大' WHERE `university_name` = '首都医科大学';
UPDATE `beijing_universities` SET `university_short_name` = '国戏' WHERE `university_name` = '中国戏曲学院';
UPDATE `beijing_universities` SET `university_short_name` = '电科' WHERE `university_name` = '北京电子科技学院';
UPDATE `beijing_universities` SET `university_short_name` = '物资学院' WHERE `university_name` = '北京物资学院';
UPDATE `beijing_universities` SET `university_short_name` = '北方工大' WHERE `university_name` = '北方工业大学';
UPDATE `beijing_universities` SET `university_short_name` = '外交学院' WHERE `university_name` = '外交学院';
UPDATE `beijing_universities` SET `university_short_name` = '协和' WHERE `university_name` = '北京协和医学院';
