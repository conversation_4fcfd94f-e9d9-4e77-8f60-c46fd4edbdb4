-- 伴学星项目默认消息数据
-- 注意：执行前请确保已有用户数据，这里假设用户ID从1开始

-- 插入默认消息数据
INSERT INTO `banxuexing_messages` (`from_user_id`, `to_user_id`, `teacher_id`, `content`, `type`, `is_read`, `create_time`) VALUES
-- 系统欢迎消息
(0, 1, NULL, '欢迎来到伴学星！这里是您寻找优秀家教老师的最佳平台。', 3, 0, UNIX_TIMESTAMP()),
(0, 2, NULL, '感谢您注册伴学星！如有任何问题，请随时联系我们的客服。', 3, 0, UNIX_TIMESTAMP()),

-- 用户间的咨询消息
(1, 2, 1, '您好，我对您发布的数学家教信息很感兴趣，请问您的时间安排是怎样的？', 1, 0, UNIX_TIMESTAMP() - 3600),
(2, 1, 1, '您好！我的时间比较灵活，周一到周五晚上7-9点都可以，周末全天都有空。您孩子现在是几年级呢？', 1, 1, UNIX_TIMESTAMP() - 3500),
(1, 2, 1, '我孩子现在高二，数学基础不太好，希望能系统性地提升一下。您的教学方式是怎样的？', 1, 1, UNIX_TIMESTAMP() - 3400),
(2, 1, 1, '我会先了解孩子的具体情况，然后制定个性化的学习计划。主要是帮助孩子理解基础概念，然后逐步提升解题能力。', 1, 0, UNIX_TIMESTAMP() - 3300),

-- 更多用户咨询
(1, 3, 2, '老师您好，看到您的英语家教信息，请问您有教过高中英语的经验吗？', 1, 0, UNIX_TIMESTAMP() - 7200),
(3, 1, 2, '您好！我有3年的高中英语教学经验，主要擅长语法和阅读理解的教学。您孩子在英语方面有什么具体的困难吗？', 1, 1, UNIX_TIMESTAMP() - 7100),

(2, 3, NULL, '请问您还有其他科目的家教推荐吗？我孩子物理也需要补习。', 1, 0, UNIX_TIMESTAMP() - 5400),
(3, 2, NULL, '我可以推荐几位物理老师给您，他们都很专业。稍后我把联系方式发给您。', 1, 1, UNIX_TIMESTAMP() - 5300),

-- 课程安排相关消息
(1, 2, 1, '我们可以先安排一次试听课吗？看看孩子的接受程度如何。', 1, 1, UNIX_TIMESTAMP() - 1800),
(2, 1, 1, '当然可以！试听课是免费的，我们可以安排在这周六下午2点，您看怎么样？', 1, 0, UNIX_TIMESTAMP() - 1700),
(1, 2, 1, '好的，那就这周六下午2点。请问是在您那里上课还是可以上门？', 1, 0, UNIX_TIMESTAMP() - 1600),

-- 价格咨询
(4, 2, 1, '请问您的课时费是多少？一般一次课多长时间？', 1, 0, UNIX_TIMESTAMP() - 10800),
(2, 4, 1, '我的课时费是150元/小时，一般建议一次课2小时，这样效果会比较好。', 1, 1, UNIX_TIMESTAMP() - 10700),
(4, 2, 1, '价格还可以接受，请问您最近的时间安排是怎样的？', 1, 1, UNIX_TIMESTAMP() - 10600),

-- 教学方法咨询
(5, 3, 2, '老师，您的英语教学主要侧重哪些方面？我孩子的口语比较弱。', 1, 0, UNIX_TIMESTAMP() - 14400),
(3, 5, 2, '我的教学会根据学生的具体情况来调整。对于口语薄弱的学生，我会增加口语练习的比重，通过情景对话来提升。', 1, 1, UNIX_TIMESTAMP() - 14300),

-- 学习进度反馈
(2, 1, 1, '您孩子这周的学习情况不错，基础概念掌握得比较好，下周我们开始练习一些中等难度的题目。', 1, 0, UNIX_TIMESTAMP() - 86400),
(1, 2, 1, '谢谢老师！孩子回来说您讲得很清楚，他现在对数学的兴趣也提高了不少。', 1, 1, UNIX_TIMESTAMP() - 86300),

-- 课程调整
(1, 3, 2, '老师，下周三我们有事，能不能调到周四同样的时间？', 1, 1, UNIX_TIMESTAMP() - 172800),
(3, 1, 2, '没问题，我周四那个时间段也是空的。我们就改到周四下午3点。', 1, 0, UNIX_TIMESTAMP() - 172700),

-- 推荐其他老师
(2, 4, NULL, '您好，我看您在找物理老师，我可以推荐一位很不错的物理老师给您。', 1, 0, UNIX_TIMESTAMP() - 259200),
(4, 2, NULL, '太好了！请问这位老师的教学经验怎么样？', 1, 1, UNIX_TIMESTAMP() - 259100),

-- 感谢消息
(1, 2, 1, '经过一个月的学习，孩子的数学成绩提升了很多，真的很感谢您的耐心教导！', 1, 1, UNIX_TIMESTAMP() - 604800),
(2, 1, 1, '看到孩子的进步我也很开心！继续保持这个学习状态，相信他会越来越好的。', 1, 0, UNIX_TIMESTAMP() - 604700);
