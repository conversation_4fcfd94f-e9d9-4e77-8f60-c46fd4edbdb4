-- 图片URL迁移脚本：将完整URL转换为相对路径
-- 执行前请先备份数据库！

-- 1. 迁移用户头像 (user表的face_url字段)
-- 将 https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/dev/avatar/xxx 
-- 转换为 dev/avatar/xxx

UPDATE user 
SET face_url = CASE 
    -- COS公有存储桶URL (开发环境)
    WHEN face_url LIKE 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        SUBSTRING(face_url, LENGTH('https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/') + 1)
    
    -- COS私有存储桶URL (开发环境)  
    WHEN face_url LIKE 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        SUBSTRING(face_url, LENGTH('https://treehole-1320255796.cos.ap-beijing.myqcloud.com/') + 1)
    
    -- EdgeOne CDN公有域名 (生产环境)
    WHEN face_url LIKE 'https://public.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(face_url, LENGTH('https://public.bjgaoxiaoshequ.store/') + 1)
    
    -- EdgeOne CDN私有域名 (生产环境)
    WHEN face_url LIKE 'https://private.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(face_url, LENGTH('https://private.bjgaoxiaoshequ.store/') + 1)
    
    -- 旧的本地域名
    WHEN face_url LIKE 'https://www.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(face_url, LENGTH('https://www.bjgaoxiaoshequ.store/') + 1)
    
    -- 本地开发域名
    WHEN face_url LIKE 'http://localhost/%' THEN 
        SUBSTRING(face_url, LENGTH('http://localhost/') + 1)
    
    -- 如果已经是相对路径或其他格式，保持不变
    ELSE face_url 
END
WHERE face_url IS NOT NULL 
  AND face_url != ''
  AND (
    face_url LIKE 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' OR
    face_url LIKE 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' OR
    face_url LIKE 'https://public.bjgaoxiaoshequ.store/%' OR
    face_url LIKE 'https://private.bjgaoxiaoshequ.store/%' OR
    face_url LIKE 'https://www.bjgaoxiaoshequ.store/%' OR
    face_url LIKE 'http://localhost/%'
  );

-- 2. 迁移消息表图片 (message表的images字段)
-- 处理JSON数组格式的图片URL

UPDATE message 
SET images = CASE 
    -- 替换COS公有存储桶URL
    WHEN images LIKE '%https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/', '')
    
    -- 替换COS私有存储桶URL
    WHEN images LIKE '%https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/', '')
    
    -- 替换EdgeOne CDN公有域名
    WHEN images LIKE '%https://public.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://public.bjgaoxiaoshequ.store/', '')
    
    -- 替换EdgeOne CDN私有域名
    WHEN images LIKE '%https://private.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://private.bjgaoxiaoshequ.store/', '')
    
    -- 替换旧的本地域名
    WHEN images LIKE '%https://www.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://www.bjgaoxiaoshequ.store/', '')
    
    -- 替换本地开发域名
    WHEN images LIKE '%http://localhost/%' THEN 
        REPLACE(images, 'http://localhost/', '')
    
    ELSE images 
END
WHERE images IS NOT NULL 
  AND images != ''
  AND images != '[]'
  AND (
    images LIKE '%https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' OR
    images LIKE '%https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' OR
    images LIKE '%https://public.bjgaoxiaoshequ.store/%' OR
    images LIKE '%https://private.bjgaoxiaoshequ.store/%' OR
    images LIKE '%https://www.bjgaoxiaoshequ.store/%' OR
    images LIKE '%http://localhost/%'
  );

-- 3. 迁移帖子表图片 (post表的images字段)

UPDATE post 
SET images = CASE 
    WHEN images LIKE '%https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/', '')
    WHEN images LIKE '%https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/', '')
    WHEN images LIKE '%https://public.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://public.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%https://private.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://private.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%https://www.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://www.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%http://localhost/%' THEN 
        REPLACE(images, 'http://localhost/', '')
    ELSE images 
END
WHERE images IS NOT NULL 
  AND images != ''
  AND images != '[]';

-- 4. 迁移评论表图片 (comment表的images字段)

UPDATE comment 
SET images = CASE 
    WHEN images LIKE '%https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/', '')
    WHEN images LIKE '%https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        REPLACE(images, 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/', '')
    WHEN images LIKE '%https://public.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://public.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%https://private.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://private.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%https://www.bjgaoxiaoshequ.store/%' THEN 
        REPLACE(images, 'https://www.bjgaoxiaoshequ.store/', '')
    WHEN images LIKE '%http://localhost/%' THEN 
        REPLACE(images, 'http://localhost/', '')
    ELSE images 
END
WHERE images IS NOT NULL 
  AND images != ''
  AND images != '[]';

-- 5. 迁移活动表封面图片 (activities表的cover_image字段)

UPDATE activities 
SET cover_image = CASE 
    WHEN cover_image LIKE 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        SUBSTRING(cover_image, LENGTH('https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com/') + 1)
    WHEN cover_image LIKE 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com/%' THEN 
        SUBSTRING(cover_image, LENGTH('https://treehole-1320255796.cos.ap-beijing.myqcloud.com/') + 1)
    WHEN cover_image LIKE 'https://public.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(cover_image, LENGTH('https://public.bjgaoxiaoshequ.store/') + 1)
    WHEN cover_image LIKE 'https://private.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(cover_image, LENGTH('https://private.bjgaoxiaoshequ.store/') + 1)
    WHEN cover_image LIKE 'https://www.bjgaoxiaoshequ.store/%' THEN 
        SUBSTRING(cover_image, LENGTH('https://www.bjgaoxiaoshequ.store/') + 1)
    WHEN cover_image LIKE 'http://localhost/%' THEN 
        SUBSTRING(cover_image, LENGTH('http://localhost/') + 1)
    ELSE cover_image 
END
WHERE cover_image IS NOT NULL 
  AND cover_image != '';

-- 6. 查看迁移结果统计
SELECT 
    '用户头像' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN face_url LIKE 'http%' THEN 1 END) as still_full_urls,
    COUNT(CASE WHEN face_url NOT LIKE 'http%' AND face_url IS NOT NULL AND face_url != '' THEN 1 END) as relative_paths
FROM user
WHERE face_url IS NOT NULL AND face_url != ''

UNION ALL

SELECT 
    '消息图片' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN images LIKE '%http%' THEN 1 END) as still_full_urls,
    COUNT(CASE WHEN images NOT LIKE '%http%' AND images IS NOT NULL AND images != '' AND images != '[]' THEN 1 END) as relative_paths
FROM message
WHERE images IS NOT NULL AND images != '' AND images != '[]'

UNION ALL

SELECT 
    '帖子图片' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN images LIKE '%http%' THEN 1 END) as still_full_urls,
    COUNT(CASE WHEN images NOT LIKE '%http%' AND images IS NOT NULL AND images != '' AND images != '[]' THEN 1 END) as relative_paths
FROM post
WHERE images IS NOT NULL AND images != '' AND images != '[]';

-- 执行完成后的说明：
-- 1. 检查统计结果，确保迁移成功
-- 2. 测试前端图片显示是否正常
-- 3. 如果有问题，可以从备份中恢复数据
