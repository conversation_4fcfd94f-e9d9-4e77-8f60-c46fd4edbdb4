-- 北航专业信息表
-- 创建时间: 2025-06-19

-- 创建大学信息表
DROP TABLE IF EXISTS `universities`;

CREATE TABLE `universities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '大学ID',
  `name` varchar(100) NOT NULL COMMENT '大学名称',
  `short_name` varchar(20) NOT NULL COMMENT '大学简称',
  `code` varchar(20) NOT NULL COMMENT '大学代码',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大学信息表';

-- 插入北航信息
INSERT INTO `universities` (`id`, `name`, `short_name`, `code`) VALUES
(1, '北京航空航天大学', '北航', 'BUAA');

-- 创建专业信息表
DROP TABLE IF EXISTS `buaa_majors`;

CREATE TABLE `buaa_majors` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `department_id` int(11) NOT NULL COMMENT '系号',
  `college_name` varchar(100) NOT NULL COMMENT '学院名称',
  `major_name` varchar(100) NOT NULL COMMENT '专业名称',
  `university_id` int(11) NOT NULL DEFAULT 1 COMMENT '学校ID，1表示北航',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_university_id` (`university_id`),
  KEY `idx_college_name` (`college_name`),
  KEY `idx_major_name` (`major_name`),
  CONSTRAINT `fk_majors_university` FOREIGN KEY (`university_id`) REFERENCES `universities` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='专业信息表';

-- 插入北航专业数据
INSERT INTO `buaa_majors` (`department_id`, `college_name`, `major_name`, `university_id`) VALUES
(1, '材料科学与工程学院', '材料科学与工程', 1),
(1, '材料科学与工程学院', '纳米材料与技术', 1),
(1, '材料科学与工程学院', '环境工程', 1),
(2, '电子信息工程学院', '电子信息工程', 1),
(2, '电子信息工程学院', '电子科学与技术', 1),
(2, '电子信息工程学院', '通信工程', 1),
(2, '电子信息工程学院', '光电信息科学与工程', 1),
(2, '电子信息工程学院', '集成电路设计与集成系统', 1),
(2, '电子信息工程学院', '电磁场与无线技术', 1),
(2, '电子信息工程学院', '交通运输', 1),
(3, '自动化科学与电气工程学院', '电气工程及其自动化', 1),
(3, '自动化科学与电气工程学院', '自动化', 1),
(3, '自动化科学与电气工程学院', '机器人工程', 1),
(4, '能源与动力工程学院', '能源与动力工程', 1),
(4, '能源与动力工程学院', '飞行器动力工程', 1),
(4, '能源与动力工程学院', '空天智能电推进技术', 1),
(5, '航空科学与工程学院', '工程力学', 1),
(5, '航空科学与工程学院', '飞行器设计与工程', 1),
(5, '航空科学与工程学院', '飞行器环境与生命保障工程', 1),
(5, '航空科学与工程学院', '航空航天工程', 1),
(5, '航空科学与工程学院', '智能飞行器技术', 1),
(5, '航空科学与工程学院', '低空技术与工程', 1),
(6, '计算机学院', '计算机科学与技术', 1),
(6, '计算机学院', '虚拟现实技术', 1),
(7, '机械工程及自动化学院', '机械工程', 1),
(7, '机械工程及自动化学院', '工业设计', 1),
(7, '机械工程及自动化学院', '飞行器制造工程', 1),
(7, '机械工程及自动化学院', '微机电系统工程', 1),
(7, '机械工程及自动化学院', '机器人工程', 1),
(7, '机械工程及自动化学院', '智能制造工程', 1),
(8, '经济管理学院', '金融学', 1),
(8, '经济管理学院', '国际经济与贸易', 1),
(8, '经济管理学院', '信息管理与信息系统', 1),
(8, '经济管理学院', '工商管理', 1),
(8, '经济管理学院', '会计学', 1),
(8, '经济管理学院', '工业工程', 1),
(8, '经济管理学院', '经济统计学', 1),
(8, '经济管理学院', '工程管理', 1),
(8, '经济管理学院', '能源经济', 1),
(9, '数学科学学院', '数学与应用数学', 1),
(9, '数学科学学院', '信息与计算科学', 1),
(9, '数学科学学院', '信息安全', 1),
(9, '数学科学学院', '统计学', 1),
(10, '生物与医学工程学院', '生物医学工程', 1),
(10, '生物与医学工程学院', '智能医学工程', 1),
(10, '生物与医学工程学院', '运动康复', 1),
(11, '人文社会科学学院 (公共管理学院)', '经济学', 1),
(11, '人文社会科学学院 (公共管理学院)', '法学', 1),
(11, '人文社会科学学院 (公共管理学院)', '行政管理', 1),
(11, '人文社会科学学院 (公共管理学院)', '新闻学', 1),
(12, '外国语学院', '英语', 1),
(12, '外国语学院', '德语', 1),
(12, '外国语学院', '翻译', 1),
(12, '外国语学院', '法语', 1),
(13, '交通科学与工程学院', '车辆工程', 1),
(13, '交通科学与工程学院', '土木工程', 1),
(13, '交通科学与工程学院', '交通运输', 1),
(13, '交通科学与工程学院', '飞行器适航技术', 1),
(13, '交通科学与工程学院', '智慧交通', 1),
(14, '可靠性与系统工程学院', '飞行器质量与可靠性', 1),
(14, '可靠性与系统工程学院', '安全工程', 1),
(15, '宇航学院', '飞行器设计与工程', 1),
(15, '宇航学院', '飞行器动力工程', 1),
(15, '宇航学院', '探测制导与控制技术', 1),
(15, '宇航学院', '飞行器控制与信息工程', 1),
(15, '宇航学院', '智能飞行器技术', 1),
(15, '宇航学院', '空天智能电推进技术', 1),
(16, '飞行学院', '飞行技术', 1),
(16, '飞行学院', '交通运输', 1),
(16, '飞行学院', '无人驾驶航空器系统工程', 1),
(17, '仪器科学与光电工程学院', '测控技术与仪器', 1),
(17, '仪器科学与光电工程学院', '光电信息科学与工程', 1),
(17, '仪器科学与光电工程学院', '遥感科学与技术', 1),
(17, '仪器科学与光电工程学院', '智能感知工程', 1),
(19, '物理学院', '应用物理学', 1),
(19, '物理学院', '核物理', 1),
(19, '物理学院', '物理学', 1),
(20, '法学院', '法学', 1),
(21, '软件学院', '软件工程', 1),
(24, '中法工程师学院 / 国际通用工程学院', '机械工程', 1),
(24, '中法工程师学院 / 国际通用工程学院', '航空航天工程', 1),
(24, '中法工程师学院 / 国际通用工程学院', '电子与计算机工程', 1),
(26, '新媒体艺术与设计学院', '视觉传达设计', 1),
(26, '新媒体艺术与设计学院', '绘画', 1),
(27, '化学学院', '化学', 1),
(27, '化学学院', '应用化学', 1),
(27, '化学学院', '化学生物学', 1),
(29, '人文与社会科学高等研究院', '新闻学', 1),
(30, '空间与环境学院', '空间科学与技术', 1),
(39, '网络空间安全学院', '信息安全', 1),
(39, '网络空间安全学院', '信息对抗技术', 1),
(39, '网络空间安全学院', '网络空间安全', 1),
(41, '集成电路科学与工程学院', '微电子科学与工程', 1),
(42, '人工智能学院 (人工智能研究院)', '信息与计算科学', 1),
(42, '人工智能学院 (人工智能研究院)', '人工智能', 1);
