-- 创建认证申请表
CREATE TABLE IF NOT EXISTS `auth_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '申请用户ID',
  `username` varchar(100) NOT NULL COMMENT '申请用户名',
  `school_id` int(11) NOT NULL COMMENT '学校ID',
  `school_name` varchar(255) NOT NULL COMMENT '学校名称',
  `image_url` varchar(500) NOT NULL COMMENT '认证图片URL',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '申请状态：pending待审核，approved已通过，rejected已拒绝',
  `reviewed_by` int(11) DEFAULT NULL COMMENT '审核人ID',
  `reviewed_at` datetime DEFAULT NULL COMMENT '审核时间',
  `reason` text DEFAULT NULL COMMENT '审核原因/备注',
  `created_at` datetime NOT NULL COMMENT '申请时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_school_id` (`school_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生认证申请表';

-- 为user表添加verified_university_id字段（如果不存在）
-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'user'
     AND COLUMN_NAME = 'verified_university_id') = 0,
    'ALTER TABLE `user` ADD COLUMN `verified_university_id` int(11) DEFAULT NULL COMMENT ''认证的大学ID'' AFTER `status`;',
    'SELECT ''Column verified_university_id already exists'' as message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'user'
     AND INDEX_NAME = 'idx_verified_university_id') = 0,
    'ALTER TABLE `user` ADD INDEX `idx_verified_university_id` (`verified_university_id`);',
    'SELECT ''Index idx_verified_university_id already exists'' as message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
