-- 修复 reply_to_user_id 字段允许 NULL 值
-- 执行这个SQL来修复数据库表结构

-- 修复食堂窗口评论表
ALTER TABLE `window_comments`
MODIFY COLUMN `reply_to_user_id` int(11) DEFAULT NULL COMMENT '回复的用户ID';

-- 修复了然几分评论表 (原来默认值是0，现在改为NULL)
ALTER TABLE `liaoran_comments`
MODIFY COLUMN `reply_to_user_id` int(11) DEFAULT NULL COMMENT '回复的用户ID';

-- 修复了然几分评论表的parent_id字段也改为NULL
ALTER TABLE `liaoran_comments`
MODIFY COLUMN `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID，NULL表示主评论';

-- 更新现有数据：将0值改为NULL
UPDATE `window_comments` SET `reply_to_user_id` = NULL WHERE `reply_to_user_id` = 0;
UPDATE `window_comments` SET `parent_id` = NULL WHERE `parent_id` = 0;

UPDATE `liaoran_comments` SET `reply_to_user_id` = NULL WHERE `reply_to_user_id` = 0;
UPDATE `liaoran_comments` SET `parent_id` = NULL WHERE `parent_id` = 0;

-- 检查表结构
DESCRIBE `window_comments`;
DESCRIBE `liaoran_comments`;
