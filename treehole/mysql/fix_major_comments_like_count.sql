-- 修复专业评论表的点赞数问题

-- 1. 确保专业评论表存在
CREATE TABLE IF NOT EXISTS `major_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `major_id` int(11) NOT NULL COMMENT '专业ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `images` text COMMENT '评论图片，JSON格式',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID，0表示主评论',
  `reply_to_user_id` int(11) DEFAULT NULL COMMENT '回复的用户ID',
  `reply_to_username` varchar(50) DEFAULT NULL COMMENT '回复的用户名',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1正常，0删除',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_major_id` (`major_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业评论表';

-- 2. 确保专业评论点赞表存在
CREATE TABLE IF NOT EXISTS `major_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `major_id` int(11) NOT NULL COMMENT '专业ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_major_id` (`major_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业评论点赞表';

-- 3. 修复现有评论的NULL值
UPDATE `major_comments` SET `like_count` = 0 WHERE `like_count` IS NULL;
UPDATE `major_comments` SET `reply_count` = 0 WHERE `reply_count` IS NULL;

-- 4. 重新计算所有评论的点赞数
UPDATE `major_comments` mc
SET `like_count` = (
    SELECT COUNT(*)
    FROM `major_comment_likes` mcl
    WHERE mcl.`comment_id` = mc.`id`
);

-- 5. 重新计算所有主评论的回复数
UPDATE `major_comments` mc
SET `reply_count` = (
    SELECT COUNT(*)
    FROM `major_comments` mc2
    WHERE mc2.`parent_id` = mc.`id` AND mc2.`status` = 1
)
WHERE mc.`parent_id` = 0;

-- 6. 确保字段不为NULL（添加约束）
ALTER TABLE `major_comments` 
MODIFY COLUMN `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
MODIFY COLUMN `reply_count` int(11) NOT NULL DEFAULT 0 COMMENT '回复数';
