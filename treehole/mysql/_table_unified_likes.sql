-- --------------------------------------------------------

--
-- 表的结构 `unified_likes` - 统一点赞表
--

CREATE TABLE `unified_likes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：message/comment/reply/liaoran_comment/liaoran_reply',
  `target_id` int NOT NULL COMMENT '目标ID',
  `message_id` int DEFAULT NULL COMMENT '关联帖子ID（用于通知）',
  `comment_id` int DEFAULT NULL COMMENT '关联评论ID（回复点赞时使用）',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_target` (`target_type`, `target_id`),
  K<PERSON>Y `idx_user` (`user_id`),
  KEY `idx_message` (`message_id`),
  KEY `idx_comment` (`comment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一点赞表';

-- --------------------------------------------------------

--
-- 为 `unified_likes` 表添加索引
--

ALTER TABLE `unified_likes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  ADD KEY `idx_target` (`target_type`, `target_id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_message` (`message_id`),
  ADD KEY `idx_comment` (`comment_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

ALTER TABLE `unified_likes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;
