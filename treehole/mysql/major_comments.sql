-- 专业评论表
CREATE TABLE `major_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `major_id` int(11) NOT NULL COMMENT '专业ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `images` text COMMENT '评论图片，JSON格式',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID，0表示主评论',
  `reply_to_user_id` int(11) DEFAULT NULL COMMENT '回复的用户ID',
  `reply_to_username` varchar(50) DEFAULT NULL COMMENT '回复的用户名',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1正常，0删除',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_major_id` (`major_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业评论表';

-- 专业评论点赞表
CREATE TABLE `major_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `major_id` int(11) NOT NULL COMMENT '专业ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_major_id` (`major_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业评论点赞表';
