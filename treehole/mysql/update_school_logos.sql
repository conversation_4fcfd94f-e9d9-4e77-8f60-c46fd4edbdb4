-- 更新学校头像/校徽 SQL
-- 根据university_name字段匹配校徽图片文件

-- 更新北京大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京大学.jpg' WHERE `university_name` = '北京大学';

-- 更新清华大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/清华大学.jpg' WHERE `university_name` = '清华大学';

-- 更新中国人民大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国人民大学.jpg' WHERE `university_name` = '中国人民大学';

-- 更新北京师范大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京师范大学.jpg' WHERE `university_name` = '北京师范大学';

-- 更新北京航空航天大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京航空航天大学.jpg' WHERE `university_name` = '北京航空航天大学';

-- 更新北京理工大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京理工大学.jpg' WHERE `university_name` = '北京理工大学';

-- 更新中国农业大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国农业大学.jpg' WHERE `university_name` = '中国农业大学';

-- 更新中央民族大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中央民族大学.jpg' WHERE `university_name` = '中央民族大学';

-- 更新北京交通大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京交通大学.jpg' WHERE `university_name` = '北京交通大学';

-- 更新北京科技大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京科技大学.jpg' WHERE `university_name` = '北京科技大学';

-- 更新北京邮电大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京邮电大学.jpg' WHERE `university_name` = '北京邮电大学';

-- 更新北京林业大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京林业大学.jpg' WHERE `university_name` = '北京林业大学';

-- 更新北京外国语大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京外国语大学.jpg' WHERE `university_name` = '北京外国语大学';

-- 更新对外经济贸易大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/对外经济贸易大学.jpg' WHERE `university_name` = '对外经济贸易大学';

-- 更新中央财经大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中央财经大学.jpg' WHERE `university_name` = '中央财经大学';

-- 更新中国政法大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国政法大学.jpg' WHERE `university_name` = '中国政法大学';

-- 更新中国地质大学（北京）
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国地质大学（北京）.jpg' WHERE `university_name` = '中国地质大学（北京）';

-- 更新中国矿业大学（北京）
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国矿业大学（北京）.jpg' WHERE `university_name` = '中国矿业大学（北京）';

-- 更新中国石油大学（北京）
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国石油大学（北京）.jpg' WHERE `university_name` = '中国石油大学（北京）';

-- 更新北京化工大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京化工大学.jpg' WHERE `university_name` = '北京化工大学';

-- 更新首都师范大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/首都师范大学.jpg' WHERE `university_name` = '首都师范大学';

-- 更新北京工商大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京工商大学.jpg' WHERE `university_name` = '北京工商大学';

-- 更新北京语言大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京语言大学.jpg' WHERE `university_name` = '北京语言大学';

-- 更新北京体育大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京体育大学.jpg' WHERE `university_name` = '北京体育大学';

-- 更新国际关系学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/国际关系学院.jpg' WHERE `university_name` = '国际关系学院';

-- 更新北京电影学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京电影学院.jpg' WHERE `university_name` = '北京电影学院';

-- 更新北京舞蹈学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京舞蹈学院.jpg' WHERE `university_name` = '北京舞蹈学院';

-- 更新中央音乐学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中央音乐学院.jpg' WHERE `university_name` = '中央音乐学院';

-- 更新中国劳动关系学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国劳动关系学院.jpg' WHERE `university_name` = '中国劳动关系学院';

-- 更新中国人民公安大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国人民公安大学.jpg' WHERE `university_name` = '中国人民公安大学';

-- 更新首都体育学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/首都体育学院.jpg' WHERE `university_name` = '首都体育学院';

-- 更新北京信息科技大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京信息科技大学.jpg' WHERE `university_name` = '北京信息科技大学';

-- 更新北京联合大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京联合大学.jpg' WHERE `university_name` = '北京联合大学';

-- 更新中华女子学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中华女子学院.jpg' WHERE `university_name` = '中华女子学院';

-- 更新中国科学院大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国科学院大学.jpg' WHERE `university_name` = '中国科学院大学';

-- 更新华北电力大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/华北电力大学.jpg' WHERE `university_name` = '华北电力大学';

-- 更新北京工业大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京工业大学.jpg' WHERE `university_name` = '北京工业大学';

-- 更新北京建筑大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京建筑大学.jpg' WHERE `university_name` = '北京建筑大学';

-- 更新北京印刷学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京印刷学院.jpg' WHERE `university_name` = '北京印刷学院';

-- 更新北京石油化工学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京石油化工学院.jpg' WHERE `university_name` = '北京石油化工学院';

-- 更新北京农学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京农学院.jpg' WHERE `university_name` = '北京农学院';

-- 更新首都医科大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/首都医科大学.jpg' WHERE `university_name` = '首都医科大学';

-- 更新北京中医药大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京中医药大学.jpg' WHERE `university_name` = '北京中医药大学';

-- 更新首都经济贸易大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/首都经济贸易大学.jpg' WHERE `university_name` = '首都经济贸易大学';

-- 更新中国传媒大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国传媒大学.jpg' WHERE `university_name` = '中国传媒大学';

-- 更新中央美术学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中央美术学院.jpg' WHERE `university_name` = '中央美术学院';

-- 更新中央戏剧学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中央戏剧学院.jpg' WHERE `university_name` = '中央戏剧学院';

-- 更新北京服装学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京服装学院.jpg' WHERE `university_name` = '北京服装学院';

-- 更新北京物资学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京物资学院.jpg' WHERE `university_name` = '北京物资学院';

-- 更新外交学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/外交学院.jpg' WHERE `university_name` = '外交学院';

-- 更新中国音乐学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国音乐学院.jpg' WHERE `university_name` = '中国音乐学院';

-- 更新中国戏曲学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国戏曲学院.jpg' WHERE `university_name` = '中国戏曲学院';

-- 更新北京第二外国语学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京第二外国语学院.jpg' WHERE `university_name` = '北京第二外国语学院';

-- 更新中国青年政治学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国青年政治学院.jpg' WHERE `university_name` = '中国青年政治学院';

-- 更新中国消防救援学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国消防救援学院.jpg' WHERE `university_name` = '中国消防救援学院';

-- 更新中国社会科学院大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/中国社会科学院大学.jpg' WHERE `university_name` = '中国社会科学院大学';

-- 更新北京电子科技学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京电子科技学院.jpg' WHERE `university_name` = '北京电子科技学院';

-- 更新北方工业大学
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北方工业大学.jpg' WHERE `university_name` = '北方工业大学';

-- 更新北京协和医学院
UPDATE `beijing_universities` SET `logo_url` = 'https://www.bjgaoxiaoshequ.store/中国所有大学校徽图片-200px-jpgs/北京协和医学院.jpg' WHERE `university_name` = '北京协和医学院';
