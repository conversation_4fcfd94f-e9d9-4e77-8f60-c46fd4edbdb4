-- 北京高校信息表添加 university_id 字段迁移指南
-- 
-- 目的：为同一学校的不同校区分配统一的 university_id
-- 例如：北航学院路校区和北航沙河校区都使用 university_id = 5
--
-- 执行步骤：

-- 步骤1：备份现有数据
-- CREATE TABLE beijing_universities_backup AS SELECT * FROM beijing_universities;

-- 步骤2：添加 university_id 字段
ALTER TABLE `beijing_universities` 
ADD COLUMN `university_id` int(11) NOT NULL DEFAULT 0 COMMENT '学校ID（同一学校不同校区使用相同ID）' 
AFTER `id`;

-- 步骤3：添加索引
ALTER TABLE `beijing_universities` 
ADD KEY `idx_university_id` (`university_id`);

-- 步骤4：更新现有数据的 university_id 值
-- 主要985/211高校的 university_id 分配：

UPDATE `beijing_universities` SET `university_id` = 1 WHERE `university_name` = '北京大学';
UPDATE `beijing_universities` SET `university_id` = 2 WHERE `university_name` = '清华大学';
UPDATE `beijing_universities` SET `university_id` = 3 WHERE `university_name` = '中国人民大学';
UPDATE `beijing_universities` SET `university_id` = 4 WHERE `university_name` = '北京师范大学';
UPDATE `beijing_universities` SET `university_id` = 5 WHERE `university_name` = '北京航空航天大学';
UPDATE `beijing_universities` SET `university_id` = 6 WHERE `university_name` = '北京理工大学';
UPDATE `beijing_universities` SET `university_id` = 7 WHERE `university_name` = '中国农业大学';
UPDATE `beijing_universities` SET `university_id` = 8 WHERE `university_name` = '中央民族大学';
UPDATE `beijing_universities` SET `university_id` = 9 WHERE `university_name` = '北京交通大学';
UPDATE `beijing_universities` SET `university_id` = 10 WHERE `university_name` = '北京科技大学';
UPDATE `beijing_universities` SET `university_id` = 11 WHERE `university_name` = '北京邮电大学';
UPDATE `beijing_universities` SET `university_id` = 12 WHERE `university_name` = '北京林业大学';
UPDATE `beijing_universities` SET `university_id` = 13 WHERE `university_name` = '北京外国语大学';
UPDATE `beijing_universities` SET `university_id` = 14 WHERE `university_name` = '对外经济贸易大学';
UPDATE `beijing_universities` SET `university_id` = 15 WHERE `university_name` = '中央财经大学';
UPDATE `beijing_universities` SET `university_id` = 16 WHERE `university_name` = '中国政法大学';
UPDATE `beijing_universities` SET `university_id` = 17 WHERE `university_name` = '中国地质大学(北京)';
UPDATE `beijing_universities` SET `university_id` = 18 WHERE `university_name` = '中国矿业大学(北京)';
UPDATE `beijing_universities` SET `university_id` = 19 WHERE `university_name` = '中国石油大学(北京)';
UPDATE `beijing_universities` SET `university_id` = 20 WHERE `university_name` = '北京化工大学';

-- 其他重要高校：
UPDATE `beijing_universities` SET `university_id` = 21 WHERE `university_name` = '首都师范大学';
UPDATE `beijing_universities` SET `university_id` = 22 WHERE `university_name` = '北京工商大学';
UPDATE `beijing_universities` SET `university_id` = 23 WHERE `university_name` = '北京语言大学';
UPDATE `beijing_universities` SET `university_id` = 24 WHERE `university_name` = '北京体育大学';
UPDATE `beijing_universities` SET `university_id` = 25 WHERE `university_name` = '国际关系学院';
UPDATE `beijing_universities` SET `university_id` = 26 WHERE `university_name` = '北京电影学院';
UPDATE `beijing_universities` SET `university_id` = 27 WHERE `university_name` = '北京舞蹈学院';
UPDATE `beijing_universities` SET `university_id` = 28 WHERE `university_name` = '中央音乐学院';
UPDATE `beijing_universities` SET `university_id` = 29 WHERE `university_name` = '中国劳动关系学院';
UPDATE `beijing_universities` SET `university_id` = 30 WHERE `university_name` = '中国人民公安大学';
UPDATE `beijing_universities` SET `university_id` = 31 WHERE `university_name` = '首都体育学院';
UPDATE `beijing_universities` SET `university_id` = 32 WHERE `university_name` = '北京信息科技大学';
UPDATE `beijing_universities` SET `university_id` = 33 WHERE `university_name` = '北京联合大学';
UPDATE `beijing_universities` SET `university_id` = 34 WHERE `university_name` = '中华女子学院';
UPDATE `beijing_universities` SET `university_id` = 35 WHERE `university_name` = '中国科学院大学';
UPDATE `beijing_universities` SET `university_id` = 36 WHERE `university_name` = '华北电力大学';
UPDATE `beijing_universities` SET `university_id` = 37 WHERE `university_name` = '北京农学院';
UPDATE `beijing_universities` SET `university_id` = 38 WHERE `university_name` = '中国消防救援学院';
UPDATE `beijing_universities` SET `university_id` = 39 WHERE `university_name` = '北京工业大学';
UPDATE `beijing_universities` SET `university_id` = 40 WHERE `university_name` = '首都经济贸易大学';
UPDATE `beijing_universities` SET `university_id` = 41 WHERE `university_name` = '北京第二外国语学院';
UPDATE `beijing_universities` SET `university_id` = 42 WHERE `university_name` = '中国传媒大学';
UPDATE `beijing_universities` SET `university_id` = 43 WHERE `university_name` = '中央美术学院';
UPDATE `beijing_universities` SET `university_id` = 44 WHERE `university_name` = '中国音乐学院';
UPDATE `beijing_universities` SET `university_id` = 45 WHERE `university_name` = '北京服装学院';
UPDATE `beijing_universities` SET `university_id` = 46 WHERE `university_name` = '中国社会科学院大学';
UPDATE `beijing_universities` SET `university_id` = 47 WHERE `university_name` = '北京中医药大学';
UPDATE `beijing_universities` SET `university_id` = 48 WHERE `university_name` = '北京建筑大学';
UPDATE `beijing_universities` SET `university_id` = 49 WHERE `university_name` = '北京印刷学院';
UPDATE `beijing_universities` SET `university_id` = 50 WHERE `university_name` = '北京石油化工学院';
UPDATE `beijing_universities` SET `university_id` = 51 WHERE `university_name` = '首都医科大学';
UPDATE `beijing_universities` SET `university_id` = 52 WHERE `university_name` = '中国戏曲学院';
UPDATE `beijing_universities` SET `university_id` = 53 WHERE `university_name` = '北京电子科技学院';
UPDATE `beijing_universities` SET `university_id` = 54 WHERE `university_name` = '北京物资学院';
UPDATE `beijing_universities` SET `university_id` = 55 WHERE `university_name` = '北方工业大学';
UPDATE `beijing_universities` SET `university_id` = 56 WHERE `university_name` = '外交学院';
UPDATE `beijing_universities` SET `university_id` = 57 WHERE `university_name` = '北京协和医学院';

-- 步骤5：验证更新结果
-- 查看每个学校有多少个校区
SELECT 
    university_id,
    university_name,
    COUNT(*) as campus_count,
    GROUP_CONCAT(short_name ORDER BY sort_order DESC SEPARATOR ', ') as campuses
FROM `beijing_universities` 
WHERE university_id > 0
GROUP BY university_id, university_name 
ORDER BY university_id;

-- 步骤6：检查是否有遗漏的学校（university_id = 0）
SELECT * FROM `beijing_universities` WHERE university_id = 0;

-- 使用示例：
-- 1. 查询北航的所有校区：
-- SELECT * FROM beijing_universities WHERE university_id = 5;

-- 2. 查询有多个校区的学校：
-- SELECT university_id, university_name, COUNT(*) as campus_count 
-- FROM beijing_universities 
-- GROUP BY university_id, university_name 
-- HAVING campus_count > 1;

-- 3. 在应用程序中，可以通过 university_id 来统一管理同一学校的不同校区
-- 例如：用户选择学校时，可以先选择 university_name，再选择具体的校区 short_name
