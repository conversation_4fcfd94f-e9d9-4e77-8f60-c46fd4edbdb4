-- 为用户表添加认证学校ID字段
-- 用于记录用户通过SSO或邮箱认证时所属的学校

-- 1. 添加认证学校ID字段
ALTER TABLE `user`
ADD COLUMN `verified_university_id` int(11) DEFAULT NULL COMMENT '认证学校ID，关联beijing_universities表的university_id字段'
AFTER `school_id`;

-- 2. 添加索引
ALTER TABLE `user`
ADD KEY `idx_verified_university_id` (`verified_university_id`);

-- 3. 添加外键约束（可选，确保数据完整性）
-- 注意：这里关联的是 university_id 而不是 id
ALTER TABLE `user`
ADD CONSTRAINT `fk_user_verified_university`
FOREIGN KEY (`verified_university_id`) REFERENCES `beijing_universities` (`university_id`)
ON DELETE SET NULL ON UPDATE CASCADE;

-- 4. 验证字段添加结果
DESCRIBE `user`;

-- 5. 查看用户表结构
SHOW CREATE TABLE `user`;
