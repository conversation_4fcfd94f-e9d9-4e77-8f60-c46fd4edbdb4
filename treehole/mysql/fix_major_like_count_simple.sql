-- 简单修复专业评论点赞数的SQL脚本

-- 1. 修复NULL值
UPDATE major_comments SET like_count = 0 WHERE like_count IS NULL;
UPDATE major_comments SET reply_count = 0 WHERE reply_count IS NULL;

-- 2. 重新计算所有评论的实际点赞数
UPDATE major_comments mc 
SET like_count = (
    SELECT COUNT(*) 
    FROM major_comment_likes mcl 
    WHERE mcl.comment_id = mc.id
);

-- 3. 验证修复结果（可选，用于检查）
-- SELECT 
--     mc.id,
--     mc.content,
--     mc.like_count as stored_likes,
--     (SELECT COUNT(*) FROM major_comment_likes mcl WHERE mcl.comment_id = mc.id) as actual_likes
-- FROM major_comments mc 
-- WHERE mc.status = 1 
-- ORDER BY mc.created_at DESC 
-- LIMIT 10;
