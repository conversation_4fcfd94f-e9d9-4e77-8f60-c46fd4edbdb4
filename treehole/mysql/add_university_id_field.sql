-- 为北京高校信息表添加 university_id 字段
-- 用于统一管理同一学校的不同校区

-- 1. 如果表已存在，先添加 university_id 字段
ALTER TABLE `beijing_universities` 
ADD COLUMN `university_id` int(11) NOT NULL DEFAULT 0 COMMENT '学校ID（同一学校不同校区使用相同ID）' 
AFTER `id`;

-- 2. 添加索引
ALTER TABLE `beijing_universities` 
ADD KEY `idx_university_id` (`university_id`);

-- 3. 更新现有数据的 university_id 值
-- 为每个学校分配统一的 university_id

-- 北京大学 (university_id = 1)
UPDATE `beijing_universities` SET `university_id` = 1 WHERE `university_name` = '北京大学';

-- 清华大学 (university_id = 2)
UPDATE `beijing_universities` SET `university_id` = 2 WHERE `university_name` = '清华大学';

-- 中国人民大学 (university_id = 3)
UPDATE `beijing_universities` SET `university_id` = 3 WHERE `university_name` = '中国人民大学';

-- 北京师范大学 (university_id = 4)
UPDATE `beijing_universities` SET `university_id` = 4 WHERE `university_name` = '北京师范大学';

-- 北京航空航天大学 (university_id = 5)
UPDATE `beijing_universities` SET `university_id` = 5 WHERE `university_name` = '北京航空航天大学';

-- 北京理工大学 (university_id = 6)
UPDATE `beijing_universities` SET `university_id` = 6 WHERE `university_name` = '北京理工大学';

-- 中国农业大学 (university_id = 7)
UPDATE `beijing_universities` SET `university_id` = 7 WHERE `university_name` = '中国农业大学';

-- 中央民族大学 (university_id = 8)
UPDATE `beijing_universities` SET `university_id` = 8 WHERE `university_name` = '中央民族大学';

-- 北京交通大学 (university_id = 9)
UPDATE `beijing_universities` SET `university_id` = 9 WHERE `university_name` = '北京交通大学';

-- 北京科技大学 (university_id = 10)
UPDATE `beijing_universities` SET `university_id` = 10 WHERE `university_name` = '北京科技大学';

-- 北京邮电大学 (university_id = 11)
UPDATE `beijing_universities` SET `university_id` = 11 WHERE `university_name` = '北京邮电大学';

-- 北京林业大学 (university_id = 12)
UPDATE `beijing_universities` SET `university_id` = 12 WHERE `university_name` = '北京林业大学';

-- 北京外国语大学 (university_id = 13)
UPDATE `beijing_universities` SET `university_id` = 13 WHERE `university_name` = '北京外国语大学';

-- 对外经济贸易大学 (university_id = 14)
UPDATE `beijing_universities` SET `university_id` = 14 WHERE `university_name` = '对外经济贸易大学';

-- 中央财经大学 (university_id = 15)
UPDATE `beijing_universities` SET `university_id` = 15 WHERE `university_name` = '中央财经大学';

-- 中国政法大学 (university_id = 16)
UPDATE `beijing_universities` SET `university_id` = 16 WHERE `university_name` = '中国政法大学';

-- 中国地质大学(北京) (university_id = 17)
UPDATE `beijing_universities` SET `university_id` = 17 WHERE `university_name` = '中国地质大学(北京)';

-- 中国矿业大学(北京) (university_id = 18)
UPDATE `beijing_universities` SET `university_id` = 18 WHERE `university_name` = '中国矿业大学(北京)';

-- 中国石油大学(北京) (university_id = 19)
UPDATE `beijing_universities` SET `university_id` = 19 WHERE `university_name` = '中国石油大学(北京)';

-- 北京化工大学 (university_id = 20)
UPDATE `beijing_universities` SET `university_id` = 20 WHERE `university_name` = '北京化工大学';

-- 首都师范大学 (university_id = 21)
UPDATE `beijing_universities` SET `university_id` = 21 WHERE `university_name` = '首都师范大学';

-- 北京工商大学 (university_id = 22)
UPDATE `beijing_universities` SET `university_id` = 22 WHERE `university_name` = '北京工商大学';

-- 北京语言大学 (university_id = 23)
UPDATE `beijing_universities` SET `university_id` = 23 WHERE `university_name` = '北京语言大学';

-- 北京体育大学 (university_id = 24)
UPDATE `beijing_universities` SET `university_id` = 24 WHERE `university_name` = '北京体育大学';

-- 国际关系学院 (university_id = 25)
UPDATE `beijing_universities` SET `university_id` = 25 WHERE `university_name` = '国际关系学院';

-- 北京电影学院 (university_id = 26)
UPDATE `beijing_universities` SET `university_id` = 26 WHERE `university_name` = '北京电影学院';

-- 北京舞蹈学院 (university_id = 27)
UPDATE `beijing_universities` SET `university_id` = 27 WHERE `university_name` = '北京舞蹈学院';

-- 中央音乐学院 (university_id = 28)
UPDATE `beijing_universities` SET `university_id` = 28 WHERE `university_name` = '中央音乐学院';

-- 中国劳动关系学院 (university_id = 29)
UPDATE `beijing_universities` SET `university_id` = 29 WHERE `university_name` = '中国劳动关系学院';

-- 中国人民公安大学 (university_id = 30)
UPDATE `beijing_universities` SET `university_id` = 30 WHERE `university_name` = '中国人民公安大学';

-- 首都体育学院 (university_id = 31)
UPDATE `beijing_universities` SET `university_id` = 31 WHERE `university_name` = '首都体育学院';

-- 北京信息科技大学 (university_id = 32)
UPDATE `beijing_universities` SET `university_id` = 32 WHERE `university_name` = '北京信息科技大学';

-- 北京联合大学 (university_id = 33)
UPDATE `beijing_universities` SET `university_id` = 33 WHERE `university_name` = '北京联合大学';

-- 中华女子学院 (university_id = 34)
UPDATE `beijing_universities` SET `university_id` = 34 WHERE `university_name` = '中华女子学院';

-- 中国科学院大学 (university_id = 35)
UPDATE `beijing_universities` SET `university_id` = 35 WHERE `university_name` = '中国科学院大学';

-- 华北电力大学 (university_id = 36)
UPDATE `beijing_universities` SET `university_id` = 36 WHERE `university_name` = '华北电力大学';

-- 北京农学院 (university_id = 37)
UPDATE `beijing_universities` SET `university_id` = 37 WHERE `university_name` = '北京农学院';

-- 中国消防救援学院 (university_id = 38)
UPDATE `beijing_universities` SET `university_id` = 38 WHERE `university_name` = '中国消防救援学院';

-- 北京工业大学 (university_id = 39)
UPDATE `beijing_universities` SET `university_id` = 39 WHERE `university_name` = '北京工业大学';

-- 首都经济贸易大学 (university_id = 40)
UPDATE `beijing_universities` SET `university_id` = 40 WHERE `university_name` = '首都经济贸易大学';

-- 北京第二外国语学院 (university_id = 41)
UPDATE `beijing_universities` SET `university_id` = 41 WHERE `university_name` = '北京第二外国语学院';

-- 中国传媒大学 (university_id = 42)
UPDATE `beijing_universities` SET `university_id` = 42 WHERE `university_name` = '中国传媒大学';

-- 中央美术学院 (university_id = 43)
UPDATE `beijing_universities` SET `university_id` = 43 WHERE `university_name` = '中央美术学院';

-- 中国音乐学院 (university_id = 44)
UPDATE `beijing_universities` SET `university_id` = 44 WHERE `university_name` = '中国音乐学院';

-- 北京服装学院 (university_id = 45)
UPDATE `beijing_universities` SET `university_id` = 45 WHERE `university_name` = '北京服装学院';

-- 中国社会科学院大学 (university_id = 46)
UPDATE `beijing_universities` SET `university_id` = 46 WHERE `university_name` = '中国社会科学院大学';

-- 北京中医药大学 (university_id = 47)
UPDATE `beijing_universities` SET `university_id` = 47 WHERE `university_name` = '北京中医药大学';

-- 北京建筑大学 (university_id = 48)
UPDATE `beijing_universities` SET `university_id` = 48 WHERE `university_name` = '北京建筑大学';

-- 北京印刷学院 (university_id = 49)
UPDATE `beijing_universities` SET `university_id` = 49 WHERE `university_name` = '北京印刷学院';

-- 北京石油化工学院 (university_id = 50)
UPDATE `beijing_universities` SET `university_id` = 50 WHERE `university_name` = '北京石油化工学院';

-- 首都医科大学 (university_id = 51)
UPDATE `beijing_universities` SET `university_id` = 51 WHERE `university_name` = '首都医科大学';

-- 中国戏曲学院 (university_id = 52)
UPDATE `beijing_universities` SET `university_id` = 52 WHERE `university_name` = '中国戏曲学院';

-- 北京电子科技学院 (university_id = 53)
UPDATE `beijing_universities` SET `university_id` = 53 WHERE `university_name` = '北京电子科技学院';

-- 北京物资学院 (university_id = 54)
UPDATE `beijing_universities` SET `university_id` = 54 WHERE `university_name` = '北京物资学院';

-- 北方工业大学 (university_id = 55)
UPDATE `beijing_universities` SET `university_id` = 55 WHERE `university_name` = '北方工业大学';

-- 外交学院 (university_id = 56)
UPDATE `beijing_universities` SET `university_id` = 56 WHERE `university_name` = '外交学院';

-- 北京协和医学院 (university_id = 57)
UPDATE `beijing_universities` SET `university_id` = 57 WHERE `university_name` = '北京协和医学院';

-- 4. 验证更新结果
SELECT
    university_id,
    university_name,
    COUNT(*) as campus_count,
    GROUP_CONCAT(short_name ORDER BY sort_order DESC) as campuses
FROM `beijing_universities`
WHERE university_id > 0
GROUP BY university_id, university_name
ORDER BY university_id;
