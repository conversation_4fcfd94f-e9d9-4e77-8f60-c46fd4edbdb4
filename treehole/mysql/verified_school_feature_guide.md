# 用户认证学校功能说明

## 功能概述

为用户表添加了 `verified_university_id` 字段，用于记录用户通过SSO认证或邮箱认证时所属的学校ID。这样可以区分用户当前选择的学校（`school_id`）和用户实际认证的学校（`verified_university_id`）。

## 数据库修改

### 1. 用户表新增字段

```sql
-- 添加认证学校ID字段
ALTER TABLE `user`
ADD COLUMN `verified_university_id` int(11) DEFAULT NULL COMMENT '认证学校ID，关联beijing_universities表的university_id字段'
AFTER `school_id`;

-- 添加索引
ALTER TABLE `user`
ADD KEY `idx_verified_university_id` (`verified_university_id`);

-- 添加外键约束
ALTER TABLE `user`
ADD CONSTRAINT `fk_user_verified_university`
FOREIGN KEY (`verified_university_id`) REFERENCES `beijing_universities` (`university_id`)
ON DELETE SET NULL ON UPDATE CASCADE;
```

### 2. 字段说明

- `school_id`: 用户当前选择的校区ID（关联 beijing_universities.id，可以随时更改）
- `verified_university_id`: 用户认证时的学校ID（关联 beijing_universities.university_id，认证时自动设置，代表用户真实所属学校）

## 后端功能修改

### 1. SSO认证 (Sen.php)

- 认证成功时，根据用户当前选择的校区ID查询对应的学校ID，并设置为 `verified_university_id`
- 返回消息中包含认证学校信息（显示学校全名）

### 2. 邮箱认证 (Email.php)

- 认证成功时，根据用户当前选择的校区ID查询对应的学校ID，并设置为 `verified_university_id`
- 返回消息中包含认证学校信息（显示学校全名）

### 3. 用户信息查询 (User.php)

- 登录和注册时返回用户信息包含 `verified_university_id` 字段
- 自动查询并返回 `verified_school_info` 认证学校详细信息
- 新增 `getVerifiedSchoolInfo` 接口专门查询用户认证学校信息

## API接口

### 1. 获取用户认证学校信息

**接口地址**: `POST /index.php/User/getVerifiedSchoolInfo`

**请求头**:
```
Content-Type: application/json
token: {用户token}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "user_id": 123,
        "username": "张三",
        "status": "verified",
        "verified_university_id": 5,
        "verified_school_info": {
            "id": 12,
            "district": "海淀区",
            "full_name": "北京航空航天大学 - 学院路校区",
            "short_name": "北航(学院路)",
            "university_short_name": "北航",
            "university_name": "北京航空航天大学",
            "logo_url": "https://example.com/logo.jpg",
            "university_id": 5
        }
    }
}
```

### 2. 用户登录/注册响应

现在用户登录和注册的响应中会包含以下额外字段：
- `verified_university_id`: 认证学校ID
- `verified_school_info`: 认证学校详细信息（如果存在）

## 使用场景

### 1. 学校认证流程

1. 用户选择校区（设置 `school_id`）
2. 用户进行SSO认证或邮箱认证
3. 认证成功后，系统根据校区ID查询对应的学校ID，并设置 `verified_university_id`
4. 用户的认证学校信息被永久记录

### 2. 数据查询和验证

- 可以通过 `verified_university_id` 查询用户真实所属学校
- 可以验证用户是否已完成学校认证
- 可以统计各学校的认证用户数量
- 同一学校不同校区的用户会有相同的 `verified_university_id`

### 3. 权限控制

- 某些功能可以基于 `verified_university_id` 进行权限控制
- 确保用户只能访问其认证学校的相关资源
- 可以实现学校级别的权限管理（而不是校区级别）

## 注意事项

1. **认证时机**: 只有在SSO认证或邮箱认证成功时才会设置 `verified_university_id`
2. **数据一致性**: 认证时会根据用户当前选择的校区ID查询对应的学校ID，所以用户需要先选择正确的校区再进行认证
3. **不可更改**: 一旦设置了 `verified_university_id`，建议不要随意更改，除非有特殊的管理需求
4. **向后兼容**: 现有用户的 `verified_university_id` 为 NULL，不影响现有功能
5. **外键约束**: 启用了外键约束确保数据完整性，防止无效的学校ID

## 执行步骤

1. 执行数据库修改SQL：`source treehole/mysql/add_verified_school_id_field.sql`
2. 重启后端服务使代码修改生效
3. 测试SSO认证和邮箱认证功能
4. 验证用户信息查询接口返回正确的认证学校信息
