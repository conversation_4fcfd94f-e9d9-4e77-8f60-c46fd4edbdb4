# Apache虚拟主机配置示例
# 请将此配置添加到Apache的虚拟主机配置文件中

<VirtualHost *:80>
    ServerName **************
    DocumentRoot "您的项目路径/treehole/public"
    DirectoryIndex index.php home.php index.html
    
    <Directory "您的项目路径/treehole/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog logs/ip-error.log
    CustomLog logs/ip-access.log common
</VirtualHost>

# 注意：
# 1. 将"您的项目路径"替换为实际的完整路径
# 2. 确保路径使用正斜杠 / 而不是反斜杠 \
# 3. 保存后重启Apache服务
