<?php
// 简单的COS配置测试脚本
require_once 'vendor/autoload.php';

use Qcloud\Cos\Client;

echo "=== COS配置测试 ===\n";

// 直接读取配置文件
$config = include 'config/cos.php';

echo "地域: " . $config['region'] . "\n";
echo "公有存储桶: " . $config['buckets']['public']['bucket'] . "\n";
echo "私有存储桶: " . $config['buckets']['private']['bucket'] . "\n";

// 检查配置是否已填写
if ($config['credentials']['secretId'] === 'your_secret_id_here') {
    echo "✗ 请先在 config/cos.php 中填写正确的 SecretId\n";
    exit;
}

if ($config['credentials']['secretKey'] === 'your_secret_key_here') {
    echo "✗ 请先在 config/cos.php 中填写正确的 SecretKey\n";
    exit;
}

if ($config['buckets']['public']['bucket'] === 'your-public-bucket-name-here') {
    echo "✗ 请先在 config/cos.php 中填写正确的公有存储桶名称\n";
    exit;
}

if ($config['buckets']['private']['bucket'] === 'your-private-bucket-name-here') {
    echo "✗ 请先在 config/cos.php 中填写正确的私有存储桶名称\n";
    exit;
}

// 测试客户端连接
try {
    $client = new Client([
        'region' => $config['region'],
        'credentials' => $config['credentials']
    ]);
    echo "✓ COS客户端创建成功\n";

    // 测试公有存储桶访问
    $result = $client->headBucket([
        'Bucket' => $config['buckets']['public']['bucket']
    ]);
    echo "✓ 公有存储桶访问正常\n";

    // 测试私有存储桶访问
    $result = $client->headBucket([
        'Bucket' => $config['buckets']['private']['bucket']
    ]);
    echo "✓ 私有存储桶访问正常\n";

} catch (Exception $e) {
    echo "✗ COS连接失败: " . $e->getMessage() . "\n";
    exit;
}

echo "\n=== 测试完成 ===\n";
echo "✓ 配置正确！现在可以在前端测试上传功能了。\n";
?>
