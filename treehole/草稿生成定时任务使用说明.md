# 草稿生成定时任务使用说明

## 功能介绍
这个定时任务会在每天17:00自动调用草稿生成接口，生成微信公众号草稿。

## 文件说明
- `treehole/app/command/DraftTask.php` - 定时任务主文件
- `treehole/config/draft_task.php` - 配置文件（可选，当前直接在代码中配置）

## 使用方法

### 1. 启动定时任务
```bash
php think draft-task start
```

### 2. 查看任务状态
```bash
php think draft-task status
```

### 3. 停止任务
```bash
php think draft-task stop
```

### 4. 强制停止任务
```bash
php think draft-task stop force
```

## 参数配置

在 `DraftTask.php` 文件的 `getDraftConfig()` 方法中可以修改以下参数：

### API接口地址
```php
'api_url' => 'https://www.bjgaoxiaoshequ.store/SimpleDraftGenerator/generateDraft'
```

### 请求参数
```php
'params' => [
    'limit' => 20,              // 获取消息数量，建议10-30条
    'auto_publish' => 0,        // 是否自动发布，0=仅生成草稿，1=生成并自动发布
    'cover_image' => '',        // 封面图片URL，留空使用默认封面
]
```

### 超时时间
```php
'timeout' => 120,  // 请求超时时间（秒）
```

## 执行时间修改

如果需要修改执行时间（默认17:00），在 `getSecondsUntilNextRun()` 方法中修改：

```php
$today17 = strtotime(date('Y-m-d 17:00:00'));  // 改为其他时间，如 18:30:00
```

## 注意事项

1. **确保EasyTask库已安装**：定时任务依赖EasyTask库
2. **权限问题**：确保runtime目录有写入权限
3. **进程管理**：任务启动后会在后台运行，可通过status命令查看状态
4. **服务器重启**：服务器重启后需要重新启动定时任务
5. **日志查看**：任务执行信息会直接输出到控制台

## 常见问题

### Q: 如何确认任务是否正常运行？
A: 使用 `php think draft-task status` 查看任务状态

### Q: 如何修改执行时间？
A: 修改 `getSecondsUntilNextRun()` 方法中的时间设置

### Q: 如何修改请求参数？
A: 修改 `getDraftConfig()` 方法中的 `params` 数组

### Q: 任务执行失败怎么办？
A: 检查网络连接、API接口是否正常、参数是否正确

## 示例

### 修改为每天18:30执行
```php
private function getSecondsUntilNextRun(): int
{
    $now = time();
    $todayTarget = strtotime(date('Y-m-d 18:30:00'));  // 修改为18:30
    
    if ($now >= $todayTarget) {
        $nextTarget = strtotime('+1 day', $todayTarget);
    } else {
        $nextTarget = $todayTarget;
    }
    
    return $nextTarget - $now;
}
```

### 修改为自动发布
```php
'params' => [
    'limit' => 20,
    'auto_publish' => 1,        // 改为1，自动发布
    'cover_image' => '',
]
```
