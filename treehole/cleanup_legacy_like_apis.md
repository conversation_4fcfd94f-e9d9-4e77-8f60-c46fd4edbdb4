# 清理旧点赞接口指南

## 📋 清理步骤

### 阶段1：标记废弃（已完成）
- ✅ 在 `Like.php` 中为 `doLike`、`doLike2`、`doLike3` 添加 `@deprecated` 标记
- ✅ 创建统一点赞接口 `unifiedLike`
- ✅ 前端添加配置选项支持新旧API切换

### 阶段2：测试新接口
在删除旧接口前，请确保：

1. **数据库迁移完成**
   ```sql
   -- 检查统一点赞表是否创建成功
   SHOW TABLES LIKE 'unified_likes';
   
   -- 检查数据迁移是否完整
   SELECT target_type, COUNT(*) FROM unified_likes GROUP BY target_type;
   ```

2. **新接口测试**
   - 测试帖子点赞：`POST /like/unifiedLike` with `type=message`
   - 测试评论点赞：`POST /like/unifiedLike` with `type=comment`
   - 测试回复点赞：`POST /like/unifiedLike` with `type=reply`

3. **前端组件测试**
   - 测试 `like-button` 组件在各个页面的工作情况
   - 确认点赞状态同步正常
   - 验证事件通知机制

### 阶段3：切换到新接口
修改前端配置：

```javascript
// miniprogram1/utils/like-util.js
const CONFIG = {
  USE_UNIFIED_API: true,        // 启用统一API
  ENABLE_LEGACY_FALLBACK: true  // 保留降级机制
}
```

### 阶段4：监控和观察
观察一段时间（建议1-2周），确保：
- 没有错误日志
- 用户反馈正常
- 性能表现良好

### 阶段5：删除旧接口

#### 5.1 删除后端旧方法
```php
// 在 treehole/app/controller/Like.php 中删除以下方法：
// - doLike()
// - doLike2() 
// - doLike3()
```

#### 5.2 删除前端旧代码
```javascript
// 在各页面 .js 文件中删除旧的点赞方法：
// - dolike()
// - dolike2()
// - dolike3()
```

#### 5.3 清理配置
```javascript
// miniprogram1/utils/like-util.js
const CONFIG = {
  USE_UNIFIED_API: true,         // 保持启用
  ENABLE_LEGACY_FALLBACK: false  // 关闭降级机制
}

// 删除 LEGACY_LIKE_API_MAP 和相关降级代码
```

## 🗂️ 需要清理的文件列表

### 后端文件
- `treehole/app/controller/Like.php` - 删除 doLike、doLike2、doLike3 方法

### 前端文件
- `miniprogram1/pages/fold1/home/<USER>
- `miniprogram1/packageEmoji/pages/messageDetail/messageDetail.js` - 删除 dolike、dolike2、dolike3 方法
- `miniprogram1/pages/fold1/hot/hot.js` - 删除 dolike 方法（如果存在）
- `miniprogram1/pages/fold2/xuqiu/xuqiu.js` - 删除 dolike 方法（如果存在）

### 数据库清理
```sql
-- 在确认新系统稳定运行后，可以删除旧的点赞表
DROP TABLE IF EXISTS `message_like_backup`;
DROP TABLE IF EXISTS `comment_like_backup`;
DROP TABLE IF EXISTS `reply_like_backup`;
DROP TABLE IF EXISTS `liaoran_comment_likes_backup`;
```

## ⚠️ 注意事项

1. **渐进式清理**：不要一次性删除所有旧代码，分阶段进行
2. **备份重要**：删除前确保有完整的代码和数据库备份
3. **用户通知**：如果是生产环境，考虑提前通知用户可能的短暂影响
4. **回滚准备**：准备快速回滚方案，以防出现问题

## 🔍 验证清理效果

清理完成后，检查：
- [ ] 旧API接口已删除
- [ ] 前端不再调用旧方法
- [ ] 数据库旧表已清理
- [ ] 代码中无废弃标记
- [ ] 功能正常工作
- [ ] 性能有所提升

## 📈 预期收益

完成清理后，您将获得：
- 🎯 **统一的点赞逻辑**：所有点赞操作使用同一套代码
- 🚀 **更好的性能**：减少重复代码和数据库查询
- 🛠️ **更易维护**：只需维护一个点赞接口
- 📊 **统一的数据**：所有点赞数据在同一张表中
- 🔧 **更好的扩展性**：新增点赞类型只需配置
