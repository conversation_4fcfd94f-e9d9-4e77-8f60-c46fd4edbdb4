/* 筛选弹窗组件样式 */
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(3px);
}

.popup-show {
  visibility: visible;
  opacity: 1;
}

.popup-content {
  background-color: #ffffff;
  width: 100%;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 30rpx 30rpx 40rpx;
  position: relative;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.popup-show .popup-content {
  transform: translateY(0);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 36rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  text-align: center;
}

.popup-close {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background-color: #f6f6f6;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.popup-close:active {
  transform: translateY(-50%) scale(0.95);
  background-color: #eeeeee;
}

.popup-close image {
  width: 30rpx;
  height: 30rpx;
}

/* 选项网格样式 */
.option-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 36rpx;
  margin-top: 20rpx;
  padding: 0 10rpx;
}

.option-item {
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
}

.option-item:active {
  transform: scale(0.95);
}

.option-icon {
  width: 108rpx;
  height: 108rpx;
  border-radius: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  transition: all 0.2s;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.option-icon.selected {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.option-icon image {
  width: 52rpx;
  height: 52rpx;
}

.selected-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.selected-badge image {
  width: 20rpx;
  height: 20rpx;
}

.option-text {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-top: 6rpx;
}
