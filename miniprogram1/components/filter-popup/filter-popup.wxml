<!-- 筛选弹窗组件 -->
<view class="popup-container {{show ? 'popup-show' : ''}}" catchtap="onMaskTap" catchtouchmove="preventTouchMove" wx:if="{{show || closing}}">
  <view class="popup-content" catchtap="stopPropagation">
    <view class="popup-header">
      <view class="popup-title">{{title}}</view>
      <view class="popup-close" catchtap="onCloseTap">
        <image src="/images/guanbi.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 筛选选项网格 -->
    <view class="option-row">
      <view
        class="option-item"
        wx:for="{{options}}"
        wx:key="value"
        bindtap="onOptionTap"
        data-value="{{item.value}}"
      >
        <view class="option-icon {{selectedValue === item.value ? 'selected' : ''}}" style="background-color: {{item.bgColor}};">
          <image src="{{item.icon}}" mode="aspectFit"></image>
          <view wx:if="{{selectedValue === item.value}}" class="selected-badge">
            <image src="/images/duigou.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="option-text">{{item.label}}</view>
      </view>
    </view>
  </view>
</view>
