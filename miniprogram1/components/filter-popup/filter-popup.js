Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 弹窗标题
    title: {
      type: String,
      value: '请选择'
    },
    // 选项列表
    options: {
      type: Array,
      value: []
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    closing: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 阻止滚动穿透
    preventTouchMove() {
      return false;
    },

    // 阻止事件冒泡
    stopPropagation() {
      return false;
    },

    // 点击遮罩层
    onMaskTap() {
      this.closePopup();
    },

    // 点击关闭按钮
    onCloseTap() {
      this.closePopup();
    },

    // 点击选项
    onOptionTap(e) {
      const value = e.currentTarget.dataset.value;

      // 触发选择事件
      this.triggerEvent('select', {
        value: value
      });

      // 关闭弹窗
      this.closePopup();
    },

    // 关闭弹窗
    closePopup() {
      this.setData({
        closing: true
      });

      // 触发关闭事件
      this.triggerEvent('close');

      // 延迟隐藏，等待动画完成
      setTimeout(() => {
        this.setData({
          closing: false
        });
      }, 300);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除时执行
    }
  }
});
