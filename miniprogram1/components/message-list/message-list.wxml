<!--components/message-list/message-list.wxml-->
<!-- 根据类型显示不同的UI -->
<view wx:if="{{type === 'published'}}" class="published-container">
  <!-- 我发布的内容 - 使用原来的卡片式布局 -->
  <view wx:if="{{loading && messages.length === 0}}" class="loading-container">
    <view class="loading-dots"></view>
  </view>

  <view wx:elif="{{messages.length > 0}}" class="content">
    <view wx:for="{{messages}}" wx:key="id" class="num-item"
          bindtap="onMessageClick" data-message="{{item}}">
      <!-- 用户信息行 -->
      <view class="touxiang1">
        <view style="display: flex; align-items: center;">
          <image src="{{item.face_url}}" class="user-avatar-img"></image>
          <view style="display: flex; flex-direction: column;">
            <view style="display: flex; align-items: center;">
              <text style="margin-left:15rpx;">{{item.username}}</text>
            </view>
          </view>
        </view>
        <!-- 分类标签 -->
        <view>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '99'}}">寻找搭子</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '2'}}">发条说说</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '3'}}">校园交易</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '4'}}">告白倾诉</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '5'}}">拼车交流</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '6'}}">失物寻找</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '71'}}">房屋出租</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '72'}}">租房求助</text>
          <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '8'}}">赚点外快</text>
        </view>
      </view>

      <!-- 内容 -->
      <view class="text">{{item.content}}</view>

      <!-- 图片 -->
      <view wx:if="{{item.images && item.images.length > 0}}">
        <view class="image-container">
          <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
            <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
          </view>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="kuang">
        <view class="gradient-text">{{item.time}}</view>
        <view class="last">
          <image src="/images/pinglun2.png" mode="aspectFit" class="action-icon" />
          <view style="margin-left: 10rpx;">{{item.comment_count}}</view>
          <like-button
            type="message"
            target-id="{{item.id}}"
            is-liked="{{item.is_liked}}"
            like-count="{{item.like_count}}"
            size="normal"
            custom-class="message-like-btn"
            bind:likechange="onLikeChange"
          />
        </view>
      </view>
    </view>
    <view style="height: 30rpx;"></view>
  </view>

  <view wx:else class="empty-tip">
    <image src="/images/dangshidati-01.png" mode="aspectFit" class="empty-image"/>
    <text>还没有发布过内容哦~</text>
  </view>
</view>

<!-- 其他类型消息 - 使用通知样式 -->
<view wx:else class="message-list-container">
  <!-- 加载状态 -->
  <block wx:if="{{loading && messages.length === 0}}">
    <view class="loading-container">
      <view class="loading-dots"></view>
    </view>
  </block>

  <!-- 错误状态 -->
  <block wx:elif="{{error}}">
    <view class="error-container">
      <image src="/images/cuowu.png" class="error-icon"/>
      <text class="error-text">{{error}}</text>
      <button class="retry-btn" bindtap="onRetry">重试</button>
    </view>
  </block>

  <!-- 消息列表 -->
  <block wx:elif="{{messages.length > 0}}">
    <scroll-view
      class="message-scroll"
      scroll-y
      refresher-enabled
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore">

      <view class="message-list">
        <view class="message-item {{item.highlighted ? 'highlighted' : ''}}"
              wx:for="{{messages}}"
              wx:key="id"
              bindtap="onMessageClick"
              data-message="{{item}}">

          <!-- 头像 -->
          <view class="user-avatar">
            <image class="avatar" src="{{item.avatar}}" mode="aspectFill"/>
            <!-- 未读状态点 -->
            <view wx:if="{{!item.is_read}}" class="unread-dot"></view>
          </view>

          <!-- 消息内容 -->
          <view class="message-content">
            <!-- 用户名 -->
            <view class="user-name">{{item.username}}</view>

            <!-- 动作行 -->
            <view class="action-line">
              <view class="action-text">
                <text class="action-desc">{{item.actionText}}</text>
                <text class="time">{{item.time}}</text>
              </view>
            </view>

            <!-- 消息内容 -->
            <view wx:if="{{item.content}}" class="content-text">
              <text user-select="true">{{item.content}}</text>
            </view>

            <!-- 图片列表 -->
            <view wx:if="{{item.images && item.images.length > 0}}" class="content-images">
              <image
                wx:for="{{item.images}}"
                wx:key="*this"
                wx:for-item="image"
                src="{{image}}"
                mode="aspectFill"
                class="content-image"
              />
            </view>

            <!-- 原始内容引用 -->
            <view wx:if="{{item.originalContent}}" class="original-content">
              <view class="quote-mark"></view>
              <text class="original-text" user-select="true">{{item.originalContent}}</text>
            </view>
          </view>

          <!-- 右侧图片 -->
          <view class="right-image" wx:if="{{item.image}}">
            <image class="content-thumb" src="{{item.image}}" mode="aspectFill"/>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-dots small"></view>
        <text>加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:elif="{{!hasMore && messages.length > 0}}" class="no-more">
        <text>没有更多了</text>
      </view>
    </scroll-view>
  </block>

  <!-- 空状态 -->
  <view wx:else class="empty-tip">
    <image src="{{emptyConfig.icon}}" mode="aspectFit" class="empty-image"/>
    <text>{{emptyConfig.text}}</text>
  </view>
</view>
