/* components/message-list/message-list.wxss */

/* 我发布的内容样式 - 使用原来的卡片布局 */
.published-container {
  min-height: 100vh;
  height: auto;
  background-color: rgb(244, 244, 249);
}

.content {
  padding: 0;
}

.num-item {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: 90%;
  background-color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
}

.text {
  font-size: 32rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0;
}

.touxiang1{
  margin-bottom: 15rpx;
  height: 70rpx;
  display: flex;
  background-color:transparent;
  border: 3rpx ;
  align-items: flex-start;
  justify-content: space-between;
}

.user-avatar-img {
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

.image-container {
  margin-top: 8rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.image-item {
  margin-left: 0rpx;
  flex: 0 0 auto;
  margin-right: 18rpx;
}

.uniform-image {
  width: 198rpx;
  height: 198rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last{
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.action-icon {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
}

.gradient-text {
  font-size: 24rpx;
  color: #808080;
  line-height: 35rpx;
}

/* 通用样式 */
.message-list-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.message-scroll {
  height: 100%;
}

.message-list {
  width: 100%;
  padding-top: 16rpx;
  background-color: #ffffff;
}

.message-item {
  display: flex;
  background: #ffffff;
  padding: 32rpx 32rpx;
  position: relative;
}

.message-item::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1px;
  background-color: #eee;
}

.message-item:last-child::after {
  display: none;
}

.message-item.highlighted {
  background-color: #f8f9fa;
}

.message-item:active {
  background-color: #f5f5f5;
}

.user-avatar {
  margin-right: 24rpx;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.action-line {
  margin-bottom: 12rpx;
}

.action-text {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.action-desc {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  min-width: 0;
}

.time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.content-text {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.content-text text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.content-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.content-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.original-content {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
  margin-top: 8rpx;
}

.quote-mark {
  width: 4rpx;
  height: 32rpx;
  background: #667eea;
  margin-right: 12rpx;
  display: inline-block;
  vertical-align: top;
}

.original-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.right-image {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.content-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-dots {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-more text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}

.no-more text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 260rpx 0;
  margin-top: -100rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.empty-tip text {
  color: #999;
  font-size: 28rpx;
}
