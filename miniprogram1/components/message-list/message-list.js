/**
 * 通用消息列表组件
 * 支持点赞、评论、通知、发布等不同类型的消息列表
 */

const { messageAPI, handleError } = require('../../utils/api')
const { processMessageImages } = require('../../utils/imageUtil')
const { LIKE_TYPES } = require('../../utils/like-util.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 消息类型：likes, replies, notifications, published
    type: {
      type: String,
      value: 'notifications'
    },
    // 页面标题
    title: {
      type: String,
      value: '消息列表'
    },
    // 空状态配置
    emptyConfig: {
      type: Object,
      value: {
        icon: '/images/xiaoxi.png',
        text: '暂无消息'
      }
    },
    // 是否自动加载
    autoLoad: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    messages: [],
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    error: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化加载
     */
    async onLoad() {
      if (this.properties.autoLoad) {
        await this.loadMessages(true)
      }
    },

    /**
     * 加载消息列表
     * @param {Boolean} refresh 是否刷新
     */
    async loadMessages(refresh = false) {
      if (this.data.loading && !refresh) return
      if (this.data.loadingMore && !refresh) return

      try {
        // 设置加载状态
        if (refresh) {
          this.setData({
            refreshing: true,
            page: 1,
            hasMore: true,
            error: null
          })
        } else {
          this.setData({ loadingMore: true })
        }

        // 根据类型调用不同的API
        const apiMethod = this.getApiMethod()
        const result = await apiMethod(this.data.page, this.data.pageSize)

        // 处理数据
        const newMessages = this.formatMessages(result.data || [])
        const messages = refresh ? newMessages : [...this.data.messages, ...newMessages]

        this.setData({
          messages,
          page: this.data.page + 1,
          hasMore: newMessages.length === this.data.pageSize,
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: null
        })

        // 标记为已读
        this.markAsRead()

      } catch (error) {
        console.error('Load messages error:', error)
        this.setData({
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: handleError(error, false)
        })
      }
    },

    /**
     * 获取对应的API方法
     */
    getApiMethod() {
      const apiMap = {
        likes: messageAPI.getLikes,
        replies: messageAPI.getReplies,
        notifications: messageAPI.getNotifications,
        published: messageAPI.getMyPublished
      }
      return apiMap[this.properties.type] || messageAPI.getNotifications
    },

    /**
     * 格式化消息数据
     */
    formatMessages(messages) {
      return messages.map(item => {
        // 先处理图片URL
        const processedItem = processMessageImages(item)

        // 根据不同类型格式化数据
        switch (this.properties.type) {
          case 'likes':
            return this.formatLikeMessage(processedItem)
          case 'replies':
            return this.formatReplyMessage(processedItem)
          case 'notifications':
            return this.formatNotificationMessage(processedItem)
          case 'published':
            return this.formatPublishedMessage(processedItem)
          default:
            return processedItem
        }
      })
    },

    /**
     * 格式化点赞消息
     */
    formatLikeMessage(item) {
      return {
        ...item,
        avatar: item.from_user_avatar || '/images/weixiao.png',
        username: item.from_username,
        actionText: this.getLikeActionText(item.target_type),
        time: this.formatTime(item.created_at),
        content: item.target_content,
        image: item.content_image
      }
    },

    /**
     * 格式化评论回复消息
     */
    formatReplyMessage(item) {
      return {
        ...item,
        avatar: item.from_user_avatar || '/images/weixiao.png',
        username: item.from_username,
        actionText: this.getReplyActionText(item.type),
        time: this.formatTime(item.send_timestamp),
        content: item.content,
        originalContent: this.getOriginalContent(item),
        images: item.images || []
      }
    },

    /**
     * 格式化系统通知消息
     */
    formatNotificationMessage(item) {
      return {
        ...item,
        avatar: item.icon || '/images/xiaoxi.png',
        username: '系统通知',
        actionText: item.title,
        time: this.formatTime(item.created_at),
        content: item.message
      }
    },

    /**
     * 格式化发布消息
     */
    formatPublishedMessage(item) {
      return {
        ...item,
        face_url: item.face_url,
        username: item.username,
        choose: item.choose,
        time: this.formatTime(item.created_at),
        content: item.content,
        images: item.images || [],
        like_count: item.like_count || 0,
        comment_count: item.comment_count || 0
      }
    },

    /**
     * 获取点赞动作文本
     */
    getLikeActionText(targetType) {
      const actionMap = {
        comment: '赞了你的评论',
        reply: '赞了你的回复',
        message: '赞了你的帖子',
        liaoran_comment: '赞了你的了然几分评论',
        liaoran_reply: '赞了你的了然几分回复',
        window_comment: '赞了你的食堂评论',
        window_reply: '赞了你的食堂回复',
        major_comment: '赞了你的专业评论',
        major_reply: '赞了你的专业回复'
      }
      return actionMap[targetType] || '赞了你的内容'
    },

    /**
     * 获取评论回复动作文本
     */
    getReplyActionText(type) {
      const actionMap = {
        comment: '评论了你的帖子',
        reply_to_comment: '回复了你的评论',
        reply_to_reply: '回复了你的回复',
        liaoran_comment: '评论了你的了然几分对象',
        liaoran_reply: '回复了你的了然几分评论',
        liaoran_reply_to_reply: '回复了你的了然几分回复',
        window_comment: '评论了你的食堂窗口',
        window_reply: '回复了你的食堂评论',
        window_reply_to_reply: '回复了你的食堂回复',
        major_comment: '回复了你的专业评论',
        major_reply: '回复了你的专业回复',
        major_reply_to_reply: '回复了你的专业回复'
      }
      return actionMap[type] || '回复了你'
    },

    /**
     * 获取发布动作文本
     */
    getPublishedActionText(choose) {
      const actionMap = {
        '99': '寻找搭子',
        '2': '发条说说',
        '3': '校园交易',
        '4': '告白倾诉',
        '5': '拼车交流',
        '6': '失物寻找',
        '71': '房屋出租',
        '72': '租房求助',
        '8': '赚点外快'
      }
      return actionMap[choose] || '发布内容'
    },

    /**
     * 获取原始内容
     */
    getOriginalContent(item) {
      switch (item.type) {
        case 'comment':
          return item.message_content
        case 'reply_to_comment':
          return item.original_content
        case 'reply_to_reply':
          return item.original_reply_content
        default:
          return item.original_content || item.message_content
      }
    },

    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time
      
      const minute = 60 * 1000
      const hour = 60 * minute
      const day = 24 * hour
      
      if (diff < minute) {
        return '刚刚'
      } else if (diff < hour) {
        return `${Math.floor(diff / minute)}分钟前`
      } else if (diff < day) {
        return `${Math.floor(diff / hour)}小时前`
      } else if (diff < 7 * day) {
        return `${Math.floor(diff / day)}天前`
      } else {
        return time.toLocaleDateString()
      }
    },

    /**
     * 标记消息为已读
     */
    async markAsRead() {
      try {
        await messageAPI.markAsRead(this.properties.type)
      } catch (error) {
        console.error('Mark as read error:', error)
      }
    },

    /**
     * 下拉刷新
     */
    onRefresh() {
      this.loadMessages(true)
    },

    /**
     * 点赞状态变化处理
     */
    onLikeChange(e) {
      const { type, targetId, isLiked, totalLikes } = e.detail

      // 更新本地数据
      const messages = this.data.messages
      const index = messages.findIndex(item => item.id === targetId)

      if (index !== -1) {
        messages[index].is_liked = isLiked
        messages[index].like_count = totalLikes

        this.setData({
          messages: messages
        })
      }

      // 触发父组件事件
      this.triggerEvent('likechange', e.detail)
    },

    /**
     * 上拉加载更多
     */
    onLoadMore() {
      if (this.data.hasMore && !this.data.loadingMore) {
        this.loadMessages(false)
      }
    },

    /**
     * 点击消息项
     */
    onMessageClick(e) {
      const { message } = e.currentTarget.dataset
      
      // 触发自定义事件
      this.triggerEvent('messageclick', {
        message,
        type: this.properties.type
      })
    },

    /**
     * 重试加载
     */
    onRetry() {
      this.setData({ error: null })
      this.loadMessages(true)
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.onLoad()
    }
  }
})
