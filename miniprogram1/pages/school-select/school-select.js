Page({
  data: {
    searchText: '',
    allSchools: [],
    districts: [],
    selectedDistrict: '海淀区',
    currentSchools: {
      enabled: [],
      disabled: []
    },
    selectedSchool: null,
    isLoading: true,
    showSchoolModal: false,
    selectedSchoolName: '',
    showNavbar: true // 控制导航栏显示
  },

  onLoad(options) {
    // 检查用户是否有学校信息，如果没有则隐藏整个导航栏
    const hasSchoolInfo = wx.getStorageSync('has_school_info');
    const selectedSchool = wx.getStorageSync('selected_school');

    // 如果用户明确没有学校信息，隐藏导航栏（强制选择模式）
    const showNavbar = !(hasSchoolInfo === false || (!hasSchoolInfo && !selectedSchool));

    this.setData({
      showNavbar: showNavbar
    });

    this.loadSchools();
  },

  // 加载学校数据
  loadSchools() {
    this.setData({ isLoading: true });

    wx.request({
      url: getApp().globalData.wangz + '/school/getSchools',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      timeout: 10000, // 10秒超时
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const schools = res.data.data || [];

          // 为每个学校生成首字母
          const processedSchools = schools.map(school => ({
            ...school,
            initial: this.getInitial(school.short_name)
          }));

          // 获取所有区域
          const districts = [...new Set(schools.map(school => school.district))];

          this.setData({
            allSchools: processedSchools,
            districts: districts
          });

          // 默认显示第一个区域的学校，如果默认区域没有学校，则选择第一个有学校的区域
          let targetDistrict = this.data.selectedDistrict;
          if (districts.length > 0) {
            if (!districts.includes(targetDistrict)) {
              targetDistrict = districts[0];
              this.setData({ selectedDistrict: targetDistrict });
            }
            this.filterSchoolsByDistrict(targetDistrict);
          }
        } else {
          this.showError('加载学校数据失败');
        }
      },
      fail: (error) => {
        console.error('加载学校失败:', error);
        this.showError('网络错误，请重试');
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 获取学校名称首字母
  getInitial(name) {
    if (!name) return '?';
    
    // 提取中文字符的首字母
    const firstChar = name.charAt(0);
    
    // 如果是中文，返回第一个字符
    if (/[\u4e00-\u9fa5]/.test(firstChar)) {
      return firstChar;
    }
    
    // 如果是英文，返回大写首字母
    return firstChar.toUpperCase();
  },

  // 选择行政区
  selectDistrict(e) {
    const district = e.currentTarget.dataset.district;
    this.setData({
      selectedDistrict: district,
      searchText: '' // 清除搜索
    });
    this.filterSchoolsByDistrict(district);
  },

  // 根据行政区筛选学校
  filterSchoolsByDistrict(district) {
    const districtSchools = this.data.allSchools.filter(school => school.district === district);

    const enabled = districtSchools.filter(school => school.is_active === 1);
    const disabled = districtSchools.filter(school => school.is_active === 0);

    this.setData({
      currentSchools: {
        enabled: enabled,
        disabled: disabled
      }
    });
  },

  // 搜索输入
  onSearchInput(e) {
    const searchText = e.detail.value.trim();
    this.setData({ searchText });

    if (searchText) {
      this.filterSchools(searchText);
    } else {
      // 恢复当前区域的学校
      this.filterSchoolsByDistrict(this.data.selectedDistrict);
    }
  },

  // 筛选学校
  filterSchools(searchText) {
    const filtered = this.data.allSchools.filter(school => {
      return school.short_name.includes(searchText) ||
             school.university_short_name.includes(searchText) ||
             school.full_name.includes(searchText) ||
             school.university_name.includes(searchText) ||
             school.district.includes(searchText);
    });

    const enabled = filtered.filter(school => school.is_active === 1);
    const disabled = filtered.filter(school => school.is_active === 0);

    this.setData({
      currentSchools: {
        enabled: enabled,
        disabled: disabled
      }
    });
  },

  // 清除搜索
  clearSearch() {
    this.setData({ searchText: '' });
    this.onSearchInput({ detail: { value: '' } });
  },

  // 选择学校
  selectSchool(e) {
    const school = e.currentTarget.dataset.school;

    // 保存选择的学校到本地存储
    wx.setStorageSync('selected_school', school);
    wx.setStorageSync('has_school_info', true);
    wx.setStorageSync('school_id', school.id); // 保存school_id到本地存储

    // 更新全局变量
    const app = getApp();
    app.globalData.school_id = school.id;

    // 检查登录状态
    this.checkLoginAndProceed(school);
  },

  // 检查登录状态并继续处理
  async checkLoginAndProceed(school) {
    const app = getApp();
    const userId = wx.getStorageSync('user_id');
    const accessToken = wx.getStorageSync('access_token');

    // 如果没有登录，调用登录组件
    if (!userId || !accessToken || !app.globalData.isLoggedIn) {
      console.log('[学校选择] 用户未登录，开始登录流程');

      try {
        // 导入并调用登录管理器
        const { loginManager } = require('../../utils/loginManager');
        const loginResult = await loginManager.login();

        if (loginResult) {
          console.log('[学校选择] 登录成功，继续处理');
          // 登录成功后更新用户学校信息
          this.updateUserSchool(school);
          this.proceedAfterLogin();
        } else {
          console.log('[学校选择] 登录失败');
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('[学校选择] 登录过程出错:', error);
        wx.showToast({
          title: '登录出错，请重试',
          icon: 'none'
        });
      }
    } else {
      console.log('[学校选择] 用户已登录，直接处理');
      // 已登录，直接更新用户学校信息
      this.updateUserSchool(school);
      this.proceedAfterLogin();
    }
  },

  // 登录后继续处理
  proceedAfterLogin() {
    // 判断跳转行为：如果显示导航栏说明用户之前有学校信息，否则是首次选择
    const showNavbar = this.data.showNavbar;

    if (showNavbar) {
      // 显示导航栏，说明用户之前有学校信息，直接返回上一页
      wx.navigateBack();
    } else {
      // 不显示导航栏，说明是首次选择学校，跳转到home页面
      wx.reLaunch({
        url: '/pages/fold1/home/<USER>'
      });
    }
  },

  // 更新用户学校信息到后端
  updateUserSchool(school) {
    const userId = wx.getStorageSync('user_id');
    const accessToken = wx.getStorageSync('access_token');

    if (!userId || !accessToken) {
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/user/updateSchool',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': accessToken
      },
      data: {
        user_id: userId,
        school_id: school.id
      },
      success: (res) => {
        // 静默更新，不显示结果
      },
      fail: (error) => {
        // 静默更新，不显示错误
      }
    });
  },

  // 显示未启用提示
  showDisabledTip(e) {
    const school = e.currentTarget.dataset.school;

    this.setData({
      showSchoolModal: true,
      selectedSchoolName: school.short_name
    });
  },

  // 关闭学校弹窗
  closeSchoolModal() {
    this.setData({
      showSchoolModal: false,
      selectedSchoolName: ''
    });
  },

  // 返回上一页
  goBack() {
    // 只有在显示导航栏时才允许返回
    if (this.data.showNavbar) {
      wx.navigateBack();
    }
  },

  // 显示错误信息
  showError(message) {
    wx.showModal({
      title: '提示',
      content: message,
      showCancel: false,
      confirmText: '确定',
      confirmColor: '#007AFF'
    });
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    // 阻止滑动事件冒泡
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '选择你的学校',
      path: '/pages/school-select/school-select'
    };
  }
});
