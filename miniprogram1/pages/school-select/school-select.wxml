<!-- 学校选择页面 -->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <navbar wx:if="{{showNavbar}}" title="选择学校" showBack="{{true}}" bind:back="goBack"></navbar>

  <!-- Loading组件 -->
  <loading show="{{isLoading}}" mask="true"></loading>

  <!-- 自定义学校弹窗 -->
  <school-modal
    show="{{showSchoolModal}}"
    school-name="{{selectedSchoolName}}"
    bind:close="closeSchoolModal">
  </school-modal>

  <!-- 品牌横幅 -->
  <view class="brand-banner">
    <image src="https://www.bjgaoxiaoshequ.store/uploads/宣传图.jpeg" class="brand-image" mode="aspectFill"></image>
  </view>

  <!-- 主体内容 - 两列布局 -->
  <view class="main-container">
    <!-- 左侧区域选择 -->
    <view class="district-sidebar">
      <view class="sidebar-header">
        <text class="sidebar-title">区域选择</text>
      </view>
      <scroll-view class="district-list" scroll-y enhanced show-scrollbar="{{false}}">
        <view
          wx:for="{{districts}}"
          wx:key="*this"
          class="district-item {{selectedDistrict === item ? 'district-active' : ''}}"
          bindtap="selectDistrict"
          data-district="{{item}}"
        >
          <text class="district-name">{{item}}</text>
          <view wx:if="{{selectedDistrict === item}}" class="district-indicator"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 右侧学校内容 -->
    <view class="school-content">
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-wrapper">
          <image src="/images/fangdajing.png" class="search-icon"></image>
          <input
            class="search-field"
            placeholder="搜索学校名称或校区"
            value="{{searchText}}"
            bindinput="onSearchInput"
            placeholder-class="search-placeholder"
            type="text"
            confirm-type="search"
            maxlength="50"
            cursor-spacing="10"
            adjust-position="{{false}}"
          />
          <view wx:if="{{searchText}}" class="search-clear" bindtap="clearSearch">
            <image src="/images/guanbi.png" class="clear-icon"></image>
          </view>
        </view>
      </view>

      <!-- 学校列表 -->
      <scroll-view class="content-scroll" scroll-y enhanced show-scrollbar="{{false}}" catchtouchmove="stopPropagation">
        <view class="school-list">
          <!-- 已开放学校 -->
          <view
            wx:for="{{currentSchools.enabled}}"
            wx:key="id"
            class="school-card available-card"
            bindtap="selectSchool"
            data-school="{{item}}"
          >
            <view class="card-avatar">
              <image src="{{item.logo_url}}" class="school-logo" mode="aspectFill"></image>
            </view>
            <view class="card-content">
              <view class="school-title">{{item.short_name}}</view>
              <view class="school-subtitle">{{item.university_name}}</view>
            </view>
            <view class="card-action">
              <view class="action-btn available-btn">
                <text class="btn-text">已开放</text>
                <view class="btn-number">OK</view>
              </view>
            </view>
          </view>

          <!-- 未开放学校 -->
          <view
            wx:for="{{currentSchools.disabled}}"
            wx:key="id"
            class="school-card unavailable-card"
            bindtap="showDisabledTip"
            data-school="{{item}}"
          >
            <view class="card-avatar">
              <image src="{{item.logo_url}}" class="school-logo" mode="aspectFill"></image>
            </view>
            <view class="card-content">
              <view class="school-title disabled-title">{{item.short_name}}</view>
              <view class="school-subtitle disabled-subtitle">{{item.university_name}}</view>
            </view>
            <view class="card-action">
              <view class="action-btn unavailable-btn">
                <text class="btn-text">未开启</text>
                <view class="btn-number">NO</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:if="{{!isLoading && currentSchools.enabled.length === 0 && currentSchools.disabled.length === 0}}" class="empty-container">
          <view class="empty-illustration">
            <text class="empty-emoji">🏫</text>
          </view>
          <view class="empty-title">{{searchText ? '未找到相关学校' : '该区域暂无学校'}}</view>
          <view class="empty-desc">{{searchText ? '试试其他关键词吧' : '请选择其他区域查看'}}</view>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-bottom"></view>
      </scroll-view>
    </view>
  </view>
</view>
