/* 学校选择页面 - 简洁灰白风格 */
.page-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 品牌横幅 */
.brand-banner {
  position: relative;
  overflow: hidden;
  border-bottom: 1rpx solid #e9ecef;
}

.brand-image {
  width: 100%;
  height: 300rpx;
  display: block;
}

/* 主体内容 - 两列布局 */
.main-container {
  flex: 1;
  display: flex;
  background: #fff;
  min-height: 0;
}

/* 左侧区域选择 */
.district-sidebar {
  width: 180rpx;
  background: #fafbfc;
  border-right: 1rpx solid #e9ecef;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 24rpx 16rpx 16rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.sidebar-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #666;
  text-align: center;
  width: 100%;
  display: block;
}

.district-list {
  flex: 1;
  padding: 16rpx 0;
}

.district-item {
  position: relative;
  padding: 20rpx 12rpx;
  margin: 6rpx 12rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.district-item:active {
  transform: scale(0.95);
}

.district-name {
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  display: block;
  line-height: 1.3;
  color: #999;
  transition: all 0.3s ease;
  width: 100%;
}

.district-active .district-name {
  color: #333;
  font-weight: 700;
}

/* 右侧学校内容 */
.school-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  min-width: 0;
  position: relative;
  z-index: 1;
}

/* 搜索区域 */
.search-section {
  padding: 16rpx 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 22rpx;
  padding: 12rpx 18rpx;
  transition: all 0.3s ease;
}

.search-wrapper:focus-within {
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 12rpx;
  opacity: 0.6;
  flex-shrink: 0;
}

.search-field {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  min-width: 0;
}

.search-placeholder {
  color: #999;
}

.search-clear {
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
  flex-shrink: 0;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  touch-action: pan-y;
}

/* 学校列表 */
.school-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 16rpx;
}

/* 学校卡片 */
.school-card {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 0;
}

.available-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.unavailable-card:active {
  transform: scale(0.98);
}

/* 卡片头像 */
.card-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
}

.avatar-text {
  font-size: 22rpx;
  font-weight: 600;
  color: white;
}

.school-logo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  min-width: 0;
}

.school-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.disabled-title {
  color: #333;
}

.school-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.disabled-subtitle {
  color: #666;
}

/* 卡片操作按钮 - 完全复制home页面cyber-badge样式 */
.card-action {
  flex-shrink: 0;
}

.action-btn {
  position: relative;
  height: 32rpx;
  width: 100rpx !important;
  display: inline-block;
}

.available-btn {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 32rpx;
  text-align: center;
}

.available-btn::after, .available-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
}

.available-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.available-btn::after {
  background: var(--primary);
}

.unavailable-btn {
  --primary: #999;
  --shadow-primary: #ccc;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 32rpx;
  text-align: center;
}

.unavailable-btn::after, .unavailable-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
}

.unavailable-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.unavailable-btn::after {
  background: var(--primary);
}

.btn-text {
  font-size: 18rpx;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.3rpx;
  position: relative;
  z-index: 1;
}

.btn-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 78%;
  line-height: 8rpx;
  text-align: center;
  z-index: 1;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-illustration {
  margin-bottom: 24rpx;
}

.empty-emoji {
  font-size: 100rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

/* 底部安全区域 */
.safe-bottom {
  height: 80rpx;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
