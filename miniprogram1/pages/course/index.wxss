/* pages/course/index.wxss */
.container {
  padding: 40rpx 30rpx;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 统一所有卡片的宽度和边距 */
.notice-section,
.developer-section,
.url-section,
.tip-section {
  width: 100%;
  max-width: 600rpx;
}

/* 顶部说明区域 */
.notice-section {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
  color: white;
  box-shadow: 0 20rpx 40rpx rgba(79, 172, 254, 0.3);
}

.notice-icon {
  margin-bottom: 30rpx;
}

.course-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.notice-title {
  font-size: 42rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}

.notice-desc {
  font-size: 28rpx;
  opacity: 0.95;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 网址显示区域 */
.url-section {
  background-color: white;
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.url-label {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 20rpx;
  font-weight: 600;
  text-align: center;
}

.url-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 2rpx solid #dee2e6;
}

.url-text {
  font-size: 28rpx;
  color: #0066cc;
  word-break: break-all;
  line-height: 1.5;
  margin-bottom: 25rpx;
  text-align: center;
  font-weight: 500;
}

.copy-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  font-weight: 600;
  min-width: 120rpx;
  box-shadow: 0 6rpx 15rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.copy-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(255, 107, 107, 0.3);
}

/* 简洁提示区域 */
.tip-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 25rpx 40rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.tip-text {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 500;
  letter-spacing: 1rpx;
}

/* 开发者信息区域 */
.developer-section {
  margin-bottom: 30rpx;
}

.developer-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 25rpx;
  padding: 25rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.developer-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.developer-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 20rpx;
}

.developer-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  margin-right: 15rpx;
  letter-spacing: 1rpx;
}

.developer-name {
  font-size: 28rpx;
  color: white;
  font-weight: 600;
  letter-spacing: 1rpx;
}
