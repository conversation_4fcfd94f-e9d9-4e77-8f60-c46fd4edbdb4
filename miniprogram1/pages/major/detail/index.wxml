<wxs module="timeUtils">
  function formatTime(timeStr) {
    if (!timeStr) return ''

    // 处理iOS日期格式兼容性问题
    // 将 "2025-05-26 12:25:43" 格式转换为 "2025/05/26 12:25:43"
    // WXS不支持正则表达式，使用split和join方法替换
    var parts = timeStr.split('-')
    var formattedTimeStr = parts.join('/')

    var date = getDate(formattedTimeStr)
    var now = getDate()

    // 计算时间差（毫秒）
    var diffMs = now.getTime() - date.getTime()
    var diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    var diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    // 一天内显示x小时前
    if (diffHours < 24 && diffDays === 0) {
      if (diffHours <= 0) {
        return '刚刚'
      }
      return diffHours + '小时前'
    }
    // 3天内显示x天前
    else if (diffDays <= 3) {
      return diffDays + '天前'
    }
    // 超过3天显示具体日期 YY-MM-DD 格式（如：25-05-26）
    else {
      var year = (date.getFullYear() + '').slice(-2) // 取年份后两位
      var month = (date.getMonth() + 1 + '').length === 1 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1 + '')
      var day = (date.getDate() + '').length === 1 ? '0' + date.getDate() : (date.getDate() + '')

      return year + '-' + month + '-' + day
    }
  }

  function getTotalCommentCount(comments) {
    if (!comments || comments.length === 0) return 0

    var total = comments.length // 主评论数

    // 加上所有回复数
    for (var i = 0; i < comments.length; i++) {
      if (comments[i].replies && comments[i].replies.length > 0) {
        total += comments[i].replies.length
      }
    }

    return total
  }

  module.exports = {
    formatTime: formatTime,
    getTotalCommentCount: getTotalCommentCount
  }
</wxs>

<view class="canteen-bg">
  <custom-nav-bar title="{{majorName}}" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>

  <view wx:if="{{!isLoading && majorInfo}}">
    <!-- 专业信息头部 -->
    <view class="major-header">
      <view class="canteen-avatar large-avatar">
        <view class="text-avatar" style="background-color: {{majorInfo.avatar_color}}">
          <view wx:if="{{majorInfo.avatarTextArray.length === 2}}" class="avatar-text-two">
            <view wx:for="{{majorInfo.avatarTextArray}}" wx:key="index" class="text-char-two">{{item}}</view>
          </view>
          <view wx:elif="{{majorInfo.avatarTextArray.length === 3}}" class="avatar-text-three">
            <view wx:for="{{majorInfo.avatarTextArray}}" wx:key="index" class="text-char-three">{{item}}</view>
          </view>
          <view wx:else class="avatar-text-grid">
            <view wx:for="{{majorInfo.avatarTextArray}}" wx:key="index" class="text-char">{{item}}</view>
          </view>
        </view>
      </view>
      <view class="major-info">
        <view class="major-name">{{majorInfo.major_name}}</view>
        <view class="major-meta">
          <text class="meta-item">{{majorInfo.department_id}}系</text>
          <text class="meta-item">{{majorInfo.college_name}}</text>
        </view>
      </view>
    </view>

    <!-- 全部评论 -->
    <view class="comments-section">
      <view class="comments-header">
        <text class="comments-title">全部评论</text>
        <view class="sort-tabs">
          <text class="sort-tab {{sortType === 'hot' ? 'active' : ''}}"
                bindtap="switchSort" data-type="hot">热门</text>
          <text class="sort-tab {{sortType === 'latest' ? 'active' : ''}}"
                bindtap="switchSort" data-type="latest">最新</text>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-list" wx:if="{{comments && comments.length > 0}}">
        <view class="comment-item" wx:for="{{comments}}" wx:key="id" id="comment-{{index}}">
          <image class="comment-avatar" src="{{item.user.avatar}}" mode="aspectFill" bindtap="clickCommentToReply" data-comment="{{item}}"></image>
          <view class="comment-content" bindtap="clickCommentToReply" data-comment="{{item}}">
            <view class="comment-header" bindtap="clickCommentToReply" data-comment="{{item}}">
              <text class="comment-username">{{item.user.name}}</text>
              <!-- 删除图标，只对自己的评论显示 -->
              <image wx:if="{{item.user.id == currentUserId}}"
                     src="/images/gengduo.png"
                     class="comment-more-icon"
                     catchtap="showCommentMenu"
                     data-comment="{{item}}"
                     data-type="comment"></image>
            </view>
            <view class="comment-text" bindtap="clickCommentToReply" data-comment="{{item}}">{{item.content}}</view>
            <!-- 评论图片显示 -->
            <view wx:if="{{item.images && item.images.length > 0}}" class="comment-images">
              <image wx:for="{{item.images}}" wx:key="index" wx:for-item="img"
                     src="{{img}}" mode="aspectFill" class="comment-image"
                     catchtap="previewCommentImage"
                     data-url="{{img}}"
                     data-images="{{item.images}}">
              </image>
            </view>
            <view class="comment-footer">
              <text class="comment-time" bindtap="clickCommentToReply" data-comment="{{item}}">{{timeUtils.formatTime(item.time)}}</text>
              <text class="comment-reply" bindtap="replyComment" data-comment="{{item}}">回复</text>
              <view class="comment-actions" catchtap="likeComment" data-id="{{item.id}}" data-index="{{index}}">
                <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" class="action-icon"></image>
                <text class="action-count">{{item.likes}}</text>
              </view>
            </view>
            <!-- 回复列表 -->
            <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
              <view class="reply-item" wx:for="{{item.replies}}" wx:key="id" wx:for-item="reply" wx:for-index="replyIndex" id="reply-{{index}}-{{replyIndex}}">
                <image class="reply-avatar" src="{{reply.user.avatar}}" mode="aspectFill" catchtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}"></image>
                <view class="reply-content" catchtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">
                  <view class="reply-header">
                    <text class="reply-username">{{reply.user.name}}</text>
                    <!-- 删除图标，只对自己的回复显示 -->
                    <image wx:if="{{reply.user.id == currentUserId}}"
                           src="/images/gengduo.png"
                           class="reply-more-icon"
                           catchtap="showCommentMenu"
                           data-comment="{{reply}}"
                           data-type="reply"></image>
                  </view>
                  <view class="reply-text">
                    <text wx:if="{{reply.reply_to_username && reply.reply_to_username !== ''}}" class="reply-prefix">回复 {{reply.reply_to_username}}: </text>
                    <text class="reply-main-content">{{reply.content}}</text>
                  </view>
                  <!-- 回复图片显示 -->
                  <view wx:if="{{reply.images && reply.images.length > 0}}" class="reply-images">
                    <image wx:for="{{reply.images}}" wx:key="index" wx:for-item="img"
                           src="{{img}}" mode="aspectFill" class="reply-image"
                           catchtap="previewCommentImage"
                           data-url="{{img}}"
                           data-images="{{reply.images}}">
                    </image>
                  </view>
                  <view class="reply-footer">
                    <text class="reply-time">{{timeUtils.formatTime(reply.time)}}</text>
                    <text class="reply-reply" catchtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">回复</text>
                    <view class="reply-actions" catchtap="likeReply" data-reply-id="{{reply.id}}" data-comment-index="{{index}}" data-reply-index="{{replyIndex}}">
                      <image src="{{reply.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" class="reply-action-icon"></image>
                      <text class="reply-action-count">{{reply.likes}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无评论时显示 -->
      <view wx:if="{{!isLoading && (!comments || comments.length === 0)}}" class="no-comment">
        <image src="https://www.bjgaoxiaoshequ.store/images/content.png" mode="aspectFit" class="no-comment-image"></image>
        <text class="no-comment-text">一片荒原，来说点什么吧～</text>
      </view>
    </view>

    <!-- 底部遮罩 -->
    <view class="bottom-mask"></view>

    <!-- 底部输入框 -->
    <view class="bottom-fixed-bar">
      <view class="input-section" bindtap="handleCommentClick">
        <image src="/images/dangshidati-01.png" class="comment-icon" />
        <text class="input-placeholder">说点什么...</text>
      </view>
      <view class="action-section">
        <view class="action-item" bindtap="handleCommentClick">
          <image src="/images/pinglun2.png" class="action-icon" />
          <text class="action-text">{{timeUtils.getTotalCommentCount(comments)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!isLoading && !majorInfo}}" class="empty-state">
    <view class="empty-text">专业信息不存在</view>
    <view class="empty-btn" bindtap="goBack">返回</view>
  </view>

  <!-- 评论遮罩层 -->
  <view wx:if="{{commentSectionVisible}}" class="comment-mask" bindtap="hideCommentSection"></view>

  <!-- 评论输入框 -->
  <view wx:if="{{commentSectionVisible}}"
        class="fixed-comment-section {{showEmoji ? 'comment-section-with-emoji' : ''}} {{showEmojiList ? 'comment-section-with-emoji-list' : ''}} {{keyboardHeight > 0 ? 'comment-section-with-keyboard' : ''}}"
        style="{{keyboardHeight > 0 ? '--keyboard-height:' + keyboardHeight + 'px' : ''}}">

    <!-- 图片预览区域 - 最上面 -->
    <view class="image-preview-area" wx:if="{{selectedImages.length > 0}}">
      <view class="image-preview-item" wx:for="{{selectedImages}}" wx:key="index">
        <image src="{{item}}" mode="aspectFill" class="preview-image" bindtap="previewImage" data-index="{{index}}"></image>
        <view class="remove-image-btn" bindtap="removeImage" data-index="{{index}}">×</view>
      </view>
    </view>

    <!-- 常用表情栏 -->
    <view class="quick-emoji-bar">
      <view class="quick-emoji-item" wx:for="{{quickEmojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
        <text class="emoji-text">{{item}}</text>
      </view>
    </view>

    <!-- 评论输入框和工具栏 -->
    <view class="input-tools-container">
      <!-- 评论输入框 -->
      <textarea class="comment-textarea"
                placeholder="{{commentPlaceholder}}"
                value="{{commentContent}}"
                bindinput="onCommentInput"
                bindblur="handleInputBlur"
                bindfocus="handleInputFocus"
                bindkeyboardheightchange="handleKeyboardHeightChange"
                focus="{{commentInputFocused}}"
                fixed="true"
                show-confirm-bar="{{false}}"
                adjust-position="{{false}}"
                cursor-spacing="20"
                maxlength="100"
                auto-height="{{true}}"
                style="min-height: 80rpx; max-height: 200rpx;"
                hold-keyboard="{{true}}" />

      <!-- 工具栏 -->
      <view class="tools-row">
        <view class="left-tools">
          <view class="tool-item" bindtap="chooseImage">
            <image src="/images/tupian.png" class="tool-icon" />
            <view class="image-count" wx:if="{{selectedImages.length > 0}}">{{selectedImages.length}}</view>
          </view>
          <view class="tool-item" bindtap="openCamera">
            <image src="/images/xiangji.png" class="tool-icon" />
          </view>
          <view class="tool-item" bindtap="toggleEmojiList">
            <text class="emoji-icon">😊</text>
          </view>
        </view>
        <button class="submit-button {{isSubmittingComment ? 'disabled' : ''}}"
                bindtap="submitComment"
                disabled="{{isSubmittingComment}}">
          {{isSubmittingComment ? '发送中' : '发送'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 表情面板 -->
  <view class="emoji-panel" wx:if="{{showEmoji}}">
    <scroll-view scroll-y class="emoji-scroll">
      <view class="emoji-grid">
        <view class="emoji-item" wx:for="{{emojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
          <text class="emoji-text">{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- emoji表情面板 -->
  <view class="emoji-list-panel" wx:if="{{showEmojiList}}">
    <scroll-view scroll-y class="emoji-list-scroll">
      <view class="emoji-list-grid">
        <view class="emoji-list-item" wx:for="{{emojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
          <text>{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 删除菜单弹出层 -->
  <view wx:if="{{showDeleteMenu}}" class="delete-menu-mask" bindtap="hideDeleteMenu">
    <view class="delete-menu" catchtap="">
      <view class="delete-menu-item" bindtap="deleteComment">
        <text>删除</text>
      </view>
      <view class="delete-menu-cancel" bindtap="hideDeleteMenu">
        <text>取消</text>
      </view>
    </view>
  </view>
</view>
