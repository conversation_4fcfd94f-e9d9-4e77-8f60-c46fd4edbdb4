// pages/major/detail/index.js
const app = getApp();

// 兼容 iOS 的日期解析函数
function parseDate(dateStr) {
  // 将 "2024-12-16 11:32:14" 格式转换为 "2024/12/16 11:32:14"
  return new Date(dateStr.replace(/-/g, '/'))
}

Page({
  data: {
    isLoading: true,
    majorId: null,
    majorName: '',
    majorInfo: null,
    comments: [],
    sortType: 'hot', // hot: 热门, latest: 最新
    currentUserId: null,

    // 评论输入相关
    commentSectionVisible: false,
    commentContent: '',
    commentPlaceholder: '说点什么...',
    commentInputFocused: false,
    isSubmittingComment: false,
    selectedImages: [],
    keyboardHeight: 0,

    // 回复相关
    replyToComment: null,

    // 表情相关
    showEmoji: false,
    showEmojiList: false,
    quickEmojiList: ['😊', '😂', '😘', '😍', '😭', '🥰', '😎', '😋'],
    emojiList: [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
      '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
      '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
      '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
      '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐'
    ],

    // 删除菜单
    showDeleteMenu: false,
    currentDeleteComment: null,
    currentDeleteType: '', // 'comment' 或 'reply'
  },

  onLoad(options) {
    const majorId = options.id
    const commentId = options.comment_id
    const replyId = options.reply_id

    // 设置当前用户ID
    const currentUserId = wx.getStorageSync('user_id')
    this.setData({
      currentUserId: currentUserId
    })

    if (majorId) {
      this.setData({
        majorId: majorId,
        majorName: options.name || '专业详情'
      });
      this.loadMajorDetail(majorId, commentId, replyId)
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 生成随机浅彩色
  generateRandomColor() {
    const colors = [
      '#A8E6CF', '#FFD3A5', '#FD9696', '#C7CEEA', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#F7DC6F',
      '#D7BDE2', '#A9DFBF', '#F9E79F', '#AED6F1', '#F5B7B1'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // 检查是否为汉字
  isChineseChar(char) {
    return /[\u4e00-\u9fa5]/.test(char);
  },

  // 处理专业头像
  processMajorAvatar(major) {
    const majorName = major.major_name || '';
    // 提取汉字
    const chineseChars = majorName.split('').filter(char => this.isChineseChar(char));

    let avatarTextArray = [];
    if (chineseChars.length >= 4) {
      // 四个字以上取前四个字
      avatarTextArray = chineseChars.slice(0, 4);
    } else if (chineseChars.length === 3) {
      // 三个字
      avatarTextArray = chineseChars;
    } else if (chineseChars.length === 2) {
      // 两个字
      avatarTextArray = chineseChars;
    } else if (chineseChars.length === 1) {
      // 一个字，重复四次
      avatarTextArray = [chineseChars[0], chineseChars[0], chineseChars[0], chineseChars[0]];
    } else {
      // 没有汉字，使用默认
      avatarTextArray = ['专', '业', '推', '荐'];
    }

    return {
      ...major,
      avatarTextArray: avatarTextArray,
      avatar_color: this.generateRandomColor()
    };
  },

  // 加载专业详情
  loadMajorDetail(majorId, commentId, replyId) {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
      return;
    }

    wx.request({
      url: `${app.globalData.wangz}/major/detail`,
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        id: majorId
      },
      success: (res) => {
        if (res.data && res.data.error_code === 0) {
          const majorData = res.data.data;
          const majorInfo = this.processMajorAvatar(majorData);

          // 处理评论用户头像URL和时间格式化
          const comments = (majorData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像和时间
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          this.setData({
            majorInfo: majorInfo,
            majorName: majorInfo.major_name || '专业详情',
            comments: comments, // 将评论数据设置到comments中
            isLoading: false
          });

          // 如果有commentId或replyId参数，定位到指定评论
          if (commentId || replyId) {
            setTimeout(() => {
              this.scrollToTargetComment(commentId, replyId, comments)
            }, 500) // 等待页面渲染完成
          }
        } else {
          wx.showToast({
            title: res.data?.msg || '获取专业详情失败',
            icon: 'none'
          });
          this.setData({
            isLoading: false
          });
        }
      },
      fail: (error) => {
        console.error('获取专业详情异常:', error);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      }
    });
  },

  // 格式化时间显示（兼容iOS）
  formatTime(timeStr) {
    if (!timeStr) return ''

    // 处理iOS日期格式兼容性问题
    // 将 "2025-06-20 01:56:28" 格式转换为 "2025/06/20 01:56:28"
    let formattedTimeStr = timeStr.replace(/-/g, '/')

    const now = new Date()
    const time = parseDate(timeStr)

    // 检查日期是否有效
    if (isNaN(time.getTime())) {
      return '时间格式错误'
    }

    const diff = now.getTime() - time.getTime()

    // 计算时间差（毫秒）
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour

    if (diff < hour) {
      // 1小时内显示"x分钟前"
      const minutes = Math.floor(diff / minute)
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
    } else if (diff < day) {
      // 1天内显示"x小时前"
      const hours = Math.floor(diff / hour)
      return `${hours}小时前`
    } else if (diff < 3 * day) {
      // 3天内显示"x天前"
      const days = Math.floor(diff / day)
      return `${days}天前`
    } else {
      // 超过3天显示具体日期 YY-MM-DD
      const year = time.getFullYear().toString().slice(-2)
      const month = (time.getMonth() + 1).toString().padStart(2, '0')
      const date = time.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${date}`
    }
  },

  // 局部更新评论数据
  updateCommentsData(scrollToNewComment = false) {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    // 获取当前用户ID，用于定位新评论
    const currentUserId = wx.getStorageSync('user_id')

    wx.request({
      url: `${app.globalData.wangz}/major/detail`,
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        id: this.data.majorId
      },
      success: (res) => {
        if (res.data.error_code === 0) {
          const newData = res.data.data

          // 处理评论用户头像URL
          const comments = (newData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          // 只更新评论相关数据，保持其他数据不变
          this.setData({
            comments: comments
          })

          // 如果需要滚动到新评论
          if (scrollToNewComment && currentUserId) {
            setTimeout(() => {
              this.scrollToUserLatestComment(currentUserId, comments)
            }, 100) // 等待DOM更新
          }
        }
      },
      fail: (err) => {
        console.error('更新评论数据失败:', err)
      }
    })
  },

  // 切换排序方式
  switchSort(e) {
    const sortType = e.currentTarget.dataset.type
    this.setData({ sortType })

    let sortedComments = [...(this.data.comments || [])]
    if (sortType === 'hot') {
      // 按点赞数排序（降序）
      sortedComments.sort((a, b) => (b.likes || 0) - (a.likes || 0))
    } else {
      // 按时间排序（最新在前）
      sortedComments.sort((a, b) => parseDate(b.time) - parseDate(a.time))
    }

    this.setData({
      comments: sortedComments
    })
  },

  // 滚动到用户最新评论
  scrollToUserLatestComment(userId, comments) {
    try {
      // 查找用户的最新评论（包括回复）
      let targetCommentIndex = -1
      let targetReplyIndex = -1
      let latestTime = 0

      comments.forEach((comment, commentIndex) => {
        // 检查主评论
        if (comment.user.id == userId) {
          const commentTime = parseDate(comment.time).getTime()
          if (commentTime > latestTime) {
            latestTime = commentTime
            targetCommentIndex = commentIndex
            targetReplyIndex = -1
          }
        }

        // 检查回复
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.forEach((reply, replyIndex) => {
            if (reply.user.id == userId) {
              const replyTime = parseDate(reply.time).getTime()
              if (replyTime > latestTime) {
                latestTime = replyTime
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            }
          })
        }
      })

      // 如果找到了用户的评论，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })
      }
    } catch (error) {
      console.error('滚动到评论失败:', error)
    }
  },

  // 显示评论输入框
  handleCommentClick() {
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: '说点什么...',
      commentContent: '',
      replyToComment: null,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    });
  },

  // 隐藏评论输入框
  hideCommentSection() {
    this.setData({
      commentSectionVisible: false,
      commentInputFocused: false,
      commentContent: '',
      replyToComment: null,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [], // 重置图片
      isSubmittingComment: false // 重置发送状态
    });
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentContent: e.detail.value
    });
  },

  // 键盘高度变化
  handleKeyboardHeightChange(e) {
    this.setData({
      keyboardHeight: e.detail.height
    });
  },

  // 输入框获得焦点
  handleInputFocus() {
    this.setData({
      showEmoji: false,
      showEmojiList: false,
      commentInputFocused: true,
      commentSectionVisible: true
    });
  },

  // 输入框失去焦点
  handleInputBlur() {
    setTimeout(() => {
      // 只有当表情面板都没显示时，才重置状态
      if (!this.data.showEmoji && !this.data.showEmojiList) {
        this.setData({
          commentInputFocused: false,
          keyboardHeight: 0
        })
      }
    }, 100)
  },

  // 提交评论
  submitComment() {
    if (!this.data.commentContent.trim() && this.data.selectedImages.length === 0) {
      wx.showToast({
        title: '请输入评论内容或选择图片',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmittingComment) {
      return
    }

    this.setData({ isSubmittingComment: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isSubmittingComment: false })
      return
    }

    // 如果有图片，先上传图片
    if (this.data.selectedImages.length > 0) {
      this.uploadImagesAndSubmit()
    } else {
      this.submitCommentWithData({})
    }
  },

  // 上传图片并提交评论
  uploadImagesAndSubmit() {
    const token = wx.getStorageSync('access_token')
    const uploadPromises = this.data.selectedImages.map((imagePath, index) => {
      return new Promise((resolve) => {  // 改为总是resolve，不reject
        wx.uploadFile({
          url: getApp().globalData.wangz + '/upload/uploadImage',  // 使用正确的接口路径
          filePath: imagePath,
          name: 'file',
          formData: {
            user_id: getApp().globalData.user_id  // 添加用户ID
          },
          header: {
            'token': token
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              // 兼容多种返回格式
              if (data.error_code === 0) {
                // 格式1: {error_code: 0, data: {urls: [...]}}
                if (data.data && data.data.urls && data.data.urls.length > 0) {
                  resolve({ success: true, url: data.data.urls[0] })
                }
                // 格式2: {error_code: 0, image_url: "..."}
                else if (data.image_url) {
                  resolve({ success: true, url: data.image_url })
                }
                // 格式3: {error_code: 0, data: {image_url: "..."}}
                else if (data.data && data.data.image_url) {
                  resolve({ success: true, url: data.data.image_url })
                }
                else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              // 格式4: {code: 200, data: {url: "..."}}
              else if (data.code === 200) {
                if (data.data && data.data.url) {
                  resolve({ success: true, url: data.data.url })
                } else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              else {
                resolve({ success: false, error: data.msg || data.message || '上传失败' })
              }
            } catch (e) {
              console.error('JSON解析失败:', e, '原始数据:', res.data)
              resolve({ success: false, error: '解析响应失败: ' + e.message })
            }
          },
          fail: (err) => {
            console.error(`图片${index + 1}上传请求失败:`, err)
            resolve({ success: false, error: err.errMsg || '上传失败' })
          }
        })
      })
    })

    Promise.all(uploadPromises)
      .then(results => {
        // 筛选出成功上传的图片
        const successfulUploads = results.filter(result => result.success)
        const failedUploads = results.filter(result => !result.success)

        if (failedUploads.length > 0) {
          console.warn('部分图片上传失败:', failedUploads)
          wx.showToast({
            title: `${failedUploads.length}张图片上传失败`,
            icon: 'none',
            duration: 2000
          })
        }

        if (successfulUploads.length > 0) {
          // 有成功上传的图片，继续提交评论
          const imageUrls = successfulUploads.map(result => result.url)
          this.submitCommentWithData({ images: JSON.stringify(imageUrls) })
        } else {
          // 所有图片都上传失败
          wx.showToast({
            title: '所有图片上传失败',
            icon: 'none'
          })
          this.setData({ isSubmittingComment: false })
        }
      })
  },

  // 提交评论数据
  submitCommentWithData(extraData = {}) {
    const token = wx.getStorageSync('access_token')
    const data = {
      major_id: this.data.majorId,
      content: this.data.commentContent.trim(),
      ...extraData
    }

    // 如果是回复评论
    if (this.data.replyToComment) {
      if (this.data.replyToComment.parent_id) {
        // 回复的回复，使用父评论ID，并设置reply_to_user_id
        data.parent_id = this.data.replyToComment.parent_id
        // 确保user_id存在且为有效数字
        const replyUserId = this.data.replyToComment.user && this.data.replyToComment.user.id
        if (replyUserId && !isNaN(replyUserId)) {
          data.reply_to_user_id = replyUserId
        }
      } else {
        // 直接回复评论
        data.parent_id = this.data.replyToComment.id
      }
    }

    wx.request({
      url: getApp().globalData.wangz + '/majorcomment/addComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: data,
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          })

          // 立即添加新评论到本地数据，避免等待服务器响应
          this.addNewCommentToLocal(res.data.data, data)

          this.hideCommentSection()

          // 延迟更新完整数据，确保服务器数据同步
          setTimeout(() => {
            this.updateCommentsData(true)
          }, 1000)
        } else {
          wx.showToast({
            title: res.data.msg || '评论失败',
            icon: 'none'
          })
          // 发送失败时重置按钮状态
          this.setData({ isSubmittingComment: false })
        }
      },
      fail: (err) => {
        console.error('提交评论失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        // 发送失败时重置按钮状态
        this.setData({ isSubmittingComment: false })
      },
      complete: () => {
        // 成功时在hideCommentSection中重置，失败时在上面重置
      }
    })
  },

  // 立即添加新评论到本地数据
  addNewCommentToLocal(commentData, requestData) {
    try {
      const currentUserId = wx.getStorageSync('user_id')
      const currentUsername = wx.getStorageSync('username')
      const currentUserAvatar = wx.getStorageSync('face_url')

      // 构造新评论对象
      const newComment = {
        id: commentData.id || Date.now(), // 使用服务器返回的ID或临时ID
        content: requestData.content,
        time: new Date().toISOString().replace(/T/, ' ').replace(/\..+/, ''), // 格式化为 "YYYY-MM-DD HH:mm:ss"
        likes: 0,
        is_liked: false,
        user: {
          id: currentUserId,
          name: currentUsername || '匿名用户',
          avatar: currentUserAvatar ? (currentUserAvatar.startsWith('/uploads/') ?
            getApp().globalData.wangz + currentUserAvatar : currentUserAvatar) :
            '/images/default_avatar.png'
        },
        replies: []
      }

      // 如果有图片，添加图片
      if (requestData.images) {
        try {
          newComment.images = JSON.parse(requestData.images)
        } catch (e) {
          console.error('解析图片数据失败:', e)
          newComment.images = []
        }
      }

      const comments = [...(this.data.comments || [])]

      // 如果是回复评论
      if (requestData.parent_id) {
        // 找到父评论
        const parentIndex = comments.findIndex(comment => comment.id == requestData.parent_id)
        if (parentIndex >= 0) {
          // 构造回复对象
          const newReply = {
            id: commentData.id || Date.now(),
            content: requestData.content,
            time: new Date().toISOString().replace(/T/, ' ').replace(/\..+/, ''), // 格式化为 "YYYY-MM-DD HH:mm:ss"
            likes: 0,
            is_liked: false,
            user: {
              id: currentUserId,
              name: currentUsername || '匿名用户',
              avatar: currentUserAvatar ? (currentUserAvatar.startsWith('/uploads/') ?
                getApp().globalData.wangz + currentUserAvatar : currentUserAvatar) :
                '/images/default_avatar.png'
            }
          }

          // 如果有图片，添加图片
          if (requestData.images) {
            try {
              newReply.images = JSON.parse(requestData.images)
            } catch (e) {
              console.error('解析回复图片数据失败:', e)
              newReply.images = []
            }
          }

          // 如果是回复特定用户，添加回复目标信息
          if (requestData.reply_to_user_id) {
            // 找到被回复的用户名
            const replyToUser = this.findUserInComments(comments[parentIndex], requestData.reply_to_user_id)
            if (replyToUser) {
              newReply.reply_to_username = replyToUser.name
            }
          }

          // 添加回复到父评论
          if (!comments[parentIndex].replies) {
            comments[parentIndex].replies = []
          }
          comments[parentIndex].replies.push(newReply)
        }
      } else {
        // 添加新的主评论到列表开头
        comments.unshift(newComment)
      }

      // 更新本地数据
      this.setData({
        comments: comments
      })

      // 立即滚动到新评论
      setTimeout(() => {
        if (requestData.parent_id) {
          // 回复评论，滚动到回复
          const parentIndex = comments.findIndex(comment => comment.id == requestData.parent_id)
          if (parentIndex >= 0 && comments[parentIndex].replies) {
            const replyIndex = comments[parentIndex].replies.length - 1
            const scrollId = `reply-${parentIndex}-${replyIndex}`
            wx.pageScrollTo({
              selector: `#${scrollId}`,
              duration: 300
            })
          }
        } else {
          // 主评论，滚动到第一个评论
          wx.pageScrollTo({
            selector: '#comment-0',
            duration: 300
          })
        }
      }, 100)

    } catch (error) {
      console.error('添加新评论到本地失败:', error)
    }
  },

  // 在评论中查找用户
  findUserInComments(comment, userId) {
    // 检查主评论用户
    if (comment.user.id == userId) {
      return comment.user
    }

    // 检查回复中的用户
    if (comment.replies && comment.replies.length > 0) {
      for (const reply of comment.replies) {
        if (reply.user.id == userId) {
          return reply.user
        }
      }
    }

    return null
  },

  // 点赞评论
  likeComment(e) {
    const commentId = e.currentTarget.dataset.id
    const commentIndex = e.currentTarget.dataset.index

    if (!commentId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/majorcomment/likeComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: commentId,
        major_id: this.data.majorId
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地评论的点赞状态
          const comments = [...this.data.comments]
          if (comments[commentIndex]) {
            comments[commentIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].likes = res.data.data.total_likes
          }

          this.setData({
            comments: comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('点赞失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 点赞回复
  likeReply(e) {
    const replyId = e.currentTarget.dataset.replyId
    const commentIndex = e.currentTarget.dataset.commentIndex
    const replyIndex = e.currentTarget.dataset.replyIndex

    if (!replyId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/majorcomment/likeComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: replyId,
        major_id: this.data.majorId
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地回复的点赞状态
          const comments = [...this.data.comments]
          if (comments[commentIndex] && comments[commentIndex].replies && comments[commentIndex].replies[replyIndex]) {
            comments[commentIndex].replies[replyIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].replies[replyIndex].likes = res.data.data.total_likes
          }

          this.setData({
            comments: comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('点赞失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 回复评论
  replyComment(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      commentContent: '',
      replyToComment: comment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 点击评论内容区域回复
  clickCommentToReply(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      commentContent: '',
      replyToComment: comment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 点击回复内容区域回复
  clickReplyToReply(e) {
    const reply = e.currentTarget.dataset.reply
    const commentIndex = e.currentTarget.dataset.commentIndex

    // 构造回复对象，包含父评论信息
    const replyToComment = {
      id: reply.id,
      user: reply.user,
      parent_id: reply.parent_id || this.data.comments[commentIndex].id // 使用回复自带的parent_id，如果没有则使用父评论ID
    }

    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${reply.user.name}`,
      commentContent: '',
      replyToComment: replyToComment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 选择图片
  chooseImage() {
    const remainingCount = 9 - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能选择9张图片',
        icon: 'none'
      })
      return
    }

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        const newImages = [...this.data.selectedImages, ...res.tempFilePaths]
        this.setData({
          selectedImages: newImages
        })
      }
    })
  },

  // 打开相机
  openCamera() {
    const remainingCount = 9 - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能选择9张图片',
        icon: 'none'
      })
      return
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        const newImages = [...this.data.selectedImages, ...res.tempFilePaths]
        this.setData({
          selectedImages: newImages
        })
      }
    })
  },



  // 移除图片
  removeImage(e) {
    const index = e.currentTarget.dataset.index;
    const selectedImages = this.data.selectedImages;
    selectedImages.splice(index, 1);
    this.setData({ selectedImages });
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.selectedImages[index],
      urls: this.data.selectedImages
    });
  },

  // 预览评论图片
  previewCommentImage(e) {
    const { url, images } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: images
    });
  },

  // 选择表情
  selectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji
    const content = this.data.commentContent + emoji
    this.setData({
      commentContent: content
    })
  },

  // 切换表情面板
  toggleEmoji() {
    if (this.data.showEmoji) {
      this.setData({
        showEmoji: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmoji: true,
        showEmojiList: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 切换emoji表情列表
  toggleEmojiList() {
    if (this.data.showEmojiList) {
      this.setData({
        showEmojiList: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmojiList: true,
        showEmoji: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 显示评论菜单
  showCommentMenu(e) {
    const comment = e.currentTarget.dataset.comment
    const type = e.currentTarget.dataset.type

    this.setData({
      showDeleteMenu: true,
      currentDeleteComment: comment,
      currentDeleteType: type
    })
  },

  // 隐藏删除菜单
  hideDeleteMenu() {
    this.setData({
      showDeleteMenu: false,
      currentDeleteComment: null,
      currentDeleteType: ''
    })
  },

  // 删除评论
  deleteComment() {
    const comment = this.data.currentDeleteComment
    const type = this.data.currentDeleteType

    if (!comment) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: type === 'comment' ? '确定要删除这条评论吗？' : '确定要删除这条回复吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(comment.id)
        }
      }
    })
  },

  // 执行删除操作
  performDelete(commentId) {
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '删除中...'
    })

    wx.request({
      url: getApp().globalData.wangz + '/majorcomment/deleteComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: commentId
      },
      success: (res) => {
        wx.hideLoading()
        if (res.data.code === 200) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
          // 隐藏菜单
          this.hideDeleteMenu()
          // 刷新评论列表
          this.updateCommentsData()
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('删除失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 定位到指定评论
  scrollToTargetComment(commentId, replyId, comments) {
    try {
      let targetCommentIndex = -1
      let targetReplyIndex = -1

      // 查找目标评论或回复
      comments.forEach((comment, commentIndex) => {
        if (replyId) {
          // 查找回复
          if (comment.replies && comment.replies.length > 0) {
            comment.replies.forEach((reply, replyIndex) => {
              if (reply.id == replyId) {
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            })
          }
        } else if (commentId) {
          // 查找评论
          if (comment.id == commentId) {
            targetCommentIndex = commentIndex
          }
        }
      })

      // 如果找到了目标，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })

        // 添加高亮效果
        setTimeout(() => {
          this.highlightComment(scrollId)
        }, 300)
      }
    } catch (error) {
      console.error('定位评论失败:', error)
    }
  },

  // 高亮评论
  highlightComment(scrollId) {
    // 可以通过添加CSS类来实现高亮效果
    // 这里暂时使用简单的提示
    wx.showToast({
      title: '已定位到评论',
      icon: 'success',
      duration: 1500
    })
  },

  // 返回
  goBack() {
    wx.navigateBack();
  },

  // 分享给好友
  onShareAppMessage() {
    const { majorInfo } = this.data;
    return {
      title: `${majorInfo?.major_name || '专业推荐'} - 北航专业介绍`,
      path: `/pages/major/detail/index?id=${this.data.majorId}`,
      imageUrl: '/images/zhuanye.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { majorInfo } = this.data;
    return {
      title: `${majorInfo?.major_name || '专业推荐'} - 北航专业介绍`,
      query: `id=${this.data.majorId}`,
      imageUrl: '/images/zhuanye.png'
    };
  }
});
