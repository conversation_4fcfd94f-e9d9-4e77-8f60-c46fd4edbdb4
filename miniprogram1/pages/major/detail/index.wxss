/* 引用食堂列表的样式 */
@import "/pages/canteen/list/index.wxss";

/* 文字头像样式 */
.text-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

/* 四个字的网格布局 */
.avatar-text-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4rpx;
  width: 100%;
  height: 100%;
  padding: 12rpx;
  box-sizing: border-box;
}

.text-char {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

/* 两个字的布局 - 横版 */
.avatar-text-two {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 8rpx;
}

.text-char-two {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

/* 三个字的布局 - 横版 */
.avatar-text-three {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 4rpx;
}

.text-char-three {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

.canteen-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fbff 0%, #e0e7ef 100%);
  padding-bottom: 60rpx;
}

/* 专业头部信息 */
.major-header {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx 28rpx;
  margin: 24rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.major-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.major-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.major-meta {
  display: flex;
  gap: 16rpx;
}

.meta-item {
  font-size: 26rpx;
  color: #666;
  background: #f0f9ff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 信息区块 */
.info-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-label {
  font-size: 24rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 描述内容 */
.description-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

/* 相关专业推荐 */
.related-majors {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.related-major-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.related-major-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.related-major-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.related-major-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.related-major-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.related-major-college {
  font-size: 24rpx;
  color: #666;
}

/* 小尺寸文字头像样式 */
.avatar-text-two-small {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 4rpx;
}

.text-char-two-small {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

.avatar-text-three-small {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 2rpx;
}

.text-char-three-small {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
}

.avatar-text-grid-small {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 2rpx;
  width: 100%;
  height: 100%;
  padding: 6rpx;
  box-sizing: border-box;
}

.text-char-small {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #5ec6fa;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.empty-btn:active {
  background: #4db8e8;
  transform: scale(0.95);
}

/* 全部评论 */
.comments-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.comments-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.sort-tabs {
  display: flex;
  gap: 20rpx;
}

.sort-tab {
  font-size: 26rpx;
  color: #999;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.sort-tab.active {
  color: #5ec6fa;
  background: #f0f9ff;
  font-weight: 500;
}

/* 评论列表 */
.comments-list {
  padding: 0 32rpx 32rpx;
  margin-bottom: 40rpx;
}

.comment-item {
  position: relative;
  display: flex;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  position: relative;
  margin-bottom: 12rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.comment-more-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 36rpx;
  height: 36rpx;
  opacity: 0.5;
  transition: all 0.2s ease;
  border-radius: 50%;
  padding: 8rpx;
  z-index: 10;
}

.comment-more-icon:active {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 10rpx;
}

.comment-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-reply {
  font-size: 24rpx;
  color: #5ec6fa;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 15rpx; /* 增加点击区域 */
  margin: -10rpx -15rpx; /* 负边距保持视觉位置不变 */
  border-radius: 20rpx; /* 圆角让点击区域更自然 */
}

.comment-actions .action-icon {
  width: 28rpx;
  height: 28rpx;
}

.action-count {
  font-size: 24rpx;
  color: #999;
}

/* 回复列表样式 */
.replies-list {
  margin-top: 24rpx;
  padding-left: 20rpx; /* 减少左侧padding */
  border-left: 2rpx solid #f0f0f0;
}

.reply-item {
  position: relative;
  display: flex;
  gap: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  position: relative;
  margin-bottom: 8rpx;
}



.reply-username {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

.reply-more-icon {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  width: 36rpx;
  height: 36rpx;
  opacity: 0.5;
  transition: all 0.2s ease;
  border-radius: 50%;
  padding: 8rpx;
  z-index: 10;
}

.reply-more-icon:active {
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.reply-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.reply-prefix {
  color: #999; /* 回复xxx: 使用灰色 */
}

.reply-main-content {
  color: #333;
}

.reply-images {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  margin-top: 8rpx;
}

.reply-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 6rpx;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-reply {
  font-size: 24rpx;
  color: #5ec6fa;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 15rpx; /* 增加左右padding */
  margin: -10rpx -15rpx; /* 对应的负边距 */
  border-radius: 16rpx; /* 圆角让点击区域更自然 */
}

.reply-action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.reply-action-count {
  font-size: 22rpx;
  color: #666;
}

/* 无评论样式 */
.no-comment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  margin: 20rpx 40rpx;
  margin-top: 100rpx;
}

.no-comment-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.no-comment-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 底部遮罩 */
.bottom-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150rpx;
  background: linear-gradient(to top, rgba(248, 249, 250, 1) 0%, rgba(248, 249, 250, 0.8) 50%, rgba(248, 249, 250, 0) 100%);
  z-index: 99;
  pointer-events: none;
}

/* 底部输入框 */
.bottom-fixed-bar {
  position: fixed;
  bottom: 50rpx;
  left: 24rpx;
  right: 24rpx;
  width: auto;
  background-color: #fff;
  padding: 16rpx 24rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-sizing: border-box;
  border-radius: 40rpx;
}

.input-section {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 15rpx 30rpx;
  margin-right: 30rpx;
}

.comment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  margin: -15rpx;
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.action-item:active {
  background-color: transparent !important;
}

.action-icon {
  width: 35rpx;
  height: 35rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 评论遮罩和输入框样式 */
.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 998;
}

.fixed-comment-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 12rpx 24rpx;
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(0);
  transition: transform 0.3s ease-out;
}

.comment-section-with-keyboard {
  transform: translateY(calc(-1 * var(--keyboard-height)));
}

.comment-section-with-emoji {
  transform: translateY(-400rpx);
}

.comment-section-with-emoji-list {
  transform: translateY(-400rpx);
}



/* 常用表情栏 */
.quick-emoji-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.quick-emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.quick-emoji-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.emoji-text {
  font-size: 36rpx;
  line-height: 1;
}

/* 图片预览区域样式 - 在最上面 */
.image-preview-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 16rpx 24rpx 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.image-preview-item {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.remove-image-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 36rpx;
  height: 36rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

/* 输入框和工具栏容器 */
.input-tools-container {
  display: flex;
  flex-direction: column;
  background: #fff;
}

.comment-textarea {
  background-color: #f5f5f5;
  border-radius: 18rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.4;
  margin: 12rpx 24rpx 8rpx 24rpx;
  min-height: 80rpx;
  max-height: 200rpx;
  overflow-y: auto;
  transition: all 0.3s ease;
  width: calc(100% - 48rpx);
}

/* messagedetail样式的工具栏 */
.tools-row {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 24rpx 16rpx 24rpx;
  height: 72rpx;
  background-color: #fff;
  border-radius: 0 0 24rpx 24rpx;
}

.left-tools {
  display: flex;
  gap: 20rpx;
  align-items: center;
  flex: 1;
}

.tool-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  padding: 10rpx;
  border-radius: 50%;
}

.tool-item:active {
  background-color: #f5f5f5;
}

.tool-icon {
  width: 50rpx;
  height: 50rpx;
}

.emoji-icon {
  font-size: 40rpx;
  line-height: 1;
}

.image-count {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.submit-button {
  background: #5ec6fa;
  color: white;
  border: none;
  border-radius: 36rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  flex-shrink: 0;
}

.submit-button.disabled {
  background: #cccccc;
  color: #999999;
}

/* 表情面板 */
.emoji-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.emoji-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.emoji-item .emoji-text {
  font-size: 40rpx;
}

/* emoji表情面板 */
.emoji-list-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-list-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-list-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.emoji-list-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

/* 表情面板 */
.emoji-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.emoji-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.emoji-item .emoji-text {
  font-size: 40rpx;
}

/* emoji表情面板 */
.emoji-list-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-list-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-list-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.emoji-list-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

/* 评论图片样式 */
.comment-images {
  display: flex;
  gap: 8rpx;
  margin-top: 16rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.comment-image {
  width: calc(33.33% - 6rpx);
  height: 160rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

/* 回复图片样式 */
.reply-images {
  display: flex;
  gap: 8rpx;
  margin-top: 12rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.reply-image {
  width: calc(33.33% - 6rpx);
  height: 120rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}



/* 删除菜单 */
.delete-menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.delete-menu {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  padding: 40rpx;
}

.delete-menu-item {
  text-align: center;
  padding: 32rpx;
  font-size: 32rpx;
  color: #ff4757;
  border-bottom: 1rpx solid #eee;
}

.delete-menu-cancel {
  text-align: center;
  padding: 32rpx;
  font-size: 32rpx;
  color: #333;
}
