.canteen-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fbff 0%, #e0e7ef 100%);
  padding-bottom: 60rpx;
}

/* 分类选择器样式 */
.category-container {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 98;
  background: #fff;
  display: flex;
  align-items: center;
  height: 88rpx;
  border-bottom: 1rpx solid #eee;
}

.category-scroll {
  flex: 1;
  white-space: nowrap;
  margin-right: 80rpx;
}

.category-list {
  display: inline-flex;
  padding: 0 20rpx;
  white-space: nowrap;
}

.category-item {
  position: relative;
  display: inline-block;
  height: 88rpx;
  line-height: 88rpx;
  padding: 0 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.category-item:last-child {
  margin-right: 30rpx;
}

.category-item.active {
  color: #5ec6fa;
  font-weight: bold;
}

.category-item.active::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 6rpx;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #5ec6fa;
  border-radius: 3rpx;
}

.category-expand {
  width: 80rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, rgba(255,255,255,0), #fff 30%);
  position: fixed;
  right: 0;
  z-index: 98;
}

.expand-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
}

/* 专业列表样式 - 复用餐厅列表样式 */
.msg-list {
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 0;
  align-items: center;
  margin-top: 0;
  padding-bottom: 32rpx;
}

.canteen-card {
  width: 80vw;
  margin: 0 auto 24rpx auto;
  padding: 24rpx 28rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24rpx;
  transition: all 0.3s ease;
  min-height: 140rpx;
}

.canteen-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.02);
}

.canteen-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 18rpx;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  align-self: center;
}

.large-avatar {
  width: 140rpx;
  height: 140rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 文字头像样式 */
.text-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

/* 四个字的网格布局 */
.avatar-text-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4rpx;
  width: 100%;
  height: 100%;
  padding: 12rpx;
  box-sizing: border-box;
}

.text-char {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

/* 两个字的布局 - 横版 */
.avatar-text-two {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 8rpx;
}

.text-char-two {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

/* 三个字的布局 - 横版 */
.avatar-text-three {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 4rpx;
}

.text-char-three {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.adjust-info {
  gap: 12rpx;
}

.card-header-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 2;
  margin-top: -6rpx;
}

.canteen-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.total-rating-card {
  display: flex;
  align-items: center;
  background: #fff7e6;
  color: #ff9500;
  font-size: 26rpx;
  font-weight: 600;
  border-radius: 16rpx;
  padding: 0 18rpx;
  height: 48rpx;
  min-width: 80rpx;
  box-sizing: border-box;
  position: absolute;
  right: 0;
  top: 0;
}

.canteen-second-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 8rpx;
}

.hot-comment {
  font-size: 26rpx;
  color: #5ec6fa;
  font-weight: 600;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.score-people-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.comment-label {
  font-size: 24rpx;
  color: #5ec6fa;
  font-weight: 600;
  margin-bottom: 4rpx;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 30rpx;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 悬浮展开按钮（复用canteen的fixed-button2） */
.fixed-button2 {
  position: fixed;
  bottom: 180rpx;
  right: 32rpx;
  color: white;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 70rpx;
  height: 70rpx;
  z-index: 999;
  transition: all 0.3s cubic-bezier(.4,1.4,.6,1);
  box-shadow: 0 8rpx 32rpx rgba(52,135,239,0.13);
}

.fixed-button2:active {
  transform: scale(0.92);
  box-shadow: 0 4rpx 16rpx rgba(52,135,239,0.10);
}

.custom-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: block;
}

/* 弹窗样式复用canteen的modal样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.95);
  width: 600rpx;
  background: #fff;
  border-radius: 32rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modal.show .modal-content {
  transform: translate(-50%, -50%) scale(1);
}

.modal-header {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.department-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.department-item {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background: #f7f8fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.department-item.active {
  color: #fff;
  background: #5ec6fa;
}

.modal-footer {
  padding: 32rpx 40rpx;
  display: flex;
  gap: 24rpx;
  border-top: 2rpx solid #f5f5f5;
  justify-content: center;
}

.btn-cancel {
  flex: 1;
  height: 88rpx;
  background: #f7f8fa;
  border-radius: 44rpx;
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
