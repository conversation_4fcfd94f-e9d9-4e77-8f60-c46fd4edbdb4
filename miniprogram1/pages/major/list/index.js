const app = getApp();

Page({
  data: {
    currentDepartment: null,
    showCategoryPopup: false,
    majorList: [],
    loading: false,
    hasMore: true,
    page: 1,
    departments: [],
    statusBarHeight: 0,
    navBarHeight: 44
  },

  onLoad() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    this.loadDepartments();
  },

  // 生成随机浅彩色
  generateRandomColor() {
    const colors = [
      '#A8E6CF', '#FFD3A5', '#FD9696', '#C7CEEA', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#F7DC6F',
      '#D7BDE2', '#A9DFBF', '#F9E79F', '#AED6F1', '#F5B7B1'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // 检查是否为汉字
  isChineseChar(char) {
    return /[\u4e00-\u9fa5]/.test(char);
  },

  // 处理专业头像
  processMajorAvatar(major) {
    const majorName = major.major_name || '';
    // 提取汉字
    const chineseChars = majorName.split('').filter(char => this.isChineseChar(char));

    let avatarTextArray = [];
    if (chineseChars.length >= 4) {
      // 四个字以上取前四个字
      avatarTextArray = chineseChars.slice(0, 4);
    } else if (chineseChars.length === 3) {
      // 三个字
      avatarTextArray = chineseChars;
    } else if (chineseChars.length === 2) {
      // 两个字
      avatarTextArray = chineseChars;
    } else if (chineseChars.length === 1) {
      // 一个字，重复四次
      avatarTextArray = [chineseChars[0], chineseChars[0], chineseChars[0], chineseChars[0]];
    } else {
      // 没有汉字，使用默认
      avatarTextArray = ['专', '业', '推', '荐'];
    }

    // 添加评论类型标签
    let commentLabel = '';
    if (major.hot_comment) {
      commentLabel = major.has_likes ? '热评' : '最新评论';
    }

    return {
      ...major,
      avatarTextArray: avatarTextArray,
      avatar_color: this.generateRandomColor(),
      comment_label: commentLabel
    };
  },

  // 加载系别列表
  loadDepartments() {
    const token = wx.getStorageSync('access_token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: `${app.globalData.wangz}/major/departments`,
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data && res.data.error_code === 0) {
          const departments = res.data.data || [];
          this.setData({
            departments: departments,
            currentDepartment: departments.length > 0 ? departments[0].id : null
          });
          // 加载第一个系别的专业
          if (departments.length > 0) {
            this.loadMajorList();
          }
        } else {
          console.error('获取系别列表失败:', res.data?.msg || '未知错误');
        }
      },
      fail: (error) => {
        console.error('获取系别列表异常:', error);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 加载专业列表
  async loadMajorList() {
    // 如果是分页加载且没有更多数据，则返回
    if (this.data.page > 1 && !this.data.hasMore) return;

    // 如果已经在loading且不是第一页，则返回（避免重复请求）
    if (this.data.loading && this.data.page > 1) return;

    this.setData({ loading: true });

    try {
      const token = wx.getStorageSync('access_token');
      if (!token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const params = {
        page: this.data.page,
        page_size: 20,
        department_id: this.data.currentDepartment || ''
      };

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.wangz + '/major/list',
          method: 'POST',
          header: {
            'token': token,
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: params,
          success: resolve,
          fail: reject
        });
      });

      if (res.data.error_code === 0) {
        const list = res.data.data || [];
        // 为每个专业生成文字头像和颜色
        const processedList = list.map(item => this.processMajorAvatar(item));
        const newList = this.data.page === 1 ? processedList : [...this.data.majorList, ...processedList];

        this.setData({
          majorList: newList,
          hasMore: list.length === 20,
          page: this.data.page + 1
        });

        if (this.data.page === 1 && newList.length === 0) {
          wx.showToast({
            title: '暂无专业数据',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: res.data.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载专业列表失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 切换系别
  switchDepartment(e) {
    const department = e.currentTarget.dataset.department;
    if (department === this.data.currentDepartment) return;

    this.setData({
      currentDepartment: department,
      majorList: [], // 清空列表，显示loading
      page: 1,
      hasMore: true,
      loading: true
    });

    this.loadMajorList();
  },

  // 显示所有分类
  showAllCategories() {
    this.setData({ showCategoryPopup: true });
  },

  // 关闭弹窗
  onPopupClose() {
    this.setData({ showCategoryPopup: false });
  },

  // 在弹窗中选择分类
  onCategorySelect(e) {
    const department = e.currentTarget.dataset.department;
    this.setData({
      currentDepartment: department,
      showCategoryPopup: false,
      majorList: [], // 清空列表，显示loading
      page: 1,
      hasMore: true,
      loading: true
    });

    // 滑动到选中的系别
    this.scrollToSelectedCategory(department);

    this.loadMajorList();
  },

  // 滑动到选中的系别
  scrollToSelectedCategory(departmentId) {
    const departments = this.data.departments;
    const index = departments.findIndex(item => item.id === departmentId);
    if (index !== -1) {
      // 计算滚动位置，每个分类项大约120rpx宽度
      const scrollLeft = Math.max(0, index * 120 - 100); // 减去100rpx让选中项不在最左边

      // 延迟执行滚动，确保DOM已更新
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this);
        query.select('.category-scroll').node((res) => {
          if (res && res.node) {
            res.node.scrollLeft = scrollLeft;
          }
        });
        query.exec();
      }, 200);
    }
  },

  // 跳转到专业详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/major/detail/index?id=${id}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      majorList: [],
      page: 1,
      hasMore: true
    });
    this.loadMajorList().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMajorList();
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '专业推荐',
      path: '/pages/major/list/index',
      imageUrl: '/images/zhuanye.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '专业推荐',
      query: '',
      imageUrl: '/images/zhuanye.png'
    };
  }
});
