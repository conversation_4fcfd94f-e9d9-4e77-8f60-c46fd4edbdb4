<view class="canteen-bg">
  <custom-nav-bar title="专业推荐" back="true"></custom-nav-bar>
  <loading show="{{loading && majorList.length === 0}}" mask="false"></loading>
  <view>
    <!-- 系别分类选择器 -->
    <view class="category-container" style="top: {{statusBarHeight + navBarHeight}}px;">
      <scroll-view class="category-scroll"
                   scroll-x="true"
                   enhanced="true"
                   show-scrollbar="false"
                   scroll-with-animation="true"
                   enable-flex="true">
        <view class="category-list">
          <view class="category-item {{currentDepartment === item.id ? 'active' : ''}}"
                wx:for="{{departments}}" wx:key="id"
                bindtap="switchDepartment"
                data-department="{{item.id}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
      <view class="category-expand" bindtap="showAllCategories" style="top: {{statusBarHeight + navBarHeight}}px;">
        <image class="expand-icon" src="/images/youjiantou-3-2.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 专业列表 - 使用与餐厅列表相同的样式 -->
    <view class="msg-list" style="margin-top: 108rpx;">
      <view class="canteen-card" wx:for="{{majorList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="canteen-avatar large-avatar">
          <view class="text-avatar" style="background-color: {{item.avatar_color}}">
            <view wx:if="{{item.avatarTextArray.length === 2}}" class="avatar-text-two">
              <view wx:for="{{item.avatarTextArray}}" wx:key="index" class="text-char-two">{{item}}</view>
            </view>
            <view wx:elif="{{item.avatarTextArray.length === 3}}" class="avatar-text-three">
              <view wx:for="{{item.avatarTextArray}}" wx:key="index" class="text-char-three">{{item}}</view>
            </view>
            <view wx:else class="avatar-text-grid">
              <view wx:for="{{item.avatarTextArray}}" wx:key="index" class="text-char">{{item}}</view>
            </view>
          </view>
        </view>
        <view class="card-info adjust-info">
          <view class="card-header-row">
            <text class="canteen-name">{{item.major_name}}</text>
          </view>
          <view class="canteen-second-row">
            <view class="hot-comment" wx:if="{{item.hot_comment}}">"{{item.hot_comment}}"</view>
            <view class="hot-comment" wx:else>暂无评论</view>
            <text class="score-people-text">{{item.comment_count || 0}}人已评价</text>
          </view>
        </view>
      </view>
    </view>



    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && majorList.length > 0}}">
      <image class="loading-icon" src="/images/aixin.png" mode="aspectFit"></image>
      <text class="loading-text">加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && majorList.length > 0}}">
      没有更多了
    </view>
    <view class="empty" wx:if="{{!loading && majorList.length === 0 && departments.length > 0}}">
      暂无专业数据
    </view>
  </view>

  <!-- 分类弹窗 - 使用canteen样式的modal -->
  <view class="modal {{showCategoryPopup?'show':''}}" wx:if="{{showCategoryPopup}}">
    <view class="modal-mask" bindtap="onPopupClose"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">选择系别</view>
      </view>
      <view class="modal-body">
        <view class="department-grid">
          <view class="department-item {{currentDepartment === item.id ? 'active' : ''}}"
                wx:for="{{departments}}" wx:key="id"
                bindtap="onCategorySelect"
                data-department="{{item.id}}">
            {{item.name}}
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="onPopupClose">取消</button>
      </view>
    </view>
  </view>
</view>
