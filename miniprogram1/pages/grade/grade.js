// pages/grade/grade.js
Page({
  data: {
    // 当前学期信息 - 将在onLoad中根据当前日期动态设置
    currentYear: '',
    currentSemester: 1,
    currentSemesterDisplay: '',

    // 学期范围限制
    defaultYear: '',
    defaultSemester: 1,
    maxYear: '',
    maxSemester: 1,
    minYear: '',
    minSemester: 1,

    // 成绩数据
    gradeList: [],
    totalCredits: 0,
    currentSemesterTitle: '',
    userGPA: '0.00', // GPA

    // 数据缓存
    semesterDataCache: {}, // 缓存各学期的成绩数据
    preloadQueue: [], // 预加载队列
    isPreloading: false, // 是否正在预加载

    // 状态控制
    isLoading: false,
    hasQueried: false,
    isAtMinSemester: false,
    isAtMaxSemester: false,
    hasRetried: false, // 是否已重试过预加载数据
    isRefreshing: false // 是否正在下拉刷新
  },

  onLoad(options) {
    // 重置状态，但保留缓存数据
    this.setData({
      preloadQueue: [],
      isPreloading: false,
      hasRetried: false
    });

    // 根据当前日期设置默认学期
    this.setDefaultSemester();

    // 尝试立即加载缓存数据，避免闪烁
    this.loadCachedDataImmediately();

    // 检查是否有SSO凭据
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      // 没有凭据，跳转到SSO页面
      wx.showModal({
        title: '需要认证',
        content: '查询成绩需要先进行统一身份认证',
        showCancel: false,
        confirmText: '去认证',
        success: () => {
          wx.redirectTo({
            url: '/pages/foldshare/sso/sso?from=grade'
          });
        }
      });
      return;
    }

    // 有凭据，分步加载：先成绩，再GPA，最后预加载
    // 如果已经有缓存数据，立即开始后续流程；否则稍等片刻
    const hasCache = this.data.hasQueried;
    setTimeout(() => {
      this.loadGradesStepByStep();
    }, hasCache ? 100 : 300);
  },

  // 立即加载缓存数据，避免页面闪烁
  loadCachedDataImmediately() {
    const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;

    // 检查本地存储缓存
    try {
      const localGradeCache = wx.getStorageSync('gradeCache') || {};
      if (localGradeCache[cacheKey]) {
        const cachedData = localGradeCache[cacheKey];

        // 立即显示缓存数据，避免闪烁
        this.setData({
          gradeList: cachedData.gradeList,
          totalCredits: cachedData.totalCredits,
          userGPA: cachedData.userGPA,
          hasQueried: true,
          isLoading: false
        });

        // 更新页面缓存
        const newCache = { ...this.data.semesterDataCache };
        newCache[cacheKey] = cachedData;
        this.setData({
          semesterDataCache: newCache
        });
      }
    } catch (error) {
      // 静默处理错误
    }
  },

  onUnload() {
    // 页面卸载时停止预加载
    this.setData({
      isPreloading: false,
      preloadQueue: []
    });

    // 确保tabBar显示（解决从成绩页面返回后tabBar不显示的问题）
    try {
      wx.showTabBar({
        animation: false
      });
    } catch (error) {
      // 静默处理错误，如果当前页面不是tabBar页面会报错
    }
  },

  // 分步加载：优先使用缓存数据 -> 成绩 -> GPA -> 预加载
  loadGradesStepByStep() {
    // 检查本地存储缓存
    const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;

    try {
      const localGradeCache = wx.getStorageSync('gradeCache') || {};
      if (localGradeCache[cacheKey]) {
        const cachedData = localGradeCache[cacheKey];

        // 直接使用缓存的数据
        this.setData({
          gradeList: cachedData.gradeList,
          totalCredits: cachedData.totalCredits,
          userGPA: cachedData.userGPA,
          sessionCookies: cachedData.cookies,
          isLoading: false,
          hasQueried: true
        });

        // 更新页面缓存
        const newCache = { ...this.data.semesterDataCache };
        newCache[cacheKey] = cachedData;
        this.setData({
          semesterDataCache: newCache
        });

        // 启动静默更新检查（默认学期及前后两个学期）
        setTimeout(() => {
          this.silentUpdateDefaultAndNearbySemesters();
        }, 500);

        // 直接开始预加载其他学期
        setTimeout(() => {
          this.startPreloadOtherSemesters();
        }, 300);

        return;
      }
    } catch (error) {
      // 静默处理错误
    }

    // 如果没有预加载数据，等待一下再重试（可能预加载还在进行中）
    if (!this.data.hasRetried) {
      this.setData({ hasRetried: true });
      setTimeout(() => {
        this.loadGradesStepByStep();
      }, 500);
      return;
    }

    // 没有预加载数据，按原流程加载
    // 第1步：快速加载默认学期成绩
    this.queryGradesQuick(() => {
      // 第2步：加载GPA
      this.loadGPA(() => {
        // 第3步：启动静默更新检查（默认学期及前后两个学期）
        setTimeout(() => {
          this.silentUpdateDefaultAndNearbySemesters();
        }, 500);

        // 第4步：开始预加载其他学期
        setTimeout(() => {
          this.startPreloadOtherSemesters();
        }, 1000);
      });
    });
  },

  // 根据当前日期设置默认学期
  setDefaultSemester() {
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth()返回0-11，需要+1
    const currentYear = now.getFullYear();

    let academicYear, semester;

    if (currentMonth >= 3 && currentMonth <= 9) {
      // 3-9月显示当年的第二学期（春季学期）
      academicYear = `${currentYear - 1}-${currentYear}`;
      semester = 2;
    } else {
      // 10-12月和1-2月显示对应学年的第一学期（秋季学期）
      if (currentMonth >= 10) {
        // 10-12月，显示当年开始的学年第一学期
        academicYear = `${currentYear}-${currentYear + 1}`;
        semester = 1;
      } else {
        // 1-2月，显示上一年开始的学年第一学期
        academicYear = `${currentYear - 1}-${currentYear}`;
        semester = 1;
      }
    }

    const display = `${academicYear}年第${semester}学期`;

    // 计算学期范围限制
    const { maxYear, maxSemester, minYear, minSemester } = this.calculateSemesterRange(academicYear, semester);

    // 检查是否在边界学期
    const isAtMin = this.isSemesterEqual(academicYear, semester, minYear, minSemester);
    const isAtMax = this.isSemesterEqual(academicYear, semester, maxYear, maxSemester);

    this.setData({
      currentYear: academicYear,
      currentSemester: semester,
      currentSemesterDisplay: display,
      defaultYear: academicYear,
      defaultSemester: semester,
      maxYear: maxYear,
      maxSemester: maxSemester,
      minYear: minYear,
      minSemester: minSemester,
      isAtMinSemester: isAtMin,
      isAtMaxSemester: isAtMax
    });


  },

  // 计算学期范围限制
  calculateSemesterRange(defaultYear, defaultSemester) {
    // 解析默认学年
    const yearParts = defaultYear.split('-');
    const startYear = parseInt(yearParts[0]);

    // 计算最大学期（默认学期后两个学期）
    let maxYear = defaultYear;
    let maxSemester = defaultSemester + 2;

    // 处理学期跨年
    if (maxSemester > 3) {
      const extraYears = Math.floor((maxSemester - 1) / 3);
      maxSemester = ((maxSemester - 1) % 3) + 1;
      const newStartYear = startYear + extraYears;
      maxYear = `${newStartYear}-${newStartYear + 1}`;
    }

    // 计算最小学期（四年前的第一学期）
    const minStartYear = startYear - 4;
    const minYear = `${minStartYear}-${minStartYear + 1}`;
    const minSemester = 1;

    return { maxYear, maxSemester, minYear, minSemester };
  },

  // 检查两个学期是否相等
  isSemesterEqual(year1, semester1, year2, semester2) {
    return year1 === year2 && semester1 === semester2;
  },

  // 上一个学期
  previousSemester() {
    // 如果正在查询，不允许切换
    if (this.data.isLoading) {
      return;
    }

    let { currentYear, currentSemester } = this.data;

    currentSemester--;
    if (currentSemester < 1) {
      // 跨年度，回到上一学年的第3学期
      const yearParts = currentYear.split('-');
      const startYear = parseInt(yearParts[0]) - 1;
      const endYear = parseInt(yearParts[1]) - 1;
      currentYear = `${startYear}-${endYear}`;
      currentSemester = 3;
    }

    // 检查是否超出最小范围
    if (this.isSemesterOutOfRange(currentYear, currentSemester, 'min')) {
      wx.showToast({
        title: '已到达最早学期',
        icon: 'none'
      });
      return;
    }

    this.updateSemester(currentYear, currentSemester);
  },

  // 下一个学期
  nextSemester() {
    // 如果正在查询，不允许切换
    if (this.data.isLoading) {
      return;
    }

    let { currentYear, currentSemester } = this.data;

    currentSemester++;
    if (currentSemester > 3) {
      // 跨年度，进入下一学年的第1学期
      const yearParts = currentYear.split('-');
      const startYear = parseInt(yearParts[0]) + 1;
      const endYear = parseInt(yearParts[1]) + 1;
      currentYear = `${startYear}-${endYear}`;
      currentSemester = 1;
    }

    // 检查是否超出最大范围
    if (this.isSemesterOutOfRange(currentYear, currentSemester, 'max')) {
      wx.showToast({
        title: '已到达最晚学期',
        icon: 'none'
      });
      return;
    }

    this.updateSemester(currentYear, currentSemester);
  },

  // 检查学期是否超出范围
  isSemesterOutOfRange(year, semester, type) {
    const { minYear, minSemester, maxYear, maxSemester } = this.data;

    // 将学年转换为数字进行比较
    const yearParts = year.split('-');
    const startYear = parseInt(yearParts[0]);

    if (type === 'min') {
      const minYearParts = minYear.split('-');
      const minStartYear = parseInt(minYearParts[0]);

      if (startYear < minStartYear) {
        return true;
      } else if (startYear === minStartYear && semester < minSemester) {
        return true;
      }
    } else if (type === 'max') {
      const maxYearParts = maxYear.split('-');
      const maxStartYear = parseInt(maxYearParts[0]);

      if (startYear > maxStartYear) {
        return true;
      } else if (startYear === maxStartYear && semester > maxSemester) {
        return true;
      }
    }

    return false;
  },

  // 更新学期显示并自动查询
  updateSemester(year, semester) {
    const display = `${year}年第${semester}学期`;

    // 检查是否在边界学期
    const isAtMin = this.isSemesterEqual(year, semester, this.data.minYear, this.data.minSemester);
    const isAtMax = this.isSemesterEqual(year, semester, this.data.maxYear, this.data.maxSemester);

    this.setData({
      currentYear: year,
      currentSemester: semester,
      currentSemesterDisplay: display,
      hasQueried: false,
      gradeList: [],
      totalCredits: 0,
      userGPA: '0.00',
      isAtMinSemester: isAtMin,
      isAtMaxSemester: isAtMax
    });

    // 检查可用的缓存数据
    const cacheKey = `${year}-${semester}`;
    let cachedData = null;

    // 1. 首先检查页面缓存
    if (this.data.semesterDataCache[cacheKey]) {
      cachedData = this.data.semesterDataCache[cacheKey];
    }
    // 2. 检查本地存储缓存
    else {
      try {
        const localGradeCache = wx.getStorageSync('gradeCache') || {};
        if (localGradeCache[cacheKey]) {
          cachedData = localGradeCache[cacheKey];
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    if (cachedData) {
      // 使用缓存数据，快速显示
      this.setData({
        gradeList: cachedData.gradeList,
        totalCredits: cachedData.totalCredits,
        userGPA: cachedData.userGPA,
        hasQueried: true
      });

      // 将数据同步到页面缓存中，方便下次快速访问
      if (!this.data.semesterDataCache[cacheKey]) {
        const newCache = { ...this.data.semesterDataCache };
        newCache[cacheKey] = cachedData;
        this.setData({
          semesterDataCache: newCache
        });
      }
    } else {
      // 没有任何缓存，需要查询
      this.queryGrades();
    }
  },

  // 快速查询成绩（不包含GPA）
  queryGradesQuick(callback) {
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      wx.showToast({
        title: '请先进行统一身份认证',
        icon: 'none'
      });
      return;
    }

    const { currentYear, currentSemester } = this.data;

    this.setData({
      isLoading: true,
      currentSemesterTitle: `${currentYear}-第${currentSemester}学期`
    });

    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getScoreQuick',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: currentYear,
        xq: currentSemester
      },
      success: (res) => {
        this.handleQuickGradeResponse(res.data, callback);
      },
      fail: (error) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
        callback && callback();
      }
    });
  },

  // 查询成绩（完整版，包含GPA）
  queryGrades() {
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      wx.showToast({
        title: '请先进行统一身份认证',
        icon: 'none'
      });
      return;
    }

    const { currentYear, currentSemester } = this.data;

    this.setData({
      isLoading: true,
      currentSemesterTitle: `${currentYear}-第${currentSemester}学期`
    });

    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getScoreOneStep',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: currentYear,
        xq: currentSemester
      },
      success: (res) => {
        this.handleGradeResponse(res.data);
      },
      fail: (error) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      }
    });
  },

  // 处理快速查询响应（不包含GPA）
  handleQuickGradeResponse(data, callback) {
    this.setData({
      isLoading: false,
      hasQueried: true
    });

    if (data && data.error_code === 0) {
      const scores = data.data?.d || [];

      // 计算总学分
      let totalCredits = 0;
      scores.forEach(score => {
        const credit = parseFloat(score.xf) || 0;
        totalCredits += credit;
      });

      const gradeData = {
        gradeList: scores,
        totalCredits: totalCredits.toFixed(1),
        userGPA: '加载中...' // GPA稍后加载
      };

      this.setData(gradeData);

      // 缓存当前学期数据（暂时不包含GPA）
      const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;
      const newCache = { ...this.data.semesterDataCache };
      newCache[cacheKey] = gradeData;
      this.setData({
        semesterDataCache: newCache
      });



      if (scores.length === 0) {
        wx.showToast({
          title: '该学期暂无成绩',
          icon: 'none'
        });
      }

      // 保存cookies供后续使用
      if (data.cookies) {
        this.setData({
          sessionCookies: data.cookies
        });
      }

      callback && callback();
    } else {
      wx.showToast({
        title: data?.msg || '查询失败，请重试',
        icon: 'none'
      });
      callback && callback();
    }
  },

  // 加载GPA
  loadGPA(callback) {
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      callback && callback();
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getGpaOnly',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword
      },
      success: (res) => {
        if (res.data && res.data.error_code === 0) {
          let gpa = '0.00';
          if (res.data.data && res.data.data.gpa) {
            gpa = parseFloat(res.data.data.gpa).toFixed(2);
          }

          // 更新GPA显示
          this.setData({
            userGPA: gpa
          });

          // 更新缓存中的GPA
          const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;

          // 1. 更新页面缓存
          const newCache = { ...this.data.semesterDataCache };
          if (newCache[cacheKey]) {
            newCache[cacheKey].userGPA = gpa;
            this.setData({
              semesterDataCache: newCache
            });

            // 2. 更新本地存储缓存
            try {
              const localGradeCache = wx.getStorageSync('gradeCache') || {};
              if (localGradeCache[cacheKey]) {
                localGradeCache[cacheKey].userGPA = gpa;
                wx.setStorageSync('gradeCache', localGradeCache);
              }
            } catch (error) {
              // 静默处理错误
            }
          }

          // 保存cookies供后续使用
          if (res.data.cookies) {
            this.setData({
              sessionCookies: res.data.cookies
            });
          }
        }
        callback && callback();
      },
      fail: (error) => {
        // GPA加载失败不影响主流程
        this.setData({
          userGPA: '获取失败'
        });
        callback && callback();
      }
    });
  },

  // 处理成绩查询响应（完整版）
  handleGradeResponse(data) {
    this.setData({
      isLoading: false,
      hasQueried: true
    });

    if (data && data.error_code === 0) {
      const scores = data.data?.d || [];

      // 计算总学分
      let totalCredits = 0;
      scores.forEach(score => {
        const credit = parseFloat(score.xf) || 0;
        totalCredits += credit;
      });

      // 提取GPA信息
      let gpa = '0.00';
      if (data.user_info && data.user_info.gpa) {
        gpa = parseFloat(data.user_info.gpa).toFixed(2);
      }

      const gradeData = {
        gradeList: scores,
        totalCredits: totalCredits.toFixed(1),
        userGPA: gpa
      };

      this.setData(gradeData);

      // 缓存当前学期数据
      const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;

      // 1. 缓存到页面缓存
      const newCache = { ...this.data.semesterDataCache };
      newCache[cacheKey] = gradeData;
      this.setData({
        semesterDataCache: newCache
      });

      // 2. 缓存到本地存储
      try {
        const localGradeCache = wx.getStorageSync('gradeCache') || {};
        localGradeCache[cacheKey] = gradeData;
        wx.setStorageSync('gradeCache', localGradeCache);
      } catch (error) {
        // 静默处理错误
      }

      if (scores.length === 0) {
        wx.showToast({
          title: '该学期暂无成绩',
          icon: 'none'
        });
      }
    } else {
      wx.showToast({
        title: data?.msg || '查询失败，请重试',
        icon: 'none'
      });
    }
  },

  // 开始预加载其他学期数据
  startPreloadOtherSemesters() {
    if (this.data.isPreloading) {
      return;
    }

    this.setData({ isPreloading: true });

    // 生成预加载队列
    const preloadQueue = this.generatePreloadQueue();
    this.setData({ preloadQueue });

    // 开始预加载
    this.preloadNextSemester();
  },

  // 生成预加载队列
  generatePreloadQueue() {
    const queue = [];
    const { defaultYear, defaultSemester, minYear, minSemester, maxYear, maxSemester } = this.data;

    let priority = 1;

    // 1. 默认学期前一个学期（优先级最高）
    const { year: prevYear, semester: prevSemester } = this.getPreviousSemester(defaultYear, defaultSemester, 1);
    if (!this.isSemesterOutOfRange(prevYear, prevSemester, 'min')) {
      queue.push({ year: prevYear, semester: prevSemester, priority: priority++ });
    }

    // 2. 默认学期后一个学期
    const { year: nextYear, semester: nextSemester } = this.getNextSemester(defaultYear, defaultSemester, 1);
    if (!this.isSemesterOutOfRange(nextYear, nextSemester, 'max')) {
      queue.push({ year: nextYear, semester: nextSemester, priority: priority++ });
    }

    // 3. 继续往前排（前2个、前3个...直到4年前）
    for (let i = 2; i <= 12; i++) { // 最多预加载4年前的数据（4年*3学期=12）
      const { year, semester } = this.getPreviousSemester(defaultYear, defaultSemester, i);
      if (!this.isSemesterOutOfRange(year, semester, 'min')) {
        queue.push({ year, semester, priority: priority++ });
      }
    }

    // 4. 最后是后面第二个学期（优先级最低）
    const { year: next2Year, semester: next2Semester } = this.getNextSemester(defaultYear, defaultSemester, 2);
    if (!this.isSemesterOutOfRange(next2Year, next2Semester, 'max')) {
      queue.push({ year: next2Year, semester: next2Semester, priority: priority++ });
    }

    // 按优先级排序
    queue.sort((a, b) => a.priority - b.priority);

    return queue;
  },

  // 获取向后第n个学期
  getNextSemester(baseYear, baseSemester, steps) {
    let year = baseYear;
    let semester = baseSemester + steps;

    while (semester > 3) {
      semester -= 3;
      const yearParts = year.split('-');
      const startYear = parseInt(yearParts[0]) + 1;
      year = `${startYear}-${startYear + 1}`;
    }

    return { year, semester };
  },

  // 获取向前第n个学期
  getPreviousSemester(baseYear, baseSemester, steps) {
    let year = baseYear;
    let semester = baseSemester - steps;

    while (semester < 1) {
      semester += 3;
      const yearParts = year.split('-');
      const startYear = parseInt(yearParts[0]) - 1;
      year = `${startYear}-${startYear + 1}`;
    }

    return { year, semester };
  },

  // 预加载下一个学期
  preloadNextSemester() {
    const { preloadQueue } = this.data;

    if (preloadQueue.length === 0) {
      this.setData({ isPreloading: false });
      return;
    }

    const nextItem = preloadQueue.shift();
    const cacheKey = `${nextItem.year}-${nextItem.semester}`;

    // 检查是否已经缓存
    if (this.data.semesterDataCache[cacheKey]) {
      this.setData({ preloadQueue });
      // 继续下一个
      setTimeout(() => {
        this.preloadNextSemester();
      }, 100);
      return;
    }

    // 发起预加载请求
    this.preloadSemesterData(nextItem.year, nextItem.semester, () => {
      this.setData({ preloadQueue });
      // 延迟一点时间再加载下一个，避免请求过于频繁
      setTimeout(() => {
        this.preloadNextSemester();
      }, 500);
    });
  },

  // 预加载指定学期数据（使用快速接口）
  preloadSemesterData(year, semester, callback) {
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      callback && callback();
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getScoreQuick',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: year,
        xq: semester
      },
      success: (res) => {
        this.handlePreloadResponse(res.data, year, semester);
        callback && callback();
      },
      fail: (error) => {
        callback && callback();
      }
    });
  },

  // 处理预加载响应（使用缓存的GPA）
  handlePreloadResponse(data, year, semester) {
    if (data && data.error_code === 0) {
      const scores = data.data?.d || [];

      // 计算总学分
      let totalCredits = 0;
      scores.forEach(score => {
        const credit = parseFloat(score.xf) || 0;
        totalCredits += credit;
      });

      // 使用当前缓存的GPA（预加载不重新获取GPA）
      let gpa = this.data.userGPA || '0.00';

      const gradeData = {
        gradeList: scores,
        totalCredits: totalCredits.toFixed(1),
        userGPA: gpa
      };

      // 缓存数据
      const cacheKey = `${year}-${semester}`;
      const newCache = { ...this.data.semesterDataCache };
      newCache[cacheKey] = gradeData;
      this.setData({
        semesterDataCache: newCache
      });
    }
  },

  // 静默更新检查：检查默认学期及前后两个学期的数据
  silentUpdateCheck() {
    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      return;
    }

    // 计算需要检查的学期：默认学期及前后两个学期
    const semestersToCheck = this.calculateSemestersToCheck();

    semestersToCheck.forEach((semesterInfo, index) => {
      // 错开请求时间，避免影响用户体验
      setTimeout(() => {
        this.checkAndUpdateSemester(semesterInfo, ssoUsername, ssoPassword);
      }, index * 300);
    });
  },

  // 计算需要检查的学期（默认学期及前后两个学期）
  calculateSemestersToCheck() {
    const semesters = [];
    const currentYear = this.data.currentYear;
    const currentSemester = this.data.currentSemester;

    // 添加前两个学期
    for (let i = 1; i <= 2; i++) {
      const prev = this.getPreviousSemester(currentYear, currentSemester, i);
      semesters.unshift({ year: prev.year, semester: prev.semester });
    }

    // 添加当前学期
    semesters.push({ year: currentYear, semester: currentSemester });

    // 添加后两个学期
    for (let i = 1; i <= 2; i++) {
      const next = this.getNextSemester(currentYear, currentSemester, i);
      semesters.push({ year: next.year, semester: next.semester });
    }

    return semesters;
  },

  // 检查并更新单个学期的数据
  checkAndUpdateSemester(semesterInfo, ssoUsername, ssoPassword) {
    const { year, semester } = semesterInfo;

    // 验证数据有效性
    if (!year || !year.includes('-') || !semester || ![1, 2, 3].includes(semester)) {
      console.error('[静默更新] 学期数据无效，跳过:', { year, semester });
      return;
    }

    const cacheKey = `${year}-${semester}`;

    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getScoreOneStep',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: year,
        xq: semester
      },
      success: (res) => {
        if (res.data && res.data.error_code === 0) {
          const newGradeData = {
            gradeList: res.data.data?.d || [],
            totalCredits: this.calculateTotalCreditsForGrades(res.data.data?.d || []),
            userGPA: res.data.data?.gpa || '0.00',
            cookies: res.data.cookies,
            timestamp: Date.now()
          };

          // 检查是否与本地缓存数据不同
          const app = getApp();
          const localCache = app.globalData.gradeCache || {};
          const cachedData = localCache[cacheKey];

          if (this.isDataDifferent(cachedData, newGradeData)) {
            // 更新全局缓存
            if (!app.globalData.gradeCache) {
              app.globalData.gradeCache = {};
            }
            app.globalData.gradeCache[cacheKey] = newGradeData;

            // 更新本地存储
            try {
              const localGradeCache = wx.getStorageSync('gradeCache') || {};
              localGradeCache[cacheKey] = newGradeData;
              wx.setStorageSync('gradeCache', localGradeCache);
            } catch (error) {
              // 静默处理错误
            }

            // 如果更新的是当前显示的学期，刷新页面数据
            if (year === this.data.currentYear && semester === this.data.currentSemester) {
              this.setData({
                gradeList: newGradeData.gradeList,
                totalCredits: newGradeData.totalCredits,
                userGPA: newGradeData.userGPA
              });

              // 更新本地缓存
              const newCache = { ...this.data.semesterDataCache };
              newCache[cacheKey] = newGradeData;
              this.setData({
                semesterDataCache: newCache
              });
            }
          }
        }
      },
      fail: (error) => {
        // 静默处理错误
      }
    });
  },

  // 判断数据是否不同
  isDataDifferent(cachedData, newData) {
    if (!cachedData) return true;

    // 比较成绩列表长度
    if (cachedData.gradeList.length !== newData.gradeList.length) {
      return true;
    }

    // 比较GPA
    if (cachedData.userGPA !== newData.userGPA) {
      return true;
    }

    // 比较总学分
    if (cachedData.totalCredits !== newData.totalCredits) {
      return true;
    }

    // 简单比较成绩列表（可以根据需要增加更详细的比较）
    for (let i = 0; i < newData.gradeList.length; i++) {
      const cached = cachedData.gradeList[i];
      const newGrade = newData.gradeList[i];
      if (!cached || cached.kcmc !== newGrade.kcmc || cached.kccj !== newGrade.kccj) {
        return true;
      }
    }

    return false;
  },

  // 计算总学分（用于静默更新）
  calculateTotalCreditsForGrades(scores) {
    let totalCredits = 0;
    scores.forEach(score => {
      const credit = parseFloat(score.xf) || 0;
      totalCredits += credit;
    });
    return totalCredits.toFixed(1);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ isRefreshing: true });

    // 清除当前学期的缓存
    const cacheKey = `${this.data.currentYear}-${this.data.currentSemester}`;
    const app = getApp();

    // 清除全局缓存中的当前学期数据
    if (app.globalData.gradeCache && app.globalData.gradeCache[cacheKey]) {
      delete app.globalData.gradeCache[cacheKey];
    }

    // 清除本地存储中的当前学期数据
    try {
      const localGradeCache = wx.getStorageSync('gradeCache') || {};
      if (localGradeCache[cacheKey]) {
        delete localGradeCache[cacheKey];
        wx.setStorageSync('gradeCache', localGradeCache);
      }
    } catch (error) {
      // 静默处理错误
    }

    // 清除页面缓存
    const newCache = { ...this.data.semesterDataCache };
    delete newCache[cacheKey];
    this.setData({
      semesterDataCache: newCache,
      hasRetried: false
    });

    // 重新加载数据
    this.loadGrades(() => {
      this.setData({ isRefreshing: false });
    });
  },

  // 显示缓存状态（调试用）
  showCacheStatus() {
    const app = getApp();
    const globalCache = app.globalData.gradeCache || {};
    const localCache = wx.getStorageSync('gradeCache') || {};

    console.log('[缓存状态] 全局缓存:', globalCache);
    console.log('[缓存状态] 本地缓存:', localCache);

    wx.showModal({
      title: '缓存状态',
      content: `全局缓存: ${Object.keys(globalCache).length}项\n本地缓存: ${Object.keys(localCache).length}项`,
      showCancel: false
    });
  },

  // 清理异常缓存（调试用）
  cleanInvalidCache() {
    const app = getApp();
    const result = app.cleanExpiredCache(app.globalData.gradeCache || {});
    app.globalData.gradeCache = result;

    try {
      wx.setStorageSync('gradeCache', result);
      console.log('[清理缓存] 异常缓存清理完成');
      wx.showToast({
        title: '缓存已清理',
        icon: 'success'
      });
    } catch (error) {
      console.error('[清理缓存] 清理失败:', error);
      wx.showToast({
        title: '清理失败',
        icon: 'error'
      });
    }
  },

  // 静默更新默认学期及前后学期
  silentUpdateDefaultAndNearbySemesters() {
    const defaultYear = this.data.defaultYear;
    const defaultSemester = this.data.defaultSemester;

    // 计算需要更新的学期列表（只检查3个学期）
    const semestersToUpdate = [];

    // 添加前一个学期
    const prevSemester = this.getPreviousSemester(defaultYear, defaultSemester, 1);
    semestersToUpdate.push(prevSemester);

    // 添加默认学期
    semestersToUpdate.push({ year: defaultYear, semester: defaultSemester });

    // 添加后一个学期
    const nextSemester = this.getNextSemester(defaultYear, defaultSemester, 1);
    semestersToUpdate.push(nextSemester);

    // 静默更新每个学期
    semestersToUpdate.forEach((sem, index) => {
      setTimeout(() => {
        this.silentUpdateSemester(sem.year, sem.semester);
      }, index * 600); // 每个学期间隔600ms，避免服务器压力
    });
  },

  // 静默更新单个学期
  silentUpdateSemester(year, semester) {
    const cacheKey = `${year}-${semester}`;

    // 检查是否有本地缓存
    let hasLocalCache = false;
    try {
      const localGradeCache = wx.getStorageSync('gradeCache') || {};
      hasLocalCache = !!localGradeCache[cacheKey];
    } catch (error) {
      return; // 如果读取缓存失败，跳过更新
    }

    // 如果没有本地缓存，跳过更新（避免不必要的请求）
    if (!hasLocalCache) {
      return;
    }

    const ssoUsername = wx.getStorageSync('sso_username');
    const ssoPassword = wx.getStorageSync('sso_password');

    if (!ssoUsername || !ssoPassword) {
      return;
    }

    // 静默请求最新数据
    wx.request({
      url: getApp().globalData.wangz + '/BuaaProxy/getScoreOneStep',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: year,
        xq: semester
      },
      success: (res) => {
        this.handleSilentUpdateResponse(res.data, year, semester);
      },
      fail: (error) => {
        // 静默失败，不显示错误信息
      }
    });
  },

  // 处理静默更新响应
  handleSilentUpdateResponse(data, year, semester) {
    if (data && data.error_code === 0) {
      const scores = data.data?.d || [];

      // 计算总学分
      let totalCredits = 0;
      scores.forEach(score => {
        const credit = parseFloat(score.xf) || 0;
        totalCredits += credit;
      });

      // 提取GPA信息
      let gpa = '0.00';
      if (data.user_info && data.user_info.gpa) {
        gpa = parseFloat(data.user_info.gpa).toFixed(2);
      }

      const newGradeData = {
        gradeList: scores,
        totalCredits: totalCredits.toFixed(1),
        userGPA: gpa
      };

      const cacheKey = `${year}-${semester}`;

      // 检查数据是否有变化
      let hasChanged = false;
      try {
        const localGradeCache = wx.getStorageSync('gradeCache') || {};
        const cachedData = localGradeCache[cacheKey];

        if (cachedData) {
          // 比较关键数据是否有变化
          const oldScoresStr = JSON.stringify(cachedData.gradeList || []);
          const newScoresStr = JSON.stringify(scores);
          const oldGPA = cachedData.userGPA || '0.00';
          const oldCredits = cachedData.totalCredits || '0.0';

          hasChanged = (oldScoresStr !== newScoresStr) ||
                      (oldGPA !== gpa) ||
                      (oldCredits !== totalCredits.toFixed(1));
        }
      } catch (error) {
        // 如果比较失败，假设有变化
        hasChanged = true;
      }

      // 如果数据有变化，更新缓存
      if (hasChanged) {
        // 更新本地存储缓存
        try {
          const localGradeCache = wx.getStorageSync('gradeCache') || {};
          localGradeCache[cacheKey] = newGradeData;
          wx.setStorageSync('gradeCache', localGradeCache);
        } catch (error) {
          // 静默处理错误
        }

        // 如果是当前显示的学期，更新页面显示
        if (year === this.data.currentYear && semester === this.data.currentSemester) {
          this.setData({
            gradeList: scores,
            totalCredits: totalCredits.toFixed(1),
            userGPA: gpa
          });

          // 更新页面缓存
          const newCache = { ...this.data.semesterDataCache };
          newCache[cacheKey] = newGradeData;
          this.setData({
            semesterDataCache: newCache
          });
        }
      }
    }
  }
});
