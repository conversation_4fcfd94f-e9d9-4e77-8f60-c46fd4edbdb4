/* pages/grade/grade.wxss */
page {
  background-color: rgb(244, 244, 249); /* 与navbar保持一致的背景色 */
}

.container {
  padding: 20rpx 0 120rpx 0; /* 顶部添加与navbar的距离，底部添加安全间距 */
}

/* 学期导航器样式 */
.semester-navigator {
  width: 90%; /* 组件宽度设为90% */
  background: white;
  border-radius: 16rpx; /* 恢复圆角，因为不再完全贴边 */
  padding: 24rpx 0; /* 上下内边距，左右完全贴边 */
  margin: 0 auto; /* 水平居中 */
  margin-bottom: 20rpx; /* 只保留与下方组件的间距 */
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx; /* 为导航器内容添加左右内边距 */
}

.nav-arrow {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.nav-arrow:active {
  opacity: 1;
}

.nav-arrow.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.left-arrow {
  transform: rotate(0deg);
}

.right-arrow {
  transform: rotate(0deg);
}

.semester-display {
  flex: 1;
  text-align: center;
  padding: 0 40rpx;
  height: 60rpx; /* 固定高度，防止加载时变化 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.semester-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

/* 成绩列表样式 */
.grade-list {
  width: 90%; /* 组件宽度设为90% */
  background: white;
  border-radius: 16rpx; /* 恢复圆角，因为不再完全贴边 */
  overflow: hidden;
  margin: 0 auto; /* 水平居中 */
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.semester-header {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 24rpx 20rpx; /* 为头部内容添加左右内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e1f5fe;
}

.semester-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1976d2;
}

.total-credits {
  font-size: 26rpx;
  color: #666;
}

.grade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0; /* 左右贴合container边缘，只保留上下内边距 */
  border-bottom: 1rpx solid #f0f1f2;
}

.grade-item:last-child {
  border-bottom: none;
}

.course-info {
  flex: 1;
  padding-left: 20rpx; /* 为课程信息添加左内边距 */
}

.course-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.course-type {
  font-size: 24rpx;
  color: #666;
}

.grade-info {
  text-align: right;
  padding-right: 20rpx; /* 为成绩信息添加右内边距 */
}

.grade-score {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.grade-score.pass {
  color: #52c41a;
}

.grade-score.fail {
  color: #ff4d4f;
}

.grade-credit {
  font-size: 24rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state, .initial-state {
  width: 90%; /* 组件宽度设为90% */
  text-align: center;
  padding: 100rpx 20rpx; /* 减少左右内边距，但保留一些以确保可读性 */
  margin: 0 auto; /* 水平居中 */
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.empty-icon, .initial-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text, .initial-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.empty-hint, .initial-hint {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 加载状态样式 */
.loading-state {
  width: 90%; /* 组件宽度设为90% */
  text-align: center;
  padding: 100rpx 20rpx; /* 减少左右内边距，但保留一些以确保可读性 */
  margin: 0 auto; /* 水平居中 */
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
