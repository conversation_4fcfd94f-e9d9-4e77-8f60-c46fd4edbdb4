<!--pages/grade/grade.wxml-->
<custom-nav-bar title="成绩查询" showBack="{{true}}"></custom-nav-bar>

<scroll-view class="container" scroll-y="true" refresher-enabled="true" refresher-triggered="{{isRefreshing}}" bindrefresherrefresh="onPullDownRefresh">
  <!-- 学期导航器 -->
  <view class="semester-navigator">
    <view class="nav-container">
      <image src="/images/zuojiantou.png" class="nav-arrow left-arrow {{isLoading || isAtMinSemester ? 'disabled' : ''}}" bindtap="previousSemester"></image>
      <view class="semester-display">
        <text class="semester-text">{{currentSemesterDisplay}}</text>
      </view>
      <image src="/images/youjiantou-3.png" class="nav-arrow right-arrow {{isLoading || isAtMaxSemester ? 'disabled' : ''}}" bindtap="nextSemester"></image>
    </view>
  </view>

  <!-- 成绩列表 -->
  <view class="grade-list" wx:if="{{gradeList.length > 0}}">
    <view class="semester-header">
      <text class="semester-title">GPA: {{userGPA || '0.00'}}</text>
      <text class="total-credits">总学分: {{totalCredits}}</text>
    </view>
    
    <view class="grade-item" wx:for="{{gradeList}}" wx:key="index">
      <view class="course-info">
        <view class="course-name">{{item.kcmc}}</view>
        <view class="course-type">{{item.kclx}} | {{item.fslx}}</view>
      </view>
      
      <view class="grade-info">
        <view class="grade-score {{item.kccj === '不及格' || item.kccj < 60 ? 'fail' : 'pass'}}">
          {{item.kccj}}
        </view>
        <view class="grade-credit">{{item.xf}}学分</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && gradeList.length === 0 && hasQueried}}">
    <image src="/images/wenhao.png" class="empty-icon"></image>
    <text class="empty-text">暂无成绩数据</text>
    <text class="empty-hint">请切换其他学期查询</text>
  </view>

  <!-- 初始状态 -->
  <view class="initial-state" wx:if="{{!hasQueried && !isLoading}}">
    <image src="/images/kebiao.png" class="initial-icon"></image>
    <text class="initial-text">切换学期查询成绩</text>
    <text class="initial-hint">默认查询当前学期成绩</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
  </view>
</scroll-view>
