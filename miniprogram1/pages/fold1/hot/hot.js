// pages/fold1/hot/hot.js
const app = getApp();

Page({
  data: {
    messages: [], // 存储热门消息数据
    page: 1,
    pageSize: 10, // 每页获取的帖子数量，默认为10
    loading: true, // 是否正在加载数据
    isLoading: false, // 是否正在请求中
    abc: "255, 255, 255", // 背景色改为纯白色
  },

  onLoad(options) {
    // 如果有传入pageSize参数，则使用传入的值
    if (options && options.pageSize) {
      this.setData({
        pageSize: parseInt(options.pageSize) || 10
      });
    }
    this.loadHotMessages();
  },

  loadHotMessages(callback) {
    if (this.data.isLoading) return;

    this.setData({ isLoading: true });

    // 如果是第一页，清空现有消息列表
    if (this.data.page === 1) {
      this.setData({ messages: [] });
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/getHotMessages',
      method: 'POST',
      data: {
        page: this.data.page,
        limit: this.data.pageSize, // 传递页面大小参数
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];
        
        newMessages = newMessages.map(item => {
          // 使用imageUtil处理图片数组和头像
          const imageUtil = require('../../../utils/imageUtil.js');
          if (item.images) {
            item.images = imageUtil.processImageArray(item.images);
          } else {
            item.images = [];
          }

          // 处理头像路径
          if (item.face_url) {
            item.face_url = imageUtil.processImageUrl(item.face_url);
          }

          for (let key in item) {
            if (item[key] === null) {
              item[key] = '';
            }
          }

          return item;
        });

        const newPage = this.data.page + 1;
        this.setData({
          messages: [...this.data.messages, ...newMessages],
          page: newPage,
          isLoading: false,
          loading: false
        });
        
        // 输出日志以便调试
        console.log(`加载完成，当前页数: ${this.data.page - 1}, 消息数量: ${this.data.messages.length}`);
        
        if (callback) callback();
      },
      fail: (error) => {
        console.error("热门消息加载失败:", error);
        this.setData({ 
          isLoading: false,
          loading: false
        });
        wx.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
        if (callback) callback();
      },
    });
  },

  viewMessageDetail(e) {
    const index = e.currentTarget.dataset.index;
    const messageId = this.data.messages[index].id;
    wx.navigateTo({
      url: `/packageEmoji/pages/messageDetail/messageDetail?id=${messageId}`
    });
  },

  stopPropagation(e) {
    // 阻止事件冒泡
  },
  
  scrollToTop() {
    this.setData({
      page: 1,
      messages: [],
      loading: true
    });
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
    this.loadHotMessages();
  },

  dolike(e) {
    const that = this;
    const userId = getApp().globalData.user_id;
    const messageId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/like/doLike',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        message_id: messageId
      },
      success(res) {
        if (res.data.error_code === 0) {
          // 更新本地点赞状态
          const messages = that.data.messages;
          messages[index].is_liked = res.data.data.is_liked;
          messages[index].total_likes = res.data.data.total_likes;

          that.setData({
            messages: messages
          });

          // 发送点赞状态更新事件
          const app = getApp();
          if (app.globalData.websocket) {
            app.globalData.websocket.sendLike(
              messageId,
              'message',
              res.data.data.is_liked ? 'like' : 'unlike',
              messages[index].user_id
            ).catch(error => {
              // 移除console.error调用
            });
          }

          // 通知其他页面点赞状态已更改
          const eventBus = getApp().globalData.eventBus;
          if (eventBus) {
            eventBus.emit('likeStatusChanged', {
              messageId: messageId,
              isLiked: res.data.data.is_liked,
              totalLikes: res.data.data.total_likes
            });
          }

          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          });
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          });
        }
      },
      fail() {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  handleLongPress(e) {
    const { id, userId } = e.detail;
    const currentUserId = wx.getStorageSync('user_id');
    
    if (!currentUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否是自己的帖子
    if (currentUserId == userId) {
      wx.showActionSheet({
        itemList: ['删除'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.deleteMessage(id);
          }
        }
      });
    }
  },
  
  deleteMessage(messageId) {
    const userId = wx.getStorageSync('user_id');
    
    wx.showLoading({
      title: '正在删除',
    });
    
    wx.request({
      url: getApp().globalData.wangz + '/message/softDelete',
      method: 'POST',
      data: {
        messageId: messageId,
        userId: userId,
        token: wx.getStorageSync('token')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.error_code === 0) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 重新加载消息列表
          this.setData({
            page: 1,
            messages: [],
            loading: true
          });
          this.loadHotMessages();
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      messages: [],
      loading: true
    });
    this.loadHotMessages(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 禁用触底加载更多功能
  onReachBottom() {
    // 取消触底加载更多功能
    // this.loadHotMessages();
  },

  onShareAppMessage() {
    return {
      title: '热门消息',
      path: '/pages/fold1/hot/hot'
    };
  }
}); 