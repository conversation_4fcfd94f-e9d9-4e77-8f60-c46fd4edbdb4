.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 60rpx;
}

/* 信息区域 */
.info-section, .action-section, .auth-section, .test-section {
  margin: 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.info-card {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.value.verified {
  color: #07c160;
  font-weight: bold;
}

.value.unverified {
  color: #fa5151;
  font-weight: bold;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  padding: 25rpx 40rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
  background-color: white;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
  background-color: #007aff;
  color: white;
}

.action-btn.success {
  background-color: #07c160;
  color: white;
}

.action-btn.danger {
  background-color: #fa5151;
  color: white;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 提示文本 */
.tip-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
  line-height: 1.5;
}
