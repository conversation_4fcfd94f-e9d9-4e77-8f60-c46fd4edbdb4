// 认证学校信息演示页面
const { verifiedSchoolManager } = require('../../utils/verifiedSchoolManager');

Page({
  data: {
    verifiedInfo: {
      isVerified: false,
      statusText: '未认证',
      schoolName: '',
      schoolShortName: '',
      district: '',
      logoUrl: '',
      campusName: ''
    }
  },

  onLoad() {
    this.updateVerifiedInfo();
  },

  onShow() {
    // 每次显示页面时更新认证信息
    this.updateVerifiedInfo();
  },

  // 更新认证学校信息
  updateVerifiedInfo() {
    const displayInfo = verifiedSchoolManager.getDisplayInfo();
    this.setData({
      verifiedInfo: displayInfo
    });
    console.log('认证学校信息:', displayInfo);
  },

  // 显示认证学校详细信息
  showSchoolInfo() {
    verifiedSchoolManager.showVerifiedSchoolInfo();
  },

  // 检查认证状态
  checkVerificationStatus() {
    const isVerified = verifiedSchoolManager.isSchoolVerified();
    const schoolName = verifiedSchoolManager.getVerifiedSchoolName();
    
    wx.showModal({
      title: '认证状态检查',
      content: `认证状态: ${isVerified ? '已认证' : '未认证'}\n${schoolName ? '认证学校: ' + schoolName : ''}`,
      showCancel: false
    });
  },

  // 获取认证学校ID
  getSchoolId() {
    const universityId = verifiedSchoolManager.getVerifiedUniversityId();
    wx.showToast({
      title: universityId ? `学校ID: ${universityId}` : '未获取到学校ID',
      icon: 'none'
    });
  },

  // 清除认证信息（仅用于测试）
  clearVerifiedInfo() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除认证学校信息吗？（仅用于测试）',
      success: (res) => {
        if (res.confirm) {
          verifiedSchoolManager.clearVerifiedSchoolInfo();
          this.updateVerifiedInfo();
          wx.showToast({
            title: '已清除认证信息',
            icon: 'success'
          });
        }
      }
    });
  },

  // 跳转到SSO认证
  goToSSO() {
    wx.navigateTo({
      url: '/pages/foldshare/sso/sso'
    });
  },

  // 跳转到邮箱认证
  goToEmailAuth() {
    wx.navigateTo({
      url: '/pages/foldshare/bindauth/bindauth'
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
