const app = getApp()
const { navigateBack } = require('../../../utils/navigation');

Page({
  data: {
    notifications: [],
    page: 1,
    pageSize: 20,
    loading: true,
    hasMore: true
  },

  onLoad() {
    this.loadNotifications()
  },

  onShow() {
    // 页面显示时更新未读消息数Badge
    if (app.globalData.websocket) {
      app.globalData.websocket.updateBadgeOnShow();
    }
  },

  onClickLeft() {
    navigateBack();
  },

  async loadNotifications(isRefresh = false) {
    if (this.data.loading && !isRefresh && this.data.page > 1) return
    
    if (isRefresh) {
      this.setData({
        page: 1,
        notifications: [],
        hasMore: true,
        loading: true
      })
    } else if (this.data.page === 1) {
      this.setData({ loading: true })
    }

    try {
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.wangz}/notification/getNotifications`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            page: this.data.page,
            page_size: this.data.pageSize
          },
          success: resolve,
          fail: reject
        })
      })

      if (res.data.code === 200) {
        const newNotifications = res.data.data || []
        
        this.setData({
          notifications: this.data.page === 1 ? newNotifications : [...this.data.notifications, ...newNotifications],
          page: this.data.page + 1,
          hasMore: newNotifications.length === this.data.pageSize,
          loading: false
        })

        // 标记通知为已读
        this.markNotificationsAsRead()
      } else {
        this.setData({ loading: false })
        wx.showToast({
          title: res.data.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('请求失败:', error);
      this.setData({ loading: false })
      wx.showToast({
        title: '网络错误',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 标记通知为已读
  markNotificationsAsRead() {
    wx.request({
      url: `${app.globalData.wangz}/notification/markAsRead`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        user_id: app.globalData.user_id
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新全局未读通知数
          app.globalData.unreadNotifications = 0;
          
          // 更新个人中心页面的通知数
          const pages = getCurrentPages();
          const mePage = pages.find(page => page.route === 'pages/fold3/me/me');
          if (mePage && typeof mePage.updateGridNotifications === 'function') {
            mePage.updateGridNotifications();
          }
        }
      }
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadNotifications()
    }
  },

  onPullDownRefresh() {
    this.loadNotifications(true)
    wx.stopPullDownRefresh()
  },

  // 点击通知项
  onNotificationClick(e) {
    const notification = e.currentTarget.dataset.notification
    
    if (notification.type === 'student_auth') {
      // 学生认证通知，跳转到认证审核页面（管理员功能）
      wx.navigateTo({
        url: `/pages/fold3/authReview/authReview?id=${notification.related_id}`
      })
    }
    // 可以根据不同类型的通知跳转到不同页面
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp * 1000)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前'
    } else {
      return date.getMonth() + 1 + '月' + date.getDate() + '日'
    }
  }
})
