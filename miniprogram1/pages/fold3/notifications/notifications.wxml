<custom-nav-bar title="消息通知"></custom-nav-bar>

<view class="container">
  <view class="notification-list">
    <block wx:if="{{loading}}">
      <view class="loading-container">
        <view class="loading-dots"></view>
      </view>
    </block>
    <block wx:elif="{{notifications.length > 0}}">
      <view class="notification-item {{item.highlighted ? 'highlighted' : ''}}"
            wx:for="{{notifications}}"
            wx:key="id"
            bindtap="onNotificationClick"
            data-notification="{{item}}">
        <view class="user-avatar">
          <image class="avatar" src="{{item.icon || '/images/xiaoxi.png'}}" mode="aspectFill"/>
        </view>
        <view class="message-content">
          <view class="user-name">系统通知</view>
          <view class="action-line">
            <view class="action-text">
              <text class="notification-title">{{item.title}}</text>
              <text class="time">{{item.created_at}}</text>
            </view>
          </view>
          <view class="notification-message" wx:if="{{item.message}}">
            <text>{{item.message}}</text>
          </view>
        </view>
        <view class="read-status" wx:if="{{!item.is_read}}"></view>
      </view>
    </block>
    <view wx:else class="empty-tip">
      <image src="/images/xiaoxi.png" mode="aspectFit" class="empty-image"/>
      <text>暂无消息通知~</text>
    </view>
  </view>
</view>
