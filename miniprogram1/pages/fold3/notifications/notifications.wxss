/* pages/fold3/notifications/notifications.wxss */
page {
  background-color: #ffffff;
}

.container {
  padding: 0;
  min-height: 100vh;
  background-color: #ffffff;
}

.notification-list {
  width: 100%;
  padding-top: 16rpx;
  background-color: #ffffff;
}

.notification-item {
  display: flex;
  background: #ffffff;
  padding: 32rpx 32rpx;
  position: relative;
}

.notification-item::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1px;
  background-color: #eee;
}

.notification-item:last-child::after {
  display: none;
}

.notification-item.highlighted {
  background-color: #f8f9fa;
}

.user-avatar {
  margin-right: 24rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.action-line {
  margin-bottom: 12rpx;
}

.action-text {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.notification-title {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  min-width: 0;
}

.time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.notification-message {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
  margin-top: 8rpx;
}

.notification-message text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.read-status {
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-dots {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-tip text {
  font-size: 28rpx;
  color: #999;
}
