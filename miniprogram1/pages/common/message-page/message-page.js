/**
 * 通用消息页面
 * 通过参数配置不同类型的消息列表
 */

const { getMessageConfig, handleMessageClick } = require('../../../utils/message-config')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    config: {},
    pageType: 'notifications'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从页面参数获取消息类型
    const type = options.type || 'notifications'
    const config = getMessageConfig(type)
    
    this.setData({
      config,
      pageType: type
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: config.title
    })
  },

  /**
   * 处理消息点击事件
   */
  onMessageClick(e) {
    const { message, type } = e.detail
    handleMessageClick(message, type)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      messageList.loadMessages(true)
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      messageList.onRefresh()
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      messageList.onLoadMore()
    }
  }
})
