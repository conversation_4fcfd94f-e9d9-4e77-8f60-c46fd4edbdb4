/**
 * 调试工具 - 统一管理调试日志
 * 只在开发环境（localhost）输出调试信息
 */

/**
 * 检查是否为开发环境
 */
function isDebugMode() {
  try {
    const app = getApp();
    return app.globalData.wangz && app.globalData.wangz.includes('localhost');
  } catch (e) {
    return false;
  }
}

/**
 * 调试日志函数
 * @param {string} module - 模块名称
 * @param {...any} args - 日志参数
 */
function debugLog(module, ...args) {
  if (isDebugMode()) {
    console.log(`[${module}]`, ...args);
  }
}

/**
 * 调试错误日志函数
 * @param {string} module - 模块名称
 * @param {...any} args - 日志参数
 */
function debugError(module, ...args) {
  if (isDebugMode()) {
    console.error(`[${module}]`, ...args);
  }
}

/**
 * 调试警告日志函数
 * @param {string} module - 模块名称
 * @param {...any} args - 日志参数
 */
function debugWarn(module, ...args) {
  if (isDebugMode()) {
    console.warn(`[${module}]`, ...args);
  }
}

/**
 * 调试信息日志函数
 * @param {string} module - 模块名称
 * @param {...any} args - 日志参数
 */
function debugInfo(module, ...args) {
  if (isDebugMode()) {
    console.info(`[${module}]`, ...args);
  }
}

module.exports = {
  isDebugMode,
  debugLog,
  debugError,
  debugWarn,
  debugInfo
}
