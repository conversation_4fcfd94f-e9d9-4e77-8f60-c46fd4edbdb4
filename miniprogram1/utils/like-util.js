/**
 * 统一点赞功能工具
 * 支持帖子、评论、回复等所有类型的点赞
 */

const { request } = require('./api.js')

/**
 * 点赞类型配置
 */
const LIKE_TYPES = {
  MESSAGE: 'message',      // 帖子点赞
  COMMENT: 'comment',      // 评论点赞  
  REPLY: 'reply',          // 回复点赞
  LIAORAN_COMMENT: 'liaoran_comment', // 了然几分评论点赞
  LIAORAN_REPLY: 'liaoran_reply'      // 了然几分回复点赞
}

/**
 * 统一点赞API地址
 */
const UNIFIED_LIKE_API = '/like/unifiedLike'

/**
 * 点赞成功提示文案
 */
const LIKE_SUCCESS_MESSAGES = {
  [LIKE_TYPES.MESSAGE]: {
    liked: '点赞成功(*´∀`)~♥',
    unliked: '取消点赞(⁰﹏⁰)'
  },
  [LIKE_TYPES.COMMENT]: {
    liked: '点赞评论成功(*´∀`)~♥',
    unliked: '取消点赞(⁰﹏⁰)'
  },
  [LIKE_TYPES.REPLY]: {
    liked: '点赞回复成功(*´∀`)~♥', 
    unliked: '取消点赞(⁰﹏⁰)'
  },
  [LIKE_TYPES.LIAORAN_COMMENT]: {
    liked: '点赞成功(*´∀`)~♥',
    unliked: '取消点赞(⁰﹏⁰)'
  },
  [LIKE_TYPES.LIAORAN_REPLY]: {
    liked: '点赞成功(*´∀`)~♥',
    unliked: '取消点赞(⁰﹏⁰)'
  }
}

/**
 * 统一点赞功能
 * @param {Object} options 点赞配置
 * @param {string} options.type 点赞类型
 * @param {number} options.targetId 目标ID
 * @param {number} options.messageId 帖子ID（可选）
 * @param {number} options.commentId 评论ID（可选）
 * @param {Function} options.onSuccess 成功回调
 * @param {Function} options.onError 错误回调
 * @returns {Promise}
 */
function doLike(options) {
  const {
    type,
    targetId,
    messageId,
    commentId,
    onSuccess,
    onError
  } = options

  // 验证参数
  if (!type || !targetId) {
    const error = new Error('点赞参数不完整')
    if (onError) onError(error)
    return Promise.reject(error)
  }

  // 检查登录状态
  const app = getApp()
  const userId = app.globalData.user_id
  if (!userId) {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    const error = new Error('用户未登录')
    if (onError) onError(error)
    return Promise.reject(error)
  }

  // 使用统一点赞API
  const apiUrl = UNIFIED_LIKE_API
  const requestData = buildUnifiedRequestData(type, targetId, messageId, commentId)

  // 发起点赞请求
  return request({
    url: apiUrl,
    data: requestData
  }).then(res => {
    // 处理成功响应
    const result = handleLikeSuccess(res, type)

    // 显示成功提示
    showLikeToast(type, result.isLiked)

    // 发送点赞状态更新事件
    emitLikeStatusChanged(type, targetId, result, messageId, commentId)

    // 执行成功回调
    if (onSuccess) onSuccess(result)

    return result
  }).catch(error => {
    // 处理错误
    handleLikeError(error)
    if (onError) onError(error)
    throw error
  })
}

/**
 * 构建统一请求数据
 */
function buildUnifiedRequestData(type, targetId, messageId, commentId) {
  const data = {
    type: type,
    target_id: targetId
  }

  // 只有当messageId有值时才添加到请求数据中
  if (messageId) {
    data.message_id = messageId
  }

  // 只有当commentId有值时才添加到请求数据中
  if (commentId) {
    data.comment_id = commentId
  }

  return data
}



/**
 * 处理点赞成功响应
 */
function handleLikeSuccess(res, type) {
  // 统一API返回格式
  const isLiked = res.data.is_liked
  const totalLikes = res.data.total_likes

  return {
    isLiked,
    totalLikes
  }
}

/**
 * 显示点赞提示
 */
function showLikeToast(type, isLiked) {
  const messages = LIKE_SUCCESS_MESSAGES[type]
  if (messages) {
    wx.showToast({
      title: isLiked ? messages.liked : messages.unliked,
      icon: 'none',
      duration: 750
    })
  }
}

/**
 * 发送点赞状态更新事件
 */
function emitLikeStatusChanged(type, targetId, result, messageId, commentId) {
  const app = getApp()
  const eventBus = app.globalData.eventBus
  
  if (eventBus) {
    // 发送通用点赞事件
    eventBus.emit('likeStatusChanged', {
      type,
      targetId,
      messageId,
      commentId,
      isLiked: result.isLiked,
      totalLikes: result.totalLikes
    })
    
    // 为了兼容现有代码，也发送特定类型的事件
    if (type === LIKE_TYPES.MESSAGE) {
      eventBus.emit('messageLikeChanged', {
        messageId: targetId,
        isLiked: result.isLiked,
        totalLikes: result.totalLikes
      })
    } else if (type === LIKE_TYPES.COMMENT) {
      eventBus.emit('commentLikeChanged', {
        commentId: targetId,
        messageId,
        isLiked: result.isLiked,
        totalLikes: result.totalLikes
      })
    }
  }
  
  // WebSocket通知已禁用
  // 如果需要实时通知功能，可以在这里重新启用
}

/**
 * 处理点赞错误
 */
function handleLikeError(error) {
  let message = '点赞失败'
  
  if (error.message) {
    message = error.message
  } else if (error.msg) {
    message = error.msg
  }
  
  wx.showToast({
    title: message,
    icon: 'none'
  })
}

/**
 * 快捷点赞方法 - 帖子点赞
 */
function likeMessage(messageId, onSuccess, onError) {
  return doLike({
    type: LIKE_TYPES.MESSAGE,
    targetId: messageId,
    onSuccess,
    onError
  })
}

/**
 * 快捷点赞方法 - 评论点赞
 */
function likeComment(commentId, messageId, onSuccess, onError) {
  return doLike({
    type: LIKE_TYPES.COMMENT,
    targetId: commentId,
    messageId,
    onSuccess,
    onError
  })
}

/**
 * 快捷点赞方法 - 回复点赞
 */
function likeReply(replyId, commentId, messageId, onSuccess, onError) {
  return doLike({
    type: LIKE_TYPES.REPLY,
    targetId: replyId,
    commentId,
    messageId,
    onSuccess,
    onError
  })
}

module.exports = {
  LIKE_TYPES,
  doLike,
  likeMessage,
  likeComment,
  likeReply
}
