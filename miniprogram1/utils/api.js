/**
 * API请求封装
 * 统一处理请求、响应、错误等
 */

const app = getApp()

/**
 * 基础请求方法
 * @param {Object} options 请求配置
 * @returns {Promise}
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 默认配置
    const defaultOptions = {
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      timeout: 10000
    }

    // 合并配置
    const finalOptions = {
      ...defaultOptions,
      ...options,
      header: {
        ...defaultOptions.header,
        ...options.header
      }
    }

    // 添加基础URL
    if (!finalOptions.url.startsWith('http')) {
      finalOptions.url = `${app.globalData.wangz}${finalOptions.url}`
    }

    // 发起请求
    wx.request({
      ...finalOptions,
      success: (res) => {
        if (res.statusCode === 200) {
          // 兼容不同的返回格式：code 或 error_code
          const errorCode = res.data.code !== undefined ? res.data.code : res.data.error_code

          if (errorCode === 200 || errorCode === 0) {
            resolve(res.data)
          } else {
            // 业务错误
            const error = new Error(res.data.msg || '请求失败')
            error.code = errorCode
            error.data = res.data
            reject(error)
          }
        } else {
          // HTTP错误
          const error = new Error(`HTTP ${res.statusCode}`)
          error.statusCode = res.statusCode
          reject(error)
        }
      },
      fail: (err) => {
        // 网络错误
        const error = new Error(err.errMsg || '网络错误')
        error.type = 'network'
        reject(error)
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * 消息相关API
 */
const messageAPI = {
  // 获取点赞列表
  getLikes: (page = 1, pageSize = 20) => {
    return post('/notification/getLikes', { page, page_size: pageSize })
  },

  // 获取评论回复列表
  getReplies: (page = 1, pageSize = 20) => {
    return post('/notification/getReplies', { page, page_size: pageSize })
  },

  // 获取系统通知列表
  getNotifications: (page = 1, pageSize = 20) => {
    return post('/notification/getNotifications', { page, page_size: pageSize })
  },

  // 获取我发布的内容
  getMyPublished: (page = 1, pageSize = 20) => {
    return post('/message/getMyPublished', { page, page_size: pageSize })
  },

  // 标记消息为已读
  markAsRead: (type, ids = []) => {
    return post('/notification/markAsRead', { type, ids })
  },

  // 标记所有消息为已读
  markAllAsRead: (type) => {
    return post('/notification/markAllAsRead', { type })
  }
}

/**
 * 错误处理
 */
function handleError(error, showToast = true) {
  console.error('API Error:', error)
  
  let message = '请求失败'
  
  if (error.type === 'network') {
    message = '网络连接失败，请检查网络'
  } else if (error.code === 401) {
    message = '登录已过期，请重新登录'
    // 可以在这里处理登录过期逻辑
  } else if (error.message) {
    message = error.message
  }

  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  return message
}

module.exports = {
  request,
  get,
  post,
  messageAPI,
  handleError
}
