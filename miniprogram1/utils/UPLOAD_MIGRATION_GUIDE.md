# 图片上传接口统一迁移指南

## 概述
为了提高代码维护性和数据一致性，我们已经统一了图片上传接口。所有图片上传都应该使用统一的COS上传接口。

## 已完成的修改

### 1. 后端修改
- ✅ 修改了 `treehole/app/controller/User.php` 中的 `updateFaceUrlByPhone` 方法
- ✅ 现在头像上传也存储相对路径到数据库，返回完整URL给前端
- ✅ 保持与 `/upload/uploadToCos` 接口的一致性

### 2. 前端工具
- ✅ 创建了 `miniprogram1/utils/uploadUtil.js` 统一上传工具
- ✅ 提供了多种便捷的上传函数

## 使用方法

### 1. 引入上传工具
```javascript
const uploadUtil = require('../../utils/uploadUtil.js');
```

### 2. 使用统一上传函数

#### 头像上传
```javascript
// 方法1：使用专用函数
uploadUtil.uploadAvatar(filePath)
  .then(result => {
    // result.key: 相对路径，用于数据库存储
    // result.url: 完整URL，用于前端显示
    console.log('头像上传成功:', result.url);
  })
  .catch(error => {
    console.error('头像上传失败:', error);
  });

// 方法2：使用通用函数
uploadUtil.uploadImage(filePath, 'avatar')
  .then(result => {
    console.log('头像上传成功:', result.url);
  });
```

#### 评论图片上传
```javascript
// 单张图片
uploadUtil.uploadCommentImages(filePath)
  .then(key => {
    console.log('图片上传成功，相对路径:', key);
  });

// 多张图片
uploadUtil.uploadCommentImages([filePath1, filePath2, filePath3])
  .then(keys => {
    console.log('批量上传成功，相对路径数组:', keys);
  });
```

#### 发帖图片上传
```javascript
uploadUtil.uploadPostImages(imageArray)
  .then(keys => {
    // keys 是相对路径数组，直接存储到数据库
    this.publishWithImages(keys);
  });
```

#### 活动封面上传
```javascript
uploadUtil.uploadActivityCover(filePath)
  .then(result => {
    console.log('封面上传成功:', result.url);
  });
```

### 3. 自定义上传选项
```javascript
uploadUtil.uploadImage(filePath, 'avatar', {
  showLoading: true,           // 是否显示加载提示
  loadingText: '头像上传中...' // 自定义加载文案
})
```

## 数据存储规范

### 数据库存储
- ✅ **统一存储相对路径**：`dev/avatar/2025/08/02/xxx.jpg`
- ❌ **不再存储完整URL**：`https://xxx.cos.xxx.com/dev/avatar/2025/08/02/xxx.jpg`

### 前端显示
- 使用 `imageUtil.js` 统一处理图片URL
- 自动根据环境拼接正确的域名

## 迁移步骤

### 已完成
1. ✅ 修改头像上传后端接口存储逻辑
2. ✅ 创建统一的前端上传工具
3. ✅ 更新调试系统集成

### 待完成（可选）
1. 🔄 将现有的头像上传前端代码改为使用 `uploadUtil.js`
2. 🔄 将其他图片上传代码统一使用 `uploadUtil.js`
3. 🔄 逐步废弃旧的上传接口

## 接口对比

### 旧方式（不推荐）
```javascript
// 头像上传 - 使用专用接口
wx.uploadFile({
  url: '/user/updateFaceUrlByPhone',
  name: 'face',
  formData: { phone: xxx }
});

// 其他图片 - 使用不同接口
wx.uploadFile({
  url: '/upload/uploadToCos',
  name: 'file',
  formData: { type: 'comment' }
});
```

### 新方式（推荐）
```javascript
// 所有图片上传都使用统一工具
uploadUtil.uploadAvatar(filePath);
uploadUtil.uploadCommentImages(filePaths);
uploadUtil.uploadPostImages(filePaths);
```

## 优势

1. **代码一致性**：所有上传逻辑统一
2. **维护简单**：只需维护一套上传代码
3. **错误处理统一**：统一的错误提示和处理
4. **数据一致性**：所有图片都存储相对路径
5. **调试方便**：统一的调试日志输出

## 注意事项

1. **向后兼容**：现有的头像上传接口仍然可用
2. **渐进迁移**：可以逐步将代码迁移到新的工具
3. **数据库一致性**：新上传的图片都会存储相对路径
4. **环境自动切换**：开发和生产环境自动使用不同域名

## 测试建议

1. 测试头像上传功能是否正常
2. 检查数据库中存储的是否为相对路径
3. 验证前端显示的是否为完整URL
4. 确认开发和生产环境都能正常工作
