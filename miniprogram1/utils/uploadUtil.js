/**
 * 统一图片上传工具
 * 所有图片上传都使用这个工具，确保一致性
 */

const { debugLog, debugError } = require('./debugUtil.js');

/**
 * 统一的图片上传函数
 * @param {string} filePath - 图片文件路径
 * @param {string} type - 图片类型 (avatar, comment, post, activity, life, qun, etc.)
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise，成功时resolve相对路径，失败时reject错误信息
 */
function uploadImage(filePath, type = 'common', options = {}) {
  return new Promise((resolve, reject) => {
    if (!filePath) {
      debugError('UploadUtil', '文件路径不能为空');
      reject('文件路径不能为空');
      return;
    }

    const app = getApp();
    const token = wx.getStorageSync('access_token');
    
    if (!token) {
      debugError('UploadUtil', '用户未登录，无法上传图片');
      reject('请先登录');
      return;
    }

    debugLog('UploadUtil', '开始上传图片:', { filePath, type });

    // 显示上传进度（可选）
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...'
      });
    }

    wx.uploadFile({
      url: app.globalData.wangz + '/upload/uploadToCos',
      filePath: filePath,
      name: 'file',
      header: {
        'token': token
      },
      formData: {
        'type': type
      },
      success(res) {
        if (options.showLoading !== false) {
          wx.hideLoading();
        }

        try {
          const data = JSON.parse(res.data);
          debugLog('UploadUtil', '上传响应:', data);

          if (data.code === 200) {
            debugLog('UploadUtil', '上传成功:', {
              type: type,
              key: data.data.key,
              url: data.data.url
            });
            
            // 返回相对路径用于数据库存储
            resolve({
              key: data.data.key,        // 相对路径，用于数据库存储
              url: data.data.url,        // 完整URL，用于前端显示
              storage_type: data.data.storage_type
            });
          } else {
            debugError('UploadUtil', '上传失败:', data);
            reject(data.msg || '上传失败');
          }
        } catch (e) {
          debugError('UploadUtil', 'JSON解析错误:', e);
          reject('服务器响应格式错误');
        }
      },
      fail(err) {
        if (options.showLoading !== false) {
          wx.hideLoading();
        }
        debugError('UploadUtil', '上传请求失败:', err);
        reject('网络错误，请重试');
      }
    });
  });
}

/**
 * 批量上传图片
 * @param {Array} filePaths - 图片文件路径数组
 * @param {string} type - 图片类型
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise，成功时resolve相对路径数组
 */
function uploadImages(filePaths, type = 'common', options = {}) {
  if (!Array.isArray(filePaths) || filePaths.length === 0) {
    return Promise.resolve([]);
  }

  debugLog('UploadUtil', '开始批量上传图片:', { count: filePaths.length, type });

  // 显示批量上传进度
  if (options.showLoading !== false) {
    wx.showLoading({
      title: options.loadingText || `上传中 0/${filePaths.length}`
    });
  }

  const uploadPromises = filePaths.map((filePath, index) => {
    return uploadImage(filePath, type, { showLoading: false })
      .then(result => {
        // 更新进度显示
        if (options.showLoading !== false) {
          wx.showLoading({
            title: `上传中 ${index + 1}/${filePaths.length}`
          });
        }
        return result.key; // 返回相对路径
      })
      .catch(error => {
        debugError('UploadUtil', `第${index + 1}张图片上传失败:`, error);
        return null; // 上传失败返回null
      });
  });

  return Promise.all(uploadPromises)
    .then(results => {
      if (options.showLoading !== false) {
        wx.hideLoading();
      }
      
      // 过滤掉上传失败的图片
      const validResults = results.filter(result => result !== null);
      
      debugLog('UploadUtil', '批量上传完成:', {
        total: filePaths.length,
        success: validResults.length,
        failed: filePaths.length - validResults.length
      });

      return validResults;
    })
    .catch(error => {
      if (options.showLoading !== false) {
        wx.hideLoading();
      }
      debugError('UploadUtil', '批量上传失败:', error);
      throw error;
    });
}

/**
 * 头像上传专用函数
 * @param {string} filePath - 头像文件路径
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise
 */
function uploadAvatar(filePath, options = {}) {
  return uploadImage(filePath, 'avatar', {
    loadingText: '头像上传中...',
    ...options
  });
}

/**
 * 评论图片上传专用函数
 * @param {Array|string} filePaths - 图片文件路径或路径数组
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise
 */
function uploadCommentImages(filePaths, options = {}) {
  if (typeof filePaths === 'string') {
    return uploadImage(filePaths, 'comment', options).then(result => result.key);
  }
  return uploadImages(filePaths, 'comment', options);
}

/**
 * 发帖图片上传专用函数
 * @param {Array|string} filePaths - 图片文件路径或路径数组
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise
 */
function uploadPostImages(filePaths, options = {}) {
  if (typeof filePaths === 'string') {
    return uploadImage(filePaths, 'post', options).then(result => result.key);
  }
  return uploadImages(filePaths, 'post', options);
}

/**
 * 活动封面上传专用函数
 * @param {string} filePath - 图片文件路径
 * @param {Object} options - 可选参数
 * @returns {Promise} 返回Promise
 */
function uploadActivityCover(filePath, options = {}) {
  return uploadImage(filePath, 'activity', {
    loadingText: '封面上传中...',
    ...options
  });
}

module.exports = {
  uploadImage,
  uploadImages,
  uploadAvatar,
  uploadCommentImages,
  uploadPostImages,
  uploadActivityCover
};
