/**
 * 消息页面配置
 * 统一管理不同类型消息页面的配置
 */

const messageConfigs = {
  // 点赞消息配置
  likes: {
    type: 'likes',
    title: '收到的赞',
    emptyConfig: {
      icon: '/images/aixin.png',
      text: '还没有收到点赞哦~'
    }
  },

  // 评论回复配置
  replies: {
    type: 'replies', 
    title: '评论我的',
    emptyConfig: {
      icon: '/images/liaotianzidonghuifu.png',
      text: '还没有收到评论和回复哦~'
    }
  },

  // 系统通知配置
  notifications: {
    type: 'notifications',
    title: '消息通知',
    emptyConfig: {
      icon: '/images/xiaoxi.png',
      text: '暂无消息通知~'
    }
  },

  // 我发布的配置
  published: {
    type: 'published',
    title: '我发布的',
    emptyConfig: {
      icon: '/images/dangshidati-01.png',
      text: '还没有发布内容哦~'
    }
  }
}

/**
 * 获取消息配置
 * @param {String} type 消息类型
 * @returns {Object} 配置对象
 */
function getMessageConfig(type) {
  return messageConfigs[type] || messageConfigs.notifications
}

/**
 * 处理消息点击事件
 * @param {Object} message 消息对象
 * @param {String} type 消息类型
 */
function handleMessageClick(message, type) {
  switch (type) {
    case 'likes':
      handleLikeClick(message)
      break
    case 'replies':
      handleReplyClick(message)
      break
    case 'notifications':
      handleNotificationClick(message)
      break
    case 'published':
      handlePublishedClick(message)
      break
    default:
      console.log('Unknown message type:', type)
  }
}

/**
 * 处理点赞消息点击
 */
function handleLikeClick(message) {
  // 跳转到原始内容
  const url = getContentUrl(message.target_type, message.target_id, message.message_id)
  if (url) {
    wx.navigateTo({ url })
  }
}

/**
 * 处理评论回复消息点击
 */
function handleReplyClick(message) {
  // 跳转到原始内容
  const url = getContentUrl('message', message.message_id)
  if (url) {
    wx.navigateTo({ url })
  }
}

/**
 * 处理系统通知点击
 */
function handleNotificationClick(message) {
  // 根据通知类型处理
  switch (message.target_type) {
    case 'student_auth':
      // 跳转到认证管理页面（管理员）
      wx.navigateTo({
        url: '/pages/admin/auth-management/auth-management'
      })
      break
    case 'auth_result':
      // 显示认证结果详情
      wx.showModal({
        title: '认证结果',
        content: message.message,
        showCancel: false
      })
      break
    default:
      // 默认显示通知内容
      if (message.message) {
        wx.showModal({
          title: message.title || '系统通知',
          content: message.message,
          showCancel: false
        })
      }
  }
}

/**
 * 处理发布内容点击
 */
function handlePublishedClick(message) {
  // 跳转到内容详情
  const url = `/pages/foldshare/detail/detail?id=${message.id}`
  wx.navigateTo({ url })
}

/**
 * 获取内容URL
 */
function getContentUrl(targetType, targetId, messageId) {
  const urlMap = {
    message: `/pages/foldshare/detail/detail?id=${messageId}`,
    comment: `/pages/foldshare/detail/detail?id=${messageId}`,
    reply: `/pages/foldshare/detail/detail?id=${messageId}`,
    liaoran_comment: `/pages/foldshare/boyadetail/boyadetail?id=${messageId}`,
    liaoran_reply: `/pages/foldshare/boyadetail/boyadetail?id=${messageId}`,
    window_comment: `/pages/fold3/map/map`,
    window_reply: `/pages/fold3/map/map`,
    major_comment: `/pages/foldshare/xueke/xueke`,
    major_reply: `/pages/foldshare/xueke/xueke`
  }
  
  return urlMap[targetType]
}

module.exports = {
  messageConfigs,
  getMessageConfig,
  handleMessageClick
}
