# 调试工具使用说明

## 概述
`debugUtil.js` 提供了统一的调试日志管理，只在开发环境（localhost）输出调试信息，生产环境自动关闭。

## 使用方法

### 1. 引入调试工具
```javascript
const { debugLog, debugError, debugWarn, debugInfo } = require('../../utils/debugUtil.js');
```

### 2. 使用调试函数
```javascript
// 普通日志
debugLog('ModuleName', '这是一条调试信息', data);

// 错误日志
debugError('ModuleName', '这是一条错误信息', error);

// 警告日志
debugWarn('ModuleName', '这是一条警告信息', warning);

// 信息日志
debugInfo('ModuleName', '这是一条信息日志', info);
```

### 3. 模块名称规范
- 页面：使用页面名称，如 'Home', 'Profile', 'MessageDetail'
- 工具类：使用工具名称，如 'ImageUtil', 'LoginManager', 'CosUtil'
- 组件：使用组件名称，如 'CommentInput', 'Navbar'

## 示例

### 在页面中使用
```javascript
// pages/home/<USER>
const { debugLog } = require('../../utils/debugUtil.js');

Page({
  onLoad() {
    debugLog('Home', '页面加载完成');
  },
  
  loadData() {
    debugLog('Home', '开始加载数据');
    // ... 数据加载逻辑
    debugLog('Home', '数据加载完成', data);
  }
});
```

### 在工具类中使用
```javascript
// utils/someUtil.js
const { debugLog, debugError } = require('./debugUtil.js');

function processData(data) {
  debugLog('SomeUtil', '开始处理数据:', data);
  
  try {
    // ... 处理逻辑
    debugLog('SomeUtil', '数据处理完成:', result);
    return result;
  } catch (error) {
    debugError('SomeUtil', '数据处理失败:', error);
    throw error;
  }
}
```

## 环境检测
- **开发环境**：`app.globalData.wangz` 包含 'localhost' 时启用调试
- **生产环境**：自动关闭所有调试输出

## 优势
1. **统一管理**：所有调试代码统一管理，便于维护
2. **自动切换**：根据环境自动开启/关闭调试
3. **性能优化**：生产环境零性能损耗
4. **模块化**：支持按模块分类调试信息
5. **类型丰富**：支持 log、error、warn、info 多种日志类型

## 注意事项
1. 第一个参数必须是模块名称
2. 生产环境部署前无需手动删除调试代码
3. 建议使用有意义的模块名称便于问题定位
