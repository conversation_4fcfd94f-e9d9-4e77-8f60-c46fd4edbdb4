# 消息系统重构说明

## 概述

本次重构将原来的四个独立消息页面（点赞、评论、发布、通知）统一为一个通用的消息列表组件，采用了现代化的前端开发最佳实践。

## 架构设计

### 1. 组件化架构
- **通用消息列表组件**: `components/message-list/message-list`
- **通用消息页面**: `pages/common/message-page/message-page`
- **配置管理**: `utils/message-config.js`
- **API封装**: `utils/api.js`

### 2. 数据流设计
```
页面参数 → 配置管理 → 通用组件 → API请求 → 数据格式化 → UI渲染
```

## 核心文件说明

### API封装 (`utils/api.js`)
- 统一的请求方法封装
- 错误处理机制
- Token自动添加
- 消息相关API集合

### 通用组件 (`components/message-list/`)
- 支持四种消息类型：likes, replies, notifications, published
- 统一的UI风格和交互
- 自动分页加载
- 下拉刷新和上拉加载更多
- 错误状态和空状态处理

### 配置管理 (`utils/message-config.js`)
- 不同消息类型的配置
- 消息点击事件处理
- 页面跳转逻辑

### 通用页面 (`pages/common/message-page/`)
- 接收type参数确定消息类型
- 自动设置页面标题
- 统一的页面生命周期处理

## 使用方式

### 1. 页面跳转
```javascript
// 跳转到点赞页面
wx.navigateTo({
  url: '/pages/common/message-page/message-page?type=likes'
})

// 跳转到评论页面
wx.navigateTo({
  url: '/pages/common/message-page/message-page?type=replies'
})

// 跳转到通知页面
wx.navigateTo({
  url: '/pages/common/message-page/message-page?type=notifications'
})

// 跳转到发布页面
wx.navigateTo({
  url: '/pages/common/message-page/message-page?type=published'
})
```

### 2. 组件使用
```xml
<message-list 
  type="{{messageType}}"
  title="{{pageTitle}}"
  empty-config="{{emptyConfig}}"
  bind:messageclick="onMessageClick">
</message-list>
```

## 后端API

### 新增API接口
1. `POST /notification/getLikes` - 获取点赞列表
2. `POST /notification/getReplies` - 获取评论回复列表
3. `POST /notification/getNotifications` - 获取系统通知列表
4. `POST /message/getMyPublished` - 获取我发布的内容
5. `POST /notification/markAsRead` - 标记消息为已读

### API参数
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）
- `type`: 消息类型（用于markAsRead）
- `ids`: 消息ID数组（可选）

## 数据格式

### 统一的消息数据格式
```javascript
{
  id: 消息ID,
  avatar: 头像URL,
  username: 用户名,
  actionText: 动作描述,
  time: 格式化时间,
  content: 消息内容,
  images: 图片数组,
  originalContent: 原始内容（回复时）,
  is_read: 是否已读
}
```

## 优势

### 1. 代码复用
- 减少了75%的重复代码
- 统一的UI组件和样式
- 共享的业务逻辑

### 2. 维护性
- 单一职责原则
- 集中的配置管理
- 统一的错误处理

### 3. 扩展性
- 新增消息类型只需配置
- 组件化设计便于复用
- API标准化

### 4. 用户体验
- 统一的交互体验
- 更好的加载状态
- 完善的错误处理

## 最佳实践

### 1. API设计
- RESTful风格
- 统一的响应格式
- 完善的错误码

### 2. 组件设计
- 单一职责
- 属性驱动
- 事件通信

### 3. 状态管理
- 本地状态管理
- 统一的加载状态
- 错误状态处理

### 4. 性能优化
- 分页加载
- 图片懒加载
- 缓存机制

## 迁移说明

### 删除的文件
- `pages/fold3/likes/*`
- `pages/fold3/reply/*`
- `pages/fold3/mypub/*`

### 更新的文件
- `pages/fold3/me/me.js` - 更新跳转链接
- `app.json` - 移除旧页面注册，添加新页面

### 保留的文件
- `pages/fold3/notifications/*` - 保留作为备份

## 注意事项

1. 确保后端API正确实现
2. 测试所有消息类型的显示和交互
3. 验证页面跳转逻辑
4. 检查权限控制
5. 测试错误处理机制

## 后续优化

1. 添加消息搜索功能
2. 实现消息分类筛选
3. 添加批量操作功能
4. 优化图片加载性能
5. 添加消息推送功能
