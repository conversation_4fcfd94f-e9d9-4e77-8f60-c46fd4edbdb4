page {
  background: linear-gradient(180deg, #f0f2f5 0%, #e8eaf0 100%);
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 固定导航栏 */
navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

/* 左侧标题样式 */
.left-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
  margin-left: 16rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  height: 32px;
  margin-top: 6rpx;
}

.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f2f5 0%, #e8eaf0 100%);
  padding-top: 88px;
  padding-left: 0;
  padding-right: 0;
}

/* 用户信息区域 */
.user-section {
  padding: 60rpx 32rpx 40rpx;
  background: transparent;
  margin-bottom: 0;
}

.user-info {
  display: flex;
  align-items: center;
  background-color: transparent;
}

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-right: 24rpx;
  flex-shrink: 0;
}

/* 用户详情 */
.user-details {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.username {
  font-size: 40rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-right: 20rpx;
}

.status-badges {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.badge {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}

.badge-teacher {
  background-color: #000;
  color: #fff;
}

.badge-student {
  background-color: #007aff;
  color: #fff;
}

.badge-gold-teacher {
  background-color: #FFD700;
  color: #8B4513;
  font-weight: 600;
}

.badge-verified {
  background-color: #000;
  color: #fff;
}

.badge-icon {
  width: 60rpx;
  height: 30rpx;
  margin-left: 16rpx;
}

.user-id {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 功能容器 */
.functions-container {
  background-color: #fff;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 功能区域 */
.function-section {
  padding: 32rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 16rpx;
}

.function-text {
  font-size: 24rpx;
  color: #666;
}



/* 菜单区域 */
.menu-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  margin-top: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 24rpx;
}

.menu-text {
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
}




