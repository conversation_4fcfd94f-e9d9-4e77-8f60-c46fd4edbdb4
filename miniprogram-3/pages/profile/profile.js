const config = require('../../config/config.js');
const { processAvatarUrl, processUserInfo } = require('../../utils/common.js');

Page({
  data: {
    userInfo: {
      id: '8888888',
      name: '何老师',
      avatar: '', // 初始化为空，在onLoad中设置
      isTeacher: true // true为老师，false为学生
    }
  },

  onLoad(options) {
    // 设置初始头像
    this.setData({
      'userInfo.avatar': processAvatarUrl()
    });
    this.loadUserInfo();
  },

  onShow() {
    // 每次显示页面时刷新用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    const token = wx.getStorageSync('access_token');

    if (token) {
      // 从后端获取最新用户信息
      wx.request({
        url: config.baseUrl + config.api.getUserInfo,
        method: 'POST',
        header: {
          'token': token,
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          console.log('获取用户信息响应:', res.data);

          if (res.data.code === 200) {
            const userInfo = res.data.data;

            // 更新本地存储
            wx.setStorageSync('user_info', userInfo);

            // 将后端字段映射到前端显示字段
            const displayUserInfo = {
              id: userInfo.id, // 后端已经格式化为6位数
              name: userInfo.nickname || userInfo.real_name || '未设置昵称',
              avatar: processAvatarUrl(userInfo.avatar),
              isTeacher: userInfo.is_teacher === 1,
              phone: userInfo.phone || '',
              real_name: userInfo.real_name || '',
              nickname: userInfo.nickname || ''
            };

            this.setData({
              userInfo: displayUserInfo
            });
          } else {
            console.error('获取用户信息失败:', res.data.msg);
            this.showDefaultUserInfo();
          }
        },
        fail: (err) => {
          console.error('请求用户信息失败:', err);
          // 如果请求失败，尝试使用本地存储的信息
          this.loadLocalUserInfo();
        }
      });
    } else {
      console.log('没有token，显示默认用户信息');
      this.showDefaultUserInfo();
    }
  },

  // 加载本地用户信息（作为备用方案）
  loadLocalUserInfo() {
    const userInfo = wx.getStorageSync('user_info');

    if (userInfo) {
      const displayUserInfo = {
        id: userInfo.id,
        name: userInfo.nickname || userInfo.real_name || '未设置昵称',
        avatar: processAvatarUrl(userInfo.avatar),
        isTeacher: userInfo.is_teacher === 1,
        phone: userInfo.phone || '',
        real_name: userInfo.real_name || '',
        nickname: userInfo.nickname || ''
      };

      this.setData({
        userInfo: displayUserInfo
      });
    } else {
      this.showDefaultUserInfo();
    }
  },

  // 显示默认用户信息
  showDefaultUserInfo() {
    this.setData({
      userInfo: {
        id: '000000',
        name: '未登录用户',
        avatar: processAvatarUrl(),
        isTeacher: false,
        phone: '',
        real_name: '',
        nickname: ''
      }
    });
  },

  // 更换头像
  changeAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 这里应该上传到服务器，现在先本地显示
        this.setData({
          'userInfo.avatar': tempFilePath
        });

        // 保存到本地存储
        const userInfo = this.data.userInfo;
        wx.setStorageSync('userInfo', userInfo);

        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 修改信息
  editProfile() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  }
})
