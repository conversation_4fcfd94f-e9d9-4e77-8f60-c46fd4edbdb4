// 测试页面
const config = require('../../config/config.js');

Page({
  data: {
    result: ''
  },

  onLoad() {
    this.testBackend();
  },

  // 测试后端连接
  testBackend() {
    console.log('测试后端连接...');
    
    wx.request({
      url: `${config.baseUrl}/treehole/public/test_banxuexing_login.php`,
      method: 'GET',
      success: (res) => {
        console.log('后端测试响应:', res);
        this.setData({
          result: JSON.stringify(res.data, null, 2)
        });
      },
      fail: (err) => {
        console.log('后端测试失败:', err);
        this.setData({
          result: '连接失败: ' + JSON.stringify(err, null, 2)
        });
      }
    });
  },

  // 测试自动登录
  testAutoLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('获取到微信code:', res.code);
          
          wx.request({
            url: `${config.baseUrl}/treehole/public${config.api.autoLogin}`,
            method: 'POST',
            data: {
              code: res.code
            },
            success: (res) => {
              console.log('自动登录测试响应:', res);
              this.setData({
                result: JSON.stringify(res.data, null, 2)
              });
            },
            fail: (err) => {
              console.log('自动登录测试失败:', err);
              this.setData({
                result: '自动登录失败: ' + JSON.stringify(err, null, 2)
              });
            }
          });
        }
      }
    });
  }
});
