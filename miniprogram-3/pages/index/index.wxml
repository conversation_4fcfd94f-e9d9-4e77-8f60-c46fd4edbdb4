<!--index.wxml-->
<navigation-bar title="伴学星" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 顶部筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item {{activeFilter === 'recommend' ? 'active' : ''}}" bindtap="setFilter" data-filter="recommend">推荐</view>
      <view class="filter-item {{activeFilter === 'latest' ? 'active' : ''}}" bindtap="setFilter" data-filter="latest">最新</view>
      <view class="filter-item {{activeFilter === 'nearby' ? 'active' : ''}}" bindtap="setFilter" data-filter="nearby">附近</view>
      <view class="filter-btn" bindtap="showFilterModal">
        <text>筛选</text>
        <image class="filter-icon" src="/images/fangdajing.png"></image>
      </view>
    </view>

    <!-- 老师信息列表 -->
    <view class="teacher-list">
      <view class="teacher-card" wx:for="{{teacherList}}" wx:key="id" bindtap="viewTeacherDetail" data-id="{{item.id}}">
        <!-- 关闭按钮 -->
        <view class="close-btn">×</view>

        <!-- 标签 -->
        <view class="tags">
          <text class="tag tag-red" wx:if="{{item.isUrgent}}">急聘</text>
          <text class="tag tag-green" wx:if="{{item.isRecruiting}}">招聘中</text>
        </view>

        <!-- 标题 -->
        <view class="title">{{item.title}}</view>

        <!-- 描述 -->
        <view class="description">{{item.description}}</view>

        <!-- 用户信息 -->
        <view class="user-info">
          <image class="avatar" src="{{item.avatar}}"></image>
          <text class="username">{{item.username}}</text>
          <view class="location">
            <text>距离{{item.distance}}km·{{item.area}}</text>
          </view>
        </view>

        <!-- 课程信息和价格 -->
        <view class="course-price">
          <view class="course-info">
            <text>{{item.subject}}/{{item.grade}}/{{item.time}}</text>
          </view>
          <view class="price-action">
            <view class="price">
              <text class="price-text">¥{{item.minPrice}} ~ ¥{{item.maxPrice}}</text>
              <text class="price-unit">/小时</text>
            </view>
            <button class="interest-btn" bindtap="showInterest" data-id="{{item.id}}">感兴趣</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

<!-- 浮动新增按钮 -->
<view class="add-btn-container">
  <view class="add-btn" bindtap="addTeacher">
    <view class="add-icon">+</view>
  </view>
</view>
