// index.js
const request = require('../../utils/request.js');
const config = require('../../config/config.js');
const { processTeacherList, processAvatarUrl } = require('../../utils/common.js');

Page({
  data: {
    activeFilter: 'recommend',
    teacherList: [],
    loading: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.loadTeacherList();
  },

  // 设置筛选条件
  setFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter
    });
    this.loadTeacherList();
  },

  // 显示筛选弹窗
  showFilterModal() {
    wx.showActionSheet({
      itemList: ['按距离排序', '按价格排序', '按评分排序'],
      success: (res) => {
        console.log('选择了筛选条件:', res.tapIndex);
      }
    });
  },

  // 加载老师列表
  loadTeacherList(refresh = true) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    const page = refresh ? 1 : this.data.page;

    request.post(config.api.getTeacherList, {
      filter: this.data.activeFilter,
      page: page,
      limit: 10
    }).then(res => {
      const newList = res.data.list || [];

      // 处理头像URL，拼接baseUrl
      const processedList = processTeacherList(newList);

      const teacherList = refresh ? processedList : [...this.data.teacherList, ...processedList];

      this.setData({
        teacherList: teacherList,
        page: page + 1,
        hasMore: newList.length >= 10,
        loading: false
      });
    }).catch(err => {
      console.error('加载老师列表失败:', err);
      this.setData({ loading: false });

      // 如果是第一次加载失败，显示模拟数据
      if (refresh && this.data.teacherList.length === 0) {
        this.setMockData();
      }
    });
  },

  // 设置模拟数据（用于开发测试）
  setMockData() {
    const mockData = [
      {
        id: 1,
        title: '高三数学提升',
        description: '昌平线六道口附近，高三女生请数学女生家教，基础不错，以拔高为主，辅导课后习题，规范写题...',
        username: '王女士·2333',
        avatar: processAvatarUrl(),
        distance: 19,
        area: '海淀区',
        subject: '高中/数学',
        grade: '女生',
        gender: '每周六',
        time: '日',
        minPrice: 200,
        maxPrice: 250,
        isUrgent: true,
        isRecruiting: true
      },
      {
        id: 2,
        title: '国家高中奥数竞赛冲刺辅导',
        description: '昌平线六道口附近，高三女生请数学女生家教，基础不错，以拔高为主，辅导课后习题，规范写题...',
        username: '王女士·2333',
        avatar: processAvatarUrl(),
        distance: 19,
        area: '海淀区',
        subject: '高中/数学',
        grade: '女生',
        gender: '每周六',
        time: '日',
        minPrice: 600,
        maxPrice: 950,
        isUrgent: false,
        isRecruiting: true
      }
    ];

    this.setData({
      teacherList: mockData
    });
  },

  // 查看老师详情
  viewTeacherDetail(e) {
    const teacherId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/teacher-detail/teacher-detail?id=${teacherId}`
    });
  },

  // 表示感兴趣
  showInterest(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const teacherId = e.currentTarget.dataset.id;

    request.post(config.api.showInterest, {
      teacher_id: teacherId
    }).then(res => {
      wx.showToast({
        title: '已表示感兴趣',
        icon: 'success'
      });
    }).catch(err => {
      console.error('表示感兴趣失败:', err);
    });
  },

  onPullDownRefresh() {
    this.loadTeacherList(true);
    wx.stopPullDownRefresh();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadTeacherList(false);
    }
  },

  // 添加老师信息
  addTeacher() {
    wx.navigateTo({
      url: '/pages/add-teacher/add-teacher'
    });
  }
})
