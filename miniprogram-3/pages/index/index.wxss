/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 32rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}

.filter-item {
  margin-right: 48rpx;
  font-size: 32rpx;
  color: #666;
  position: relative;
}

.filter-item.active {
  color: #333;
  font-weight: 600;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4285F4;
  border-radius: 2rpx;
}

.filter-btn {
  margin-left: auto;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}

/* 老师卡片样式 */
.teacher-list {
  padding-bottom: 40rpx;
}

.teacher-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.close-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ccc;
}

.tags {
  margin-bottom: 16rpx;
}

.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.tag-red {
  background-color: #ff4757;
  color: #fff;
}

.tag-green {
  background-color: #2ed573;
  color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.username {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}

.location {
  margin-left: auto;
  font-size: 24rpx;
  color: #999;
}

.course-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.course-info {
  font-size: 28rpx;
  color: #666;
}

.price-action {
  display: flex;
  align-items: center;
}

.price {
  margin-right: 24rpx;
}

.price-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4757;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
}

.interest-btn {
  background-color: #4285F4;
  color: #fff;
  border: none;
  border-radius: 48rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  line-height: 1;
}

/* 浮动新增按钮样式 */
.add-btn-container {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 999;
}

.add-btn {
  width: 120rpx;
  height: 120rpx;
  background-color: #4285F4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(66, 133, 244, 0.3);
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(66, 133, 244, 0.4);
}

.add-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
  line-height: 1;
}
