<!-- 顶部导航栏 -->
<navigation-bar back="{{true}}" color="#000000" background="#f5f7fa">
</navigation-bar>

<view class="container">
  <!-- 必填项提示 -->
  <view class="required-notice">
    <view class="notice-icon">!</view>
    <text class="notice-text">以下均为必填项</text>
  </view>

  <!-- 表单内容 -->
  <scroll-view class="form-content" scroll-y>
    <!-- 学历信息 -->
    <view class="form-step" wx:if="{{currentStep === 0}}">
      <view class="form-item">
        <text class="label required">学校 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <input class="input" placeholder="请输入真实学校名称" value="{{formData.school}}" bindinput="onInputChange" data-field="school" />
        </view>
      </view>

      <view class="form-item">
        <text class="label required">专业 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <input class="input" placeholder="请输入专业全称" value="{{formData.major}}" bindinput="onInputChange" data-field="major" />
        </view>
      </view>

      <view class="form-item">
        <text class="label required">年级 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <view class="input picker" bindtap="showGradePicker">
            <text class="{{formData.grade ? '' : 'placeholder'}}">{{formData.grade || '点击选择'}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label required">最高学历 <text class="required-star">*</text></text>
        <view class="education-options">
          <view class="option {{formData.education === '本科生' ? 'selected' : ''}}" bindtap="selectEducation" data-value="本科生">本科生</view>
          <view class="option {{formData.education === '研究生' ? 'selected' : ''}}" bindtap="selectEducation" data-value="研究生">研究生</view>
          <view class="option {{formData.education === '博士生' ? 'selected' : ''}}" bindtap="selectEducation" data-value="博士生">博士生</view>
        </view>
      </view>
    </view>

    <!-- 基本意向 -->
    <view class="form-step" wx:if="{{currentStep === 1}}">
      <view class="form-item">
        <text class="label required">期望薪资 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <view class="input picker" bindtap="showSalaryPicker">
            <text class="{{formData.expectedSalary ? '' : 'placeholder'}}">{{formData.expectedSalary || '点击选择'}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label required">可教学科 <text class="required-star">*</text></text>
        <view class="subject-tags">
          <view wx:for="{{subjectOptions}}" wx:key="id"
                class="subject-tag {{item.selected ? 'selected' : ''}}"
                bindtap="toggleSubject" data-index="{{index}}">
            {{item.name}}
          </view>
        </view>
        <view class="selected-subjects" wx:if="{{selectedSubjects.length > 0}}">
          <text class="selected-label">已选择：</text>
          <text class="selected-text">{{selectedSubjects.join('、')}}</text>
        </view>
      </view>

      <view class="form-item">
        <text class="label">教学标签</text>
        <view class="tag-section">
          <view class="tag-categories">
            <view class="tag-category">
              <text class="category-title">教学特色</text>
              <view class="tag-list">
                <view wx:for="{{teachingTags}}" wx:key="id"
                      class="teaching-tag {{item.selected ? 'selected' : ''}}"
                      bindtap="toggleTeachingTag" data-index="{{index}}">
                  {{item.name}}
                </view>
              </view>
            </view>

            <view class="tag-category">
              <text class="category-title">个人特点</text>
              <view class="tag-list">
                <view wx:for="{{personalTags}}" wx:key="id"
                      class="personal-tag {{item.selected ? 'selected' : ''}}"
                      bindtap="togglePersonalTag" data-index="{{index}}">
                  {{item.name}}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label">简介</text>
        <view class="input-wrapper">
          <textarea class="textarea" placeholder="简单介绍一下自己吧" value="{{formData.introduction}}" bindinput="onInputChange" data-field="introduction"></textarea>
        </view>
      </view>

      <view class="section-divider"></view>

      <view class="form-section">
        <text class="section-title">选填项</text>

        <view class="form-item">
          <text class="label">证书/荣誉</text>
          <view class="input-wrapper">
            <input class="input" placeholder="请输入" value="{{formData.certificates}}" bindinput="onInputChange" data-field="certificates" />
          </view>
        </view>

        <view class="form-item">
          <text class="label">可教年级</text>
          <view class="input-wrapper">
            <input class="input" placeholder="请输入" value="{{formData.teachableGrades}}" bindinput="onInputChange" data-field="teachableGrades" />
          </view>
        </view>
      </view>

      <view class="section-divider"></view>

      <view class="form-section">
        <text class="section-title">可约时间</text>
        <view class="time-selector">
          <view class="time-tabs">
            <view class="time-tab {{timeMode === 'week' ? 'active' : ''}}" bindtap="switchTimeMode" data-mode="week">周</view>
            <view class="time-tab {{timeMode === 'month' ? 'active' : ''}}" bindtap="switchTimeMode" data-mode="month">月</view>
          </view>

          <view class="week-schedule" wx:if="{{timeMode === 'week'}}">
            <view class="week-header">
              <text class="day-label">日</text>
              <text class="day-label">一</text>
              <text class="day-label">二</text>
              <text class="day-label">三</text>
              <text class="day-label">四</text>
              <text class="day-label">五</text>
              <text class="day-label">六</text>
            </view>

            <view class="time-slots">
              <view class="time-row">
                <text class="time-label">上午</text>
                <view class="slot-row">
                  <view class="time-slot {{schedule.morning[0] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="0"></view>
                  <view class="time-slot {{schedule.morning[1] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="1"></view>
                  <view class="time-slot {{schedule.morning[2] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="2"></view>
                  <view class="time-slot {{schedule.morning[3] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="3"></view>
                  <view class="time-slot {{schedule.morning[4] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="4"></view>
                  <view class="time-slot {{schedule.morning[5] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="5"></view>
                  <view class="time-slot {{schedule.morning[6] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="morning" data-day="6"></view>
                </view>
              </view>

              <view class="time-row">
                <text class="time-label">下午</text>
                <view class="slot-row">
                  <view class="time-slot {{schedule.afternoon[0] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="0"></view>
                  <view class="time-slot {{schedule.afternoon[1] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="1"></view>
                  <view class="time-slot {{schedule.afternoon[2] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="2"></view>
                  <view class="time-slot {{schedule.afternoon[3] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="3"></view>
                  <view class="time-slot {{schedule.afternoon[4] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="4"></view>
                  <view class="time-slot {{schedule.afternoon[5] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="5"></view>
                  <view class="time-slot {{schedule.afternoon[6] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="afternoon" data-day="6"></view>
                </view>
              </view>

              <view class="time-row">
                <text class="time-label">晚上</text>
                <view class="slot-row">
                  <view class="time-slot {{schedule.evening[0] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="0"></view>
                  <view class="time-slot {{schedule.evening[1] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="1"></view>
                  <view class="time-slot {{schedule.evening[2] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="2"></view>
                  <view class="time-slot {{schedule.evening[3] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="3"></view>
                  <view class="time-slot {{schedule.evening[4] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="4"></view>
                  <view class="time-slot {{schedule.evening[5] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="5"></view>
                  <view class="time-slot {{schedule.evening[6] ? 'selected' : ''}}" bindtap="toggleTimeSlot" data-period="evening" data-day="6"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-step" wx:if="{{currentStep === 2}}">
      <view class="form-item">
        <text class="label required">姓名 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <input class="input" placeholder="请输入真实姓名" value="{{formData.realName}}" bindinput="onInputChange" data-field="realName" />
        </view>
      </view>

      <view class="form-item">
        <text class="label required">性别 <text class="required-star">*</text></text>
        <view class="gender-options">
          <view class="gender-option {{formData.gender === '男' ? 'selected' : ''}}" bindtap="selectGender" data-value="男">
            <view class="radio {{formData.gender === '男' ? 'checked' : ''}}"></view>
            <text>男</text>
          </view>
          <view class="gender-option {{formData.gender === '女' ? 'selected' : ''}}" bindtap="selectGender" data-value="女">
            <view class="radio {{formData.gender === '女' ? 'checked' : ''}}"></view>
            <text>女</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label required">手机号 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <input class="input" placeholder="请填写您的手机号码" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone" type="number" />
        </view>
      </view>

      <view class="form-item">
        <text class="label required">所在城市 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <view class="input picker" bindtap="showCityPicker">
            <text class="{{formData.city ? '' : 'placeholder'}}">{{formData.city || '点击选择城市'}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label">出生年月</text>
        <view class="date-picker">
          <picker mode="date" value="{{formData.birthDate}}" bindchange="onDateChange">
            <view class="picker-content">
              <text class="date-part">{{birthYear || '年'}}</text>
              <text class="date-part">{{birthMonth || '月'}}</text>
              <text class="date-part">{{birthDay || '日'}}</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-item">
        <text class="label required">身份证号 <text class="required-star">*</text></text>
        <view class="input-wrapper">
          <input class="input" placeholder="请输入真实身份证号" value="{{formData.idCard}}" bindinput="onInputChange" data-field="idCard" />
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn" bindtap="submitForm">提交申请</button>
  </view>
</view>
