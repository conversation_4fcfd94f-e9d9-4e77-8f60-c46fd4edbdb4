Page({
  data: {
    currentStep: 0,
    timeMode: 'week',
    formData: {
      // 学历信息
      school: '',
      major: '',
      grade: '',
      education: '',

      // 基本意向
      expectedSalary: '',
      subjects: '',
      introduction: '',
      certificates: '',
      teachableGrades: '',

      // 基本信息
      realName: '',
      gender: '',
      phone: '',
      city: '',
      birthDate: '',
      idCard: ''
    },
    schedule: {
      morning: [false, false, false, false, false, false, false],
      afternoon: [false, false, false, false, false, false, false],
      evening: [false, false, false, false, false, false, false]
    },
    birthYear: '',
    birthMonth: '',
    birthDay: '',

    // 学科选项
    subjectOptions: [
      { id: 1, name: '数学', selected: false },
      { id: 2, name: '语文', selected: false },
      { id: 3, name: '英语', selected: false },
      { id: 4, name: '物理', selected: false },
      { id: 5, name: '化学', selected: false },
      { id: 6, name: '生物', selected: false },
      { id: 7, name: '历史', selected: false },
      { id: 8, name: '地理', selected: false },
      { id: 9, name: '政治', selected: false },
      { id: 10, name: '计算机', selected: false }
    ],
    selectedSubjects: [],

    // 教学特色标签
    teachingTags: [
      { id: 1, name: '因材施教', selected: false },
      { id: 2, name: '互动教学', selected: false },
      { id: 3, name: '趣味教学', selected: false },
      { id: 4, name: '严格要求', selected: false },
      { id: 5, name: '耐心细致', selected: false },
      { id: 6, name: '经验丰富', selected: false }
    ],

    // 个人特点标签
    personalTags: [
      { id: 1, name: '亲和力强', selected: false },
      { id: 2, name: '责任心强', selected: false },
      { id: 3, name: '沟通能力强', selected: false },
      { id: 4, name: '专业能力强', selected: false },
      { id: 5, name: '时间灵活', selected: false },
      { id: 6, name: '提分效果好', selected: false }
    ]
  },

  onLoad(options) {

  },

  // 切换步骤
  switchStep(e) {
    const step = parseInt(e.currentTarget.dataset.step);
    this.setData({
      currentStep: step
    });
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择学历
  selectEducation(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.education': value
    });
  },

  // 选择性别
  selectGender(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.gender': value
    });
  },

  // 显示年级选择器
  showGradePicker() {
    const grades = ['大一', '大二', '大三', '大四', '研一', '研二', '研三', '博一', '博二', '博三', '博四'];
    wx.showActionSheet({
      itemList: grades,
      success: (res) => {
        this.setData({
          'formData.grade': grades[res.tapIndex]
        });
      }
    });
  },

  // 显示薪资选择器
  showSalaryPicker() {
    const salaries = ['50-80元/小时', '80-120元/小时', '120-150元/小时', '150-200元/小时', '200元以上/小时'];
    wx.showActionSheet({
      itemList: salaries,
      success: (res) => {
        this.setData({
          'formData.expectedSalary': salaries[res.tapIndex]
        });
      }
    });
  },

  // 切换学科选择
  toggleSubject(e) {
    const index = e.currentTarget.dataset.index;
    const subjectOptions = this.data.subjectOptions;
    subjectOptions[index].selected = !subjectOptions[index].selected;

    // 更新选中的学科列表
    const selectedSubjects = subjectOptions.filter(item => item.selected).map(item => item.name);

    this.setData({
      subjectOptions: subjectOptions,
      selectedSubjects: selectedSubjects,
      'formData.subjects': selectedSubjects.join(',')
    });
  },

  // 切换教学标签
  toggleTeachingTag(e) {
    const index = e.currentTarget.dataset.index;
    const teachingTags = this.data.teachingTags;
    teachingTags[index].selected = !teachingTags[index].selected;

    this.setData({
      teachingTags: teachingTags
    });
  },

  // 切换个人标签
  togglePersonalTag(e) {
    const index = e.currentTarget.dataset.index;
    const personalTags = this.data.personalTags;
    personalTags[index].selected = !personalTags[index].selected;

    this.setData({
      personalTags: personalTags
    });
  },

  // 显示城市选择器
  showCityPicker() {
    const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市'];
    wx.showActionSheet({
      itemList: cities,
      success: (res) => {
        this.setData({
          'formData.city': cities[res.tapIndex]
        });
      }
    });
  },

  // 切换时间模式
  switchTimeMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      timeMode: mode
    });
  },

  // 切换时间段
  toggleTimeSlot(e) {
    const period = e.currentTarget.dataset.period;
    const day = parseInt(e.currentTarget.dataset.day);
    const currentValue = this.data.schedule[period][day];

    this.setData({
      [`schedule.${period}[${day}]`]: !currentValue
    });
  },

  // 日期选择
  onDateChange(e) {
    const date = e.detail.value;
    const dateObj = new Date(date);
    this.setData({
      'formData.birthDate': date,
      birthYear: dateObj.getFullYear() + '年',
      birthMonth: (dateObj.getMonth() + 1) + '月',
      birthDay: dateObj.getDate() + '日'
    });
  },

  // 提交表单
  submitForm() {
    const { formData, selectedSubjects, teachingTags, personalTags } = this.data;

    // 验证必填项
    const requiredFields = {
      school: '学校',
      major: '专业',
      grade: '年级',
      education: '最高学历',
      expectedSalary: '期望薪资',
      realName: '姓名',
      gender: '性别',
      phone: '手机号',
      city: '所在城市',
      idCard: '身份证号'
    };

    for (let field in requiredFields) {
      if (!formData[field]) {
        wx.showToast({
          title: `请填写${requiredFields[field]}`,
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }

    // 验证学科选择
    if (selectedSubjects.length === 0) {
      wx.showToast({
        title: '请至少选择一个可教学科',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证身份证号格式
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(formData.idCard)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 收集选中的标签
    const selectedTeachingTags = teachingTags.filter(tag => tag.selected).map(tag => tag.name);
    const selectedPersonalTags = personalTags.filter(tag => tag.selected).map(tag => tag.name);

    // 准备提交的数据
    const submitData = {
      ...formData,
      subjects: selectedSubjects.join(','),
      teachingTags: selectedTeachingTags.join(','),
      personalTags: selectedPersonalTags.join(','),
      schedule: this.data.schedule
    };

    wx.showLoading({
      title: '提交中...'
    });

    // 这里应该调用后端API提交数据
    console.log('提交的数据:', submitData);

    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '申请提交成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
})
