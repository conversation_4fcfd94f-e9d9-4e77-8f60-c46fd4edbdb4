page {
  background-color: #f5f7fa;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 固定导航栏 */
navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  position: relative;
  padding-top: 88px;
}

/* 必填项提示 */
.required-notice {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  margin: 0 32rpx 24rpx;
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.1);
}

.notice-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #f39c12;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.notice-text {
  font-size: 28rpx;
  color: #856404;
  font-weight: 500;
}

/* 必填项星号 */
.required-star {
  color: #e74c3c;
  font-weight: bold;
  margin-left: 8rpx;
}





/* 表单内容 */
.form-content {
  flex: 1;
  padding: 0 32rpx 40rpx;
  overflow-y: auto;
}

.form-step {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-item {
  margin-bottom: 48rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 24rpx;
  font-weight: 600;
}

.input-wrapper {
  border-bottom: 2rpx solid #f0f0f0;
  transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
  border-bottom-color: #333;
}

.input {
  width: 100%;
  padding: 24rpx 0;
  font-size: 32rpx;
  color: #333;
  border: none;
  background: transparent;
}

.input.picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.input.picker::after {
  content: '>';
  color: #ccc;
  font-size: 28rpx;
  transform: rotate(90deg);
}

.placeholder {
  color: #ccc;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  color: #333;
  border: none;
  background: transparent;
  resize: none;
}

/* 学历选项 */
.education-options {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}

.option {
  flex: 1;
  padding: 24rpx 20rpx;
  text-align: center;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.option.selected {
  background-color: #333;
  color: #fff;
  border-color: #333;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 学科标签选择 */
.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
}

.subject-tag {
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.subject-tag.selected {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.selected-subjects {
  margin-top: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #007bff;
}

.selected-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.selected-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 8rpx;
}

/* 标签分类区域 */
.tag-section {
  margin-top: 24rpx;
}

.tag-categories {
  background-color: #fafafa;
  border-radius: 16rpx;
  padding: 32rpx;
}

.tag-category {
  margin-bottom: 40rpx;
}

.tag-category:last-child {
  margin-bottom: 0;
}

.category-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #28a745;
  border-radius: 3rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.teaching-tag,
.personal-tag {
  padding: 14rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  background-color: #fff;
  color: #666;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.teaching-tag.selected {
  background-color: #28a745;
  color: #fff;
  border-color: #28a745;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
}

.personal-tag.selected {
  background-color: #ffc107;
  color: #212529;
  border-color: #ffc107;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
}

/* 性别选项 */
.gender-options {
  display: flex;
  gap: 80rpx;
  margin-top: 24rpx;
}

.gender-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  cursor: pointer;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.3s ease;
}

.gender-option:hover {
  background-color: #f8f9fa;
}

.radio {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #ccc;
  border-radius: 50%;
  position: relative;
  transition: border-color 0.3s ease;
}

.radio.checked {
  border-color: #333;
}

.radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 18rpx;
  height: 18rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 表单分组 */
.section-divider {
  height: 48rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin: 32rpx 0;
}

.form-section {
  margin-top: 48rpx;
}

.section-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  display: block;
  font-weight: 500;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background-color: #999;
  border-radius: 3rpx;
}

/* 时间选择器 */
.time-selector {
  margin-top: 48rpx;
}

.time-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 48rpx;
  width: 200rpx;
  margin-left: auto;
  margin-right: auto;
}

.time-tab {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: #333;
  color: #fff;
}

.week-schedule {
  background-color: #fafafa;
  border-radius: 16rpx;
  padding: 32rpx;
}

.week-header {
  display: flex;
  margin-bottom: 32rpx;
  padding-left: 120rpx;
}

.day-label {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.time-slots {
  /* 时间段容器 */
}

.time-row {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.time-row:last-child {
  margin-bottom: 0;
}

.time-label {
  width: 100rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.slot-row {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.time-slot {
  flex: 1;
  height: 48rpx;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
  background-color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.time-slot:hover {
  border-color: #ccc;
}

.time-slot.selected {
  background-color: #333;
  border-color: #333;
}

/* 日期选择器 */
.date-picker {
  margin-top: 24rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.picker-content:hover {
  border-bottom-color: #333;
}

.date-part {
  margin-right: 32rpx;
  font-size: 32rpx;
  color: #333;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  min-width: 80rpx;
  text-align: center;
}

.date-part:last-child {
  margin-right: 0;
}

/* 底部提交区域 */
.submit-section {
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background-color: #333;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #555;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.submit-btn:active {
  transform: translateY(0);
}
