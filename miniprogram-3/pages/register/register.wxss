page {
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 固定导航栏 */
navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.register-container {
  padding: 88px 32rpx 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 身份选择区域 */
.identity-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.identity-options {
  display: flex;
  gap: 24rpx;
}

.identity-item {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  border: 2rpx solid #e5e5e5;
  transition: all 0.3s ease;
}

.identity-item.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.identity-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 16rpx;
}

.identity-icon image {
  width: 100%;
  height: 100%;
}

.identity-text {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.identity-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
}

.input, .textarea {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #e5e5e5;
  box-sizing: border-box;
}

.textarea {
  height: 160rpx;
  padding: 24rpx;
  line-height: 1.5;
}

.input:focus, .textarea:focus {
  border-color: #667eea;
  background-color: #fff;
}

.picker-input {
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.picker-input.placeholder {
  color: #999;
}

/* 性别选择 */
.gender-options {
  display: flex;
  gap: 16rpx;
}

.gender-item {
  flex: 1;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  border: 1rpx solid #e5e5e5;
  transition: all 0.3s ease;
}

.gender-item.active {
  background-color: #667eea;
  color: #fff;
  border-color: #667eea;
}

/* 提交区域 */
.submit-section {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.submit-btn::after {
  border: none;
}

.submit-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}
