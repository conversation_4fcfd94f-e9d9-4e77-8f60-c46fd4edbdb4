// 全局变量
const app = getApp();
const config = require('../../config/config.js');

Page({
  data: {
    openid: '',
    sessionKey: '',
    isTeacher: false,
    submitLoading: false,
    
    // 基本信息
    formData: {
      nickname: '',
      real_name: '',
      phone: '',
      gender: 0,
      birth_date: '',
      city: ''
    },
    
    // 老师信息
    teacherInfo: {
      school: '',
      major: '',
      grade_level: '',
      gradeIndex: 0,
      education: '',
      educationIndex: 0,
      teachable_subjects: '',
      expected_salary: '',
      introduction: ''
    },
    
    // 选项数据
    gradeOptions: ['大一', '大二', '大三', '大四', '研一', '研二', '研三', '博士'],
    educationOptions: ['本科', '硕士', '博士', '其他']
  },

  onLoad(options) {
    if (options.openid) {
      this.setData({
        openid: options.openid,
        sessionKey: options.session_key || ''
      });
    }
    
    // 获取用户信息
    this.getUserProfile();
  },

  // 获取用户信息
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          'formData.nickname': res.userInfo.nickName,
          'formData.avatar': res.userInfo.avatarUrl,
          'formData.gender': res.userInfo.gender
        });
      },
      fail: () => {
        // 用户拒绝授权，使用默认值
        console.log('用户拒绝授权获取用户信息');
      }
    });
  },

  // 选择身份
  selectIdentity(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      isTeacher: type === 'teacher'
    });
  },

  // 输入基本信息
  onInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 输入老师信息
  onTeacherInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`teacherInfo.${field}`]: value
    });
  },

  // 选择性别
  selectGender(e) {
    const gender = parseInt(e.currentTarget.dataset.gender);
    this.setData({
      'formData.gender': gender
    });
  },

  // 选择出生日期
  onDateChange(e) {
    this.setData({
      'formData.birth_date': e.detail.value
    });
  },

  // 选择年级
  onGradeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'teacherInfo.gradeIndex': index,
      'teacherInfo.grade_level': this.data.gradeOptions[index]
    });
  },

  // 选择学历
  onEducationChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'teacherInfo.educationIndex': index,
      'teacherInfo.education': this.data.educationOptions[index]
    });
  },

  // 验证表单
  validateForm() {
    const { formData, isTeacher, teacherInfo } = this.data;
    
    // 验证基本信息
    if (!formData.nickname.trim()) {
      wx.showToast({ title: '请输入昵称', icon: 'error' });
      return false;
    }
    
    if (!formData.real_name.trim()) {
      wx.showToast({ title: '请输入真实姓名', icon: 'error' });
      return false;
    }
    
    if (!formData.phone.trim()) {
      wx.showToast({ title: '请输入手机号', icon: 'error' });
      return false;
    }
    
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({ title: '手机号格式不正确', icon: 'error' });
      return false;
    }
    
    return true;
  },

  // 提交注册
  submitRegister() {
    if (this.data.submitLoading) return;
    
    if (!this.validateForm()) return;
    
    this.setData({ submitLoading: true });
    
    const { formData, isTeacher, teacherInfo, openid } = this.data;
    
    // 构建请求数据
    const requestData = {
      openid: openid,
      nickname: formData.nickname,
      avatar: formData.avatar || '',
      phone: formData.phone,
      real_name: formData.real_name,
      gender: formData.gender,
      birth_date: formData.birth_date,
      city: formData.city,
      is_teacher: isTeacher ? 1 : 0
    };
    
    // 如果是老师，添加老师信息
    if (isTeacher) {
      requestData.teacher_info = {
        school: teacherInfo.school,
        major: teacherInfo.major,
        grade_level: teacherInfo.grade_level,
        education: teacherInfo.education,
        teachable_subjects: teacherInfo.teachable_subjects,
        expected_salary: teacherInfo.expected_salary,
        introduction: teacherInfo.introduction
      };
    }
    
    // 发送注册请求
    wx.request({
      url: `${app.globalData.apiUrl}${config.api.register}`,
      method: 'POST',
      data: requestData,
      success: (res) => {
        this.setData({ submitLoading: false });
        
        if (res.data.code === 200) {
          // 注册成功
          const data = res.data.data;
          
          // 保存token和用户信息
          wx.setStorageSync('access_token', data.token);
          wx.setStorageSync('user_info', data.user_info);
          
          wx.showToast({
            title: '注册成功',
            icon: 'success'
          });
          
          // 跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
          
        } else {
          wx.showToast({
            title: res.data.msg || '注册失败',
            icon: 'error'
          });
        }
      },
      fail: () => {
        this.setData({ submitLoading: false });
        wx.showToast({
          title: '网络错误',
          icon: 'error'
        });
      }
    });
  }
});
