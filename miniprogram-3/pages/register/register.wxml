<!-- 注册页面 -->
<navigation-bar title="完善信息" back="{{true}}" color="#000000" background="#f5f7fa">
</navigation-bar>

<view class="register-container">
  <!-- 身份选择 -->
  <view class="identity-section">
    <view class="section-title">选择身份</view>
    <view class="identity-options">
      <view class="identity-item {{isTeacher ? '' : 'active'}}" bindtap="selectIdentity" data-type="student">
        <view class="identity-icon">
          <image src="/images/weixiao.png" mode="aspectFit"></image>
        </view>
        <view class="identity-text">我是学生</view>
        <view class="identity-desc">寻找优秀老师</view>
      </view>
      
      <view class="identity-item {{isTeacher ? 'active' : ''}}" bindtap="selectIdentity" data-type="teacher">
        <view class="identity-icon">
          <image src="/images/wenda.png" mode="aspectFit"></image>
        </view>
        <view class="identity-text">我是老师</view>
        <view class="identity-desc">分享知识经验</view>
      </view>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>
    
    <view class="form-item">
      <view class="label">昵称 <text class="required">*</text></view>
      <input class="input" placeholder="请输入昵称" value="{{formData.nickname}}" bindinput="onInput" data-field="nickname" />
    </view>
    
    <view class="form-item">
      <view class="label">真实姓名 <text class="required">*</text></view>
      <input class="input" placeholder="请输入真实姓名" value="{{formData.real_name}}" bindinput="onInput" data-field="real_name" />
    </view>
    
    <view class="form-item">
      <view class="label">手机号 <text class="required">*</text></view>
      <input class="input" type="number" placeholder="请输入手机号" value="{{formData.phone}}" bindinput="onInput" data-field="phone" />
    </view>
    
    <view class="form-item">
      <view class="label">性别</view>
      <view class="gender-options">
        <view class="gender-item {{formData.gender === 1 ? 'active' : ''}}" bindtap="selectGender" data-gender="1">男</view>
        <view class="gender-item {{formData.gender === 2 ? 'active' : ''}}" bindtap="selectGender" data-gender="2">女</view>
      </view>
    </view>
    
    <view class="form-item">
      <view class="label">出生日期</view>
      <picker mode="date" value="{{formData.birth_date}}" bindchange="onDateChange">
        <view class="picker-input {{formData.birth_date ? '' : 'placeholder'}}">
          {{formData.birth_date || '请选择出生日期'}}
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="label">所在城市</view>
      <input class="input" placeholder="请输入所在城市" value="{{formData.city}}" bindinput="onInput" data-field="city" />
    </view>
  </view>

  <!-- 老师专属信息 -->
  <view class="form-section" wx:if="{{isTeacher}}">
    <view class="section-title">老师信息</view>
    
    <view class="form-item">
      <view class="label">学校</view>
      <input class="input" placeholder="请输入学校名称" value="{{teacherInfo.school}}" bindinput="onTeacherInput" data-field="school" />
    </view>
    
    <view class="form-item">
      <view class="label">专业</view>
      <input class="input" placeholder="请输入专业" value="{{teacherInfo.major}}" bindinput="onTeacherInput" data-field="major" />
    </view>
    
    <view class="form-item">
      <view class="label">年级</view>
      <picker range="{{gradeOptions}}" value="{{teacherInfo.gradeIndex}}" bindchange="onGradeChange">
        <view class="picker-input {{teacherInfo.grade_level ? '' : 'placeholder'}}">
          {{teacherInfo.grade_level || '请选择年级'}}
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="label">学历</view>
      <picker range="{{educationOptions}}" value="{{teacherInfo.educationIndex}}" bindchange="onEducationChange">
        <view class="picker-input {{teacherInfo.education ? '' : 'placeholder'}}">
          {{teacherInfo.education || '请选择学历'}}
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="label">可教学科</view>
      <input class="input" placeholder="如：数学、物理、英语" value="{{teacherInfo.teachable_subjects}}" bindinput="onTeacherInput" data-field="teachable_subjects" />
    </view>
    
    <view class="form-item">
      <view class="label">期望薪资</view>
      <input class="input" placeholder="如：100-200元/小时" value="{{teacherInfo.expected_salary}}" bindinput="onTeacherInput" data-field="expected_salary" />
    </view>
    
    <view class="form-item">
      <view class="label">个人简介</view>
      <textarea class="textarea" placeholder="请简单介绍一下自己的教学经验和特长" value="{{teacherInfo.introduction}}" bindinput="onTeacherInput" data-field="introduction"></textarea>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn" bindtap="submitRegister" loading="{{submitLoading}}">
      完成注册
    </button>
  </view>
</view>
