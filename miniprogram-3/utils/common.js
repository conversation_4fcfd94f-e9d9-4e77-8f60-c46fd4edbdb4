// 通用工具函数
const config = require('../config/config.js');

// 获取静态资源的baseUrl（不包含index.php）
function getStaticBaseUrl() {
  return config.baseUrl.replace('/index.php', '');
}

/**
 * 处理头像URL，如果是相对路径则拼接baseUrl
 * @param {string} avatarPath - 头像路径
 * @returns {string} - 完整的头像URL
 */
function processAvatarUrl(avatarPath) {
  if (!avatarPath) {
    return getStaticBaseUrl() + '/touxiang/1.png'; // 默认头像
  }

  if (avatarPath.startsWith('http')) {
    return avatarPath; // 已经是完整URL
  }

  return getStaticBaseUrl() + avatarPath; // 拼接静态资源baseUrl
}

/**
 * 处理用户信息对象，拼接头像URL
 * @param {object} userInfo - 用户信息对象
 * @returns {object} - 处理后的用户信息对象
 */
function processUserInfo(userInfo) {
  if (!userInfo) return userInfo;
  
  const processed = { ...userInfo };
  if (processed.avatar) {
    processed.avatar = processAvatarUrl(processed.avatar);
  }
  
  return processed;
}

/**
 * 处理老师列表数据，拼接头像URL
 * @param {array} teacherList - 老师列表
 * @returns {array} - 处理后的老师列表
 */
function processTeacherList(teacherList) {
  if (!Array.isArray(teacherList)) return teacherList;
  
  return teacherList.map(teacher => processUserInfo(teacher));
}

module.exports = {
  getStaticBaseUrl,
  processAvatarUrl,
  processUserInfo,
  processTeacherList
};
