const config = require('../config/config.js');

/**
 * 网络请求封装
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync('access_token');
    
    // 设置请求头
    const header = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header.token = token;
    }
    
    wx.request({
      url: config.baseUrl + options.url,
      method: options.method || 'POST',
      data: options.data || {},
      header: header,
      success: (res) => {
        if (res.statusCode === 200) {
          const data = res.data;
          if (data.error_code === 0) {
            resolve(data);
          } else {
            // 如果是token相关错误，清除本地token并跳转登录
            if (data.error_code === 2 && data.msg.includes('token')) {
              wx.removeStorageSync('access_token');
              wx.removeStorageSync('user_info');
              wx.reLaunch({
                url: '/pages/login/login'
              });
            } else {
              wx.showToast({
                title: data.msg || '请求失败',
                icon: 'none'
              });
            }
            reject(data);
          }
        } else {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
}

/**
 * GET请求
 */
function get(url, data = {}, header = {}) {
  return request({
    url: url,
    method: 'GET',
    data: data,
    header: header
  });
}

/**
 * POST请求
 */
function post(url, data = {}, header = {}) {
  return request({
    url: url,
    method: 'POST',
    data: data,
    header: header
  });
}

module.exports = {
  request,
  get,
  post
};
