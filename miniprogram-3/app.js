// app.js
const config = require('./config/config.js');

App({
  globalData: {
    apiUrl: config.baseUrl, // 使用config.js中的配置
    userInfo: null,
    token: null
  },

  onLaunch() {
    // 检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');

    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
    } else {
      // 未登录，尝试自动登录
      this.autoLogin();
    }
  },

  // 自动登录
  autoLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          this.handleAutoLogin(res.code);
        } else {
          console.log('获取微信登录code失败');
        }
      },
      fail: () => {
        console.log('微信登录失败');
      }
    });
  },

  // 处理自动登录
  handleAutoLogin(code) {
    console.log('开始自动登录，code:', code);
    console.log('API URL:', `${this.globalData.apiUrl}${config.api.autoLogin}`);

    wx.request({
      url: `${this.globalData.apiUrl}${config.api.autoLogin}`,
      method: 'POST',
      data: {
        code: code
      },
      success: (res) => {
        console.log('自动登录响应:', res);
        if (res.data && res.data.code === 200) {
          const data = res.data.data;

          // 保存token和用户信息
          wx.setStorageSync('access_token', data.token);
          wx.setStorageSync('user_info', data.user_info);

          this.globalData.token = data.token;
          this.globalData.userInfo = data.user_info;

          console.log('自动登录成功，用户信息:', data.user_info);
        } else {
          console.log('自动登录失败：', res.data ? res.data.msg : '未知错误');
        }
      },
      fail: (err) => {
        console.log('自动登录网络错误:', err);
      }
    });
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('user_info');
    this.globalData.token = null;
    this.globalData.userInfo = null;

    wx.redirectTo({
      url: '/pages/login/login'
    });
  }
})
